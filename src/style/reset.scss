#group-study-v2,
[class*='memo'],
.ion-page,
ion-modal {
  font-family: var(--memo-font-family);

  & button {
    border: none;
    font-family: inherit;
  }

  & input {
    color: inherit;
    font-family: inherit;
    user-select: auto;
  }
}

#group-study-v2,
#group-study-v2 *,
[class*='memo'] *,
ion-modal * {
  user-select: none;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

body {
  // iOS 上 Safari 可能会 “贴心” 地帮你调大一些字号
  // https://stackoverflow.com/questions/3226001/some-font-sizes-rendered-larger-on-safari-iphone
  -webkit-text-size-adjust: 100%;
}

ion-modal {
  touch-action: none;
  -webkit-tap-highlight-color: transparent;
}

ol,
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

*:focus-within {
  outline: none;
}
