.memo-picker {
  --height: fit-content;
  --border-radius: 10px 10px 0 0;
  --border-color: transparent;

  .picker-wrapper {
    contain: none;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: 10px;
      transform: translateX(-50%);
      height: 4px;
      width: 30px;
      border-radius: 8px;
      background: var(--normal-bgColor1);
      z-index: 9;
    }
  }

  .picker-toolbar {
    border-bottom: none;
  }

  .picker-above-highlight,
  .picker-below-highlight {
    border: none;
  }

  .picker-columns {
    width: 85%;
    margin: 0 auto;
  }

  .memo-picker-title {
    text-align: center;
    margin: 10px 0;
  }

  .memo-picker-prefixes {
    width: 100%;
    font-size: 12px;
    display: flex;
    align-items: center;
    color: var(--ion-color-secondary);

    & > * {
      flex: 1;
      text-align: center;
      padding: 5px 0;
    }
  }

  // Hack: 暂时写死
  // ionic 这部分的值是用 SCSS 变量计算的，无法直接用 CSS 变量替换
  .picker-columns::before {
    --picker-above-highlight-height: 81px;
    --picker-below-highlight-top: 115px;

    content: '';
    position: absolute;
    top: var(--picker-above-highlight-height);
    left: 50%;
    transform: translate3d(-50%, 0, 0);
    display: block;
    height: calc(var(--picker-below-highlight-top) - var(--picker-above-highlight-height));
    width: 100%;
    border-radius: 8px;
    background: var(--normal-bgColor);
  }

  .picker-prefix {
    display: none;
  }
}

:root.dark .memo-picker {
  .picker-above-highlight,
  .picker-below-highlight {
    --background-rgb: 39, 40, 41;
  }
}
