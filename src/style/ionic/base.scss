:root {
  --ion-font-family: var(--memo-font-family);

  /* Color */
  --ion-color-primary: var(--title-color);
  --ion-color-primary-rgb: 93, 93, 93;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #525252;
  --ion-color-primary-tint: #6d6d6d;

  --ion-color-secondary: var(--second-font-color);
  --ion-color-secondary-rgb: 142, 142, 142;
  --ion-color-secondary-contrast: #000000;
  --ion-color-secondary-contrast-rgb: 0, 0, 0;
  --ion-color-secondary-shade: #7d7d7d;
  --ion-color-secondary-tint: #999999;

  --ion-color-theme: var(--theme-green);
  --ion-color-theme-rgb: 54, 181, 157;
  --ion-color-theme-contrast: #fff;
  --ion-color-theme-contrast-rgb: 0, 0, 0;
  --ion-color-theme-shade: #309f8a;
  --ion-color-theme-tint: #4abca7;

  --ion-item-color: var(--ion-color-primary);
  --ion-text-color: var(--ion-color-primary);

  --ion-item-background: var(--bg-color-2);
  --ion-background-color: var(--bg-color-2);
  --ion-overlay-background-color: var(--bg-color-2);
}

.ion-color-theme {
  --ion-color-base: var(--ion-color-theme);
  --ion-color-base-rgb: var(--ion-color-theme-rgb);
  --ion-color-contrast: var(--ion-color-theme-contrast);
  --ion-color-contrast-rgb: var(--ion-color-theme-contrast-rgb);
  --ion-color-shade: var(--ion-color-theme-shade);
  --ion-color-tint: var(--ion-color-theme-tint);
}
