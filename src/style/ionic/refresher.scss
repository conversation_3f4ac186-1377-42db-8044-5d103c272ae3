// 根据 ionRefresher 的 progress 来更新 --spinner-rotate
// 以提供更丝滑的体验
.memo-refresher-content .refresher-pulling-icon ion-spinner {
  transform: rotate(var(--spinner-rotate));
}

.memo-refresher-content {
  display: flex;

  flex-direction: column;
  justify-content: center;

  height: 100%;
}

.refresher-pulling,
.refresher-refreshing {
  display: none;

  width: 100%;
}

.refresher-pulling-icon,
.refresher-refreshing-icon {
  transition: 200ms;
  transform-origin: center center;

  font-size: 30px;

  text-align: center;
}

.refresher-pulling .memo-refresher-content {
  .refresher-pulling {
    display: block;
  }
}

.refresher-ready .memo-refresher-content {
  .refresher-pulling {
    display: block;
  }

  .refresher-pulling-icon {
    transform: rotate(0);
  }
}

.refresher-refreshing .memo-refresher-content {
  .refresher-refreshing {
    display: block;
  }
}

.refresher-cancelling .memo-refresher-content {
  .refresher-pulling {
    display: block;
  }

  .refresher-pulling-icon {
    transform: scale(0);
  }
}

.refresher-completing .memo-refresher-content {
  .refresher-refreshing {
    display: block;
  }

  .refresher-refreshing-icon {
    transform: scale(0);
  }
}
