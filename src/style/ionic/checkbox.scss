ion-item {
  --inner-border-width: 0 0 0 0;
  --min-height: 1em;
  --padding-start: 0;
  --inner-padding-end: 0;
  --font-size: 1rem;
  --ion-font-family: var(--memo-font-family);
  font-size: 1rem;

  ion-label {
    margin: 0;
  }
}

ion-checkbox {
  --size: 18px;
  --background-checked: var(--ion-color-theme);
  --border-color-checked: var(--ion-color-theme);
  --checkmark-width: 2.5;

  margin: 0 0.5em 0 0;
}

.checkbox-container[single] ion-checkbox.checkbox-checked {
  pointer-events: none;
}
