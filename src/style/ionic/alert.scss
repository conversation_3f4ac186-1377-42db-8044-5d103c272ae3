.memo-alert {
  --max-width: 280px;
  --background: var(--bg-color-2);

  .alert-wrapper {
    margin-top: calc(-1 * var(--ion-safe-area-top, 0));
    border-radius: 8px;
  }

  .alert-head {
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 9px;
  }

  .alert-title {
    margin-top: 15px;
    font-size: 1rem;
  }

  &.warning-alert .alert-title,
  &.warning-message .alert-message {
    color: var(--red-bgColor);
  }

  .alert-message,
  .alert-sub-title {
    font-size: 15px;
    color: var(--second-font-color);
  }

  .alert-button {
    --ion-text-color-rgb: 0, 0, 0;

    font-size: 1rem;
    font-weight: normal !important;
    font-family: var(--ion-font-family);
  }
}

:root.dark .memo-alert {
  .alert-button {
    --ion-text-color-rgb: 255, 255, 255;
  }
}
