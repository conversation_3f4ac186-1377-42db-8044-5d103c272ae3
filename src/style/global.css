:root {
  --normal-bgColor: var(--bg-color-1);
  --normal-bgColor1: #aaaaaa;
  --theme-green: #36b59d;
  --yellow-bgColor: #eb9e27;
  --red-bgColor: #dc663e;
  --default-bgColor: #ffffff;
  --title-color: var(--font-color-1);
  --second-font-color: var(--font-color-2);
  --default-font-color: #aaa;
  --container-width: 95%;
  --name-color: #808080;
  --title-font-color: var(--bg-color-1);
  --green-color-1: #82c2ab;
  --disabled-color: #cccccc;
  --activity-opacity: 0.6;
  --memo-font-family:
    'Helvetica Neue', Verdana, 'Microsoft Yahei', 'Hiragino Sans GB', system-ui, -apple-system, sans-serif;

  /* 通用 */
  --white-level-10: #fff;

  --gray-level-10: #f7f7f7;
  --gray-level-20: #efefef;
  --gray-level-30: #e5e5e5;
  --gray-level-40: #aaa;
  --gray-level-50: #8e8e8e;
  --gray-level-60: #5d5d5d;
  --gray-level-70: #222324;
  --gray-level-80: #272829;
  --gray-level-90: #18191a;
  --gray-level-100: #4b4c4d;
  --gray-level-110: #1d1e1e;
  --gray-level-120: #1f2021;
  --gray-level-130: #303030;
  --gray-level-140: #717171;
  --gray-level-150: #a1a1a1;
  --gray-level-160: #dbdbdb;

  --green-normal: 54, 181, 157;
  --orange-normal: 235, 158, 39;
  --red-normal: 220, 102, 62;
  --underline-normal: 93, 93, 93;
  --green-blue: 130, 194, 171;

  --black-level-10: #000;

  /* 主题 */
  --font-color-1: var(--gray-level-60);
  --font-color-2: var(--gray-level-50);
  --font-color-3: var(--black-level-10);

  --bg-color-1: var(--gray-level-10);
  --bg-color-2: var(--white-level-10);

  --disabled-color: var(--gray-level-40);
  --border-color: var(--gray-level-30);
  --card-bg: var(--white-level-10);
  --button-bg: var(--gray-level-10);
  --button-ai-bg: var(--gray-level-30);

  --color-familiar: rgba(var(--green-normal), 1);
  --color-vague: rgba(var(--orange-normal), 1);
  --color-forget: rgba(var(--red-normal), 1);

  --color-contrast: var(--black-level-10);

  --color-card-green: rgba(var(--green-normal), 0.1);
  --color-card-orange: rgba(var(--orange-normal), 0.1);
  --color-card-red: rgba(var(--red-normal), 0.1);
  --color-card-green-blue: rgba(var(--green-blue), 0.1);
}

:root.dark {
  --theme-green: #1e957d;
  --normal-bgColor: rgba(var(--green-blue), 0.25);
  --font-color-1: var(--gray-level-160);
  --font-color-2: var(--gray-level-150);
  --font-color-3: var(--gray-level-160);

  --bg-color-1: var(--gray-level-110);
  --bg-color-2: var(--gray-level-80);

  --underline-normal: 219, 219, 219;

  --border-color: var(--gray-level-140);
  --card-bg: var(--gray-level-110);

  --color-contrast: var(--white-level-10);
  --button-ai-bg: var(--gray-level-60);

  --color-card-green: rgba(var(--green-normal), 0.3);
  --color-card-orange: rgba(var(--orange-normal), 0.3);
  --color-card-red: rgba(var(--red-normal), 0.3);
  --color-card-green-blue: rgba(var(--green-blue), 0.3);

  --green-normal: 30, 149, 125;

  --disabled-color: var(--gray-level-140);
}
