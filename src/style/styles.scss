/* You can add global styles to this file, and also import other style files */
#group-study * {
  user-select: none;
  font-family:
    Helvetica Neue,
    Verdana,
    Microsoft Yahei,
    Hiragino Sans GB,
    Microsoft Sans Serif,
    WenQuanYi Micro Hei,
    sans-serif;
  -webkit-touch-callout: none; /*系统默认菜单被禁用*/
  -webkit-user-select: none; /*webkit浏览器*/
  -khtml-user-select: none; /*早期浏览器*/
  -moz-user-select: none; /*火狐*/
  -ms-user-select: none; /*IE10*/
  -webkit-tap-highlight-color: transparent;
}

:root {
  --red-bgColor: #dc663e;
  --theme-green: #36b59d;
  --second-font-color: #8e8e8e;
  --default-font-color: #aaa;
  --container-width: 95%;
  --name-color: #808080;
}

.theme-color {
  font-weight: 600;
  color: var(--theme-green);
}

body {
  background: var(--bg-color-1);
  overscroll-behavior: none;
  margin: 0;
}

.theme-color-spot {
  display: inline-block;
  background-color: var(--theme-green);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  transform: translateY(-0.125rem);
  margin: 0 8px 0 0;
}

.portraits {
  display: flex;
  justify-content: space-evenly;
  margin-top: 20px;
}

.username {
  margin-top: 3px;
  width: 74px;
  text-align: center;
  color: var(--second-font-color);
  font-size: 14px;
}

.portraits > div > div:nth-child(1) {
  width: 54px;
  height: 54px;
  border: 2px dotted var(--border-color);
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

.portraits > div > div:nth-child(1)::after,
.portraits > div > div:nth-child(1)::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 2px;
  background: var(--border-color);
  position: absolute;
}

.portraits > div > div:nth-child(1)::before {
  transform: rotate(90deg);
}

.team-model-2 {
  background: var(--bg-color-2);
  margin: 0 auto;
  padding-bottom: 5px;
  border-radius: 10px;
  width: var(--container-width);
}

.team-status {
  width: 100%;
  color: var(--theme-green);
  font-size: 22px;
  text-align: center;
  margin-top: 22px;
  font-weight: 800;
}

.date-hint {
  color: var(--title-color);
  font-size: 14px;
  margin-top: 10px;
  font-weight: 600;
}

.adjusting-status {
  margin: 0 auto 0px auto;
  /*overflow: hidden;*/
  border-radius: 10px;
  padding-bottom: 1px;
}

.sign-status {
  width: 75px;
}

.adjusting-status > div:nth-child(1) > img:nth-child(even) {
  height: 10px;
}

.adjusting-instruction {
  width: 90%;
  margin: 10px auto;
  color: var(--default-font-color);
  font-size: 13px;
  text-align: left;
}

/*.user-status {*/
/*  width: 98%;*/
/*  margin: 0 auto;*/
/*}*/

/*.user-status > div {*/
/*  display: flex;*/
/*  margin: 16px 0;*/
/*}*/

/*.user-status > div > img {*/
/*  transform: translateY(5px);*/
/*  display: inline-block*/
/*}*/

.username-2 {
  margin-top: 10px;
  color: var(--second-font-color);
  font-size: 13px;
}

.username-2 > span {
  display: inline-block;
  width: 90px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.user-signup {
  margin-left: 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.user-info {
  font-size: 13px;
  color: var(--title-color);
}

.user-info > img {
  padding: 0 3px;
}

.split {
  display: inline-block;
  width: 1px;
  height: 14px;
  background: rgba(229, 229, 229, 1);
  margin: 0 10px;
}

.re-enroll {
  width: 70%;
  height: 42px;
  background-color: var(--theme-green);
  border-radius: 3px;
  margin: 15px auto;
  font-size: 18px;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  outline: none;
  border: none;

  &:active {
    opacity: var(--activity-opacity);
  }
}

.re-enroll:active {
  background-color: rgba(0, 0, 0, 0.2);
}

.team-failed {
  border-radius: 10px;
  overflow: hidden;
  text-align: center;
}

.team-status-failed {
  color: var(--red-bgColor);
  font-size: 22px;
  margin-top: 22px;
  font-weight: 600;
}

.failed-info {
  width: 80%;
  color: var(--red-bgColor);
  font-size: 14px;
  margin: 10px auto;
}

.count-down-time {
  width: 80%;
  margin: 20px auto;
  color: var(--default-font-color);
  font-size: 26px;
  text-align: center;
}

.tick {
  font-size: 1rem;
  white-space: nowrap;
  font-family: arial, sans-serif;
  display: flex;
  flex-direction: row;
}

.tick-flip,
.tick-text-inline {
  font-size: 2.5em;
}

.tick-label {
  margin-top: 1em;
  font-size: 1em;
}

.tick-char {
  width: 1.5em;
}

.tick-text-inline {
  display: inline-block;
  text-align: center;
  min-width: 1em;
}

.tick-text-inline + .tick-text-inline {
  margin-left: -0.325em;
}

.tick-group {
  margin: 0 0.5em;
  text-align: center;
}

.tick-text-inline {
  color: var(--red-bgColor) !important;
}

.tick-label {
  color: #fff !important;
}

.tick-flip-panel {
  color: var(--red-bgColor) !important;
}

.tick-flip-panel-text-wrapper {
  line-height: 1.45 !important;
}

.tick-flip-panel {
  background-color: #fff !important;
}

.tick-flip {
  border-radius: 0.12em !important;
}

.tick-group {
  position: relative;
}

.tick-group:not(:last-child):after {
  content: ':';
  display: block;
  position: absolute;
  top: 2%;
  right: -0.6rem;
  z-index: 10;
  color: #fff;
  font-size: 26px;
}

.countdown-text {
  width: 100%;
  text-align: center;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  padding: 20px 0;
}

.countdown {
  margin-top: 30px;
}

.tick-flip-shadow {
  box-shadow:
    0 0px 0.3125em rgb(0 0 0 / 25%),
    0 0.02125em 0.06125em rgb(0 0 0 / 25%) !important;
}

.tick-flip-panel-back::after {
  background-image: linear-gradient(180deg, rgba(0, 0, 0, 0.15) 0.5px, rgba(0, 0, 0, 0.15) 0, transparent 1%);
}

.tick-flip-shadow-top {
  bottom: calc(70%) !important;
}

.tick-flip {
  font-size: 1.9em;
}

.tick-flip-spacer {
  font-weight: 1000 !important;
}

.sign-status {
  position: relative;
}

.profile-div {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 1s cubic-bezier(0.04, -0.23, 1, -0.24);
  z-index: 1024;
}

.profile-div > img {
  transition: all 1s cubic-bezier(0.8, -0.6, 1, 0);
}

.active-card {
  position: relative;
  z-index: 3;
}

.active-card-finish-second-animation {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  opacity: 0.25;
  z-index: 2;
  transform-origin: center center;
  animation: splash 0.4s forwards ease-out;
  animation-delay: 0.4s;
}

@keyframes splash {
  0% {
    transform: scale(1);
    background: #82c2ab;
  }
  50% {
    transform: scale(1.6);
  }
  100% {
    transform: scale(1);
    background: rgba(130, 194, 171, 0.15);
  }
}

.active-card-finish-third-animation {
  position: absolute;
  left: 0;
  top: 0;
}

.active-card-finish-third-animation > img {
  position: absolute;
  left: 0;
}

.swiper {
  overflow: unset !important;
}

.animation-number > svg {
  position: absolute;
  top: 0;
  left: 0;
}

.swiper {
  z-index: 3;
}

@supports not (aspect-ratio: 1 / 1) {
  .aspect-square::before {
    float: left;
    padding-top: 100%;
    content: '';
  }

  .aspect-square::after {
    display: block;
    content: '';
    clear: both;
  }
}

.button-with-desc[memo-button] {
  padding: 0;
  flex-direction: column;
  border: 1px solid var(--bg-color) !important;
  overflow: hidden;

  .btn-text {
    line-height: 2;
  }

  .btn-desc {
    white-space: nowrap;
    background-color: var(--bg-color-2);
    width: 100%;

    span {
      color: var(--default-font-color);
      display: flex;
      justify-content: center;
      font-size: 18px;
      transform: scale(0.5);
    }
  }

  &:disabled {
    border: 1px solid var(--disabled-color) !important;
  }
}

.red-text {
  color: var(--red-bgColor) !important;
}

.green-text {
  color: var(--theme-green) !important;
}

// TODO: 节日图标，之后可以删
.flag-icon {
  margin: 0 0.5em;
  display: inline-block;
  vertical-align: middle;
  height: 1em;
  transform: scale(1.5);
  transform-origin: 0 70%;
}

memo-link-preview-card {
  --color-title: var(--font-color-1);
  --color-desc: var(--font-color-2);
  --color-border: var(--border-color);
  --color-background: var(--bg-color-2);
}

.scrollbar-none:active::-webkit-scrollbar-thumb,
.scrollbar-none:focus::-webkit-scrollbar-thumb,
.scrollbar-none:hover::-webkit-scrollbar-thumb {
  visibility: visible;
}
.scrollbar-none::-webkit-scrollbar-thumb {
  display: none;
  visibility: hidden;
}
.scrollbar-none::-webkit-scrollbar {
  display: none;
  visibility: hidden;
}
