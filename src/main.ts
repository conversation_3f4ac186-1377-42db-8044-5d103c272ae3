import { enableProdMode } from '@angular/core'
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic'
import { Modules, getVersionFromUA } from '@maimemo/client-frontend-bridge'
import { browserTracingIntegration, init, setTags, setUser } from '@sentry/angular'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import { AppModule } from './app/app.module'
import { AppConfig } from './app/configs/app.config'
import { environment } from './environments/environment'
import { VERSION } from './version'
import { isAndroid } from './app/modules/shared/helper/env'

if (
  !isAndroid(window)
  && getVersionFromUA(navigator.userAgent).includes('5.3.75')
  && location.href.includes('https://www.maimemo.com')
) {
  location.replace(location.href.replace('www.maimemo.com', 'maimemo.com'))
}

function fixIosBridge() {
  window.Memo = window.Memo || {}
  for (const k in window) {
    if (k.startsWith('Memo_')) {
      window.Memo[k.substring(5, k.length)] = window[k]
    }
  }
}
fixIosBridge()

dayjs.extend(timezone)
dayjs.extend(utc)
dayjs.extend(duration)
dayjs.tz.setDefault('Asia/Shanghai');

// ResizeObserver polyfill
(async () => {
  if ('ResizeObserver' in window === false) {
    // Loads polyfill asynchronously, only if required.
    const module = await import('@juggle/resize-observer');
    (window as any).ResizeObserver = module.ResizeObserver
  }
})()

if (environment.production) {
  init({
    dsn: AppConfig.sentryDsn,
    integrations: [
      browserTracingIntegration({
        enableInp: true,
      }),
    ],
    release: VERSION,
    tracesSampleRate: 0.1,
  })
  if (/MaiMemo\/(.*?)( |$)/i.exec(navigator.userAgent)) {
    const app_version = /MaiMemo\/(.*?)( |$)/i.exec(navigator.userAgent)
    const device_id = /DId\/(.*?)( |$)/i.exec(navigator.userAgent)
    setTags({
      app_version: app_version ? app_version[1] : '',
      device_id: device_id ? device_id[1] : '',
    })
    Modules.common.getUserAccessToken().then(res => {
      const uid = res.data.userId || res.data.user_id
      setUser({ id: uid })
    })
  }
  enableProdMode()
} else {
  const meta = document.createElement('meta')

  meta.setAttribute('name', 'referrer')
  meta.setAttribute('content', 'no-referrer')
  document.getElementsByTagName('head')[0].appendChild(meta)
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err))
