/*
 * @pqina/flip v1.7.7 - A Beautifully Animated Flip Clock
 * Copyright (c) 2020 PQINA - https://pqina.nl/flip/
 */
.tick{box-sizing:border-box;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default;position:relative;z-index:1;line-height:1.4}.tick *{box-sizing:inherit}.tick [data-view]{max-width:100%}.tick span[data-view]{display:inline-block}.tick [data-layout~=pad]{margin:-.25em}.tick [data-layout~=pad]>*{margin:.25em}.tick [data-layout~=horizontal]{display:-ms-flexbox;display:flex;-ms-flex-direction:row;flex-direction:row;-ms-flex-pack:center;justify-content:center}.tick [data-layout~=horizontal][data-layout~=baseline]{-ms-flex-align:baseline;align-items:baseline}.tick [data-layout~=horizontal][data-layout~=center]{-ms-flex-pack:center;justify-content:center}.tick [data-layout~=horizontal][data-layout~=right]{-ms-flex-pack:end;justify-content:flex-end}.tick [data-layout~=horizontal][data-layout~=left]{-ms-flex-pack:start;justify-content:flex-start}.tick [data-layout~=horizontal][data-layout~=fill],.tick [data-layout~=horizontal][data-layout~=stretch]{-ms-flex-line-pack:stretch;align-content:stretch;-ms-flex-wrap:nowrap;flex-wrap:nowrap}.tick [data-layout~=horizontal][data-layout~=fill]>*,.tick [data-layout~=horizontal][data-layout~=stretch]>*{-ms-flex:1 0 0px;flex:1 0 0;width:100%}.tick [data-layout~=horizontal][data-layout~=multi-line]{-ms-flex-wrap:wrap;flex-wrap:wrap}.tick [data-layout~=horizontal][data-layout~=fit]{display:-ms-inline-flexbox;display:inline-flex;-ms-flex-wrap:nowrap;flex-wrap:nowrap;-ms-flex-line-pack:center;align-content:center;white-space:nowrap;-ms-flex-pack:start;justify-content:flex-start}.tick [data-layout~=vertical]{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center}.tick [data-layout~=vertical][data-layout~=top]{-ms-flex-pack:start;justify-content:flex-start}.tick [data-layout~=vertical][data-layout~=bottom]{-ms-flex-pack:end;justify-content:flex-end;min-height:100%}.tick [data-layout~=vertical][data-layout~=middle]{-ms-flex-pack:center;justify-content:center;min-height:100%}.tick [data-layout~=vertical][data-layout~=left]{-ms-flex-align:start;align-items:flex-start}.tick [data-layout~=vertical][data-layout~=right]{-ms-flex-align:end;align-items:flex-end}.tick [data-layout~=vertical][data-layout~=center]{text-align:center}.tick [data-layout~=vertical][data-layout~=fill],.tick [data-layout~=vertical][data-layout~=stretch]{-ms-flex-align:stretch;align-items:stretch;min-height:100%}.tick [data-layout~=vertical][data-layout~=fill]>*,.tick [data-layout~=vertical][data-layout~=stretch]>*{-ms-flex:1 0 0px;flex:1 0 0}.tick [data-layout~=vertical]>*+*{margin-top:.5em}.tick [data-layout~=overlay]{position:relative}.tick [data-layout~=overlay]>*{margin:0}.tick [data-layout~=overlay][data-layout~=center]{text-align:center}.tick [data-layout~=overlay][data-layout~=left]{text-align:left}.tick [data-layout~=overlay][data-layout~=right]{text-align:right}.tick [data-layout~=overlay]>[data-overlay=fill],.tick [data-layout~=overlay]>[data-overlay=stretch]{position:absolute;left:0;right:0;top:0;bottom:0}.tick [data-layout~=overlay]>[data-overlay=center]{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;position:absolute;left:0;right:0;top:0;bottom:0}.tick-flip{position:relative;text-align:center}.tick-flip *{border-radius:inherit;white-space:pre;letter-spacing:inherit;text-indent:inherit}.tick-flip-front{border-bottom-left-radius:0;border-bottom-right-radius:0}.tick-flip-back{border-top-left-radius:0;border-top-right-radius:0}.tick-flip-spacer{display:block;visibility:hidden}.tick-flip-shadow{position:absolute;left:1px;right:1px;top:1px;bottom:1px;color:transparent!important;background:transparent!important}.tick-flip-shadow-top{bottom:calc(50% - 1px)}.tick-flip-shadow-bottom{top:calc(50% + 1px)}.tick-flip-card-shadow{position:absolute;left:.15em;right:.15em;bottom:.125em;height:.5em;background-color:transparent;border-radius:0;opacity:0;transform-origin:0 100%;box-shadow:0 .125em .25em rgba(0,0,0,.5),0 .125em .5em rgba(0,0,0,.75);z-index:0}.tick-flip-card{position:absolute;z-index:1;left:0;top:0;width:100%;height:100%;perspective:4em}.tick-flip-panel-back,.tick-flip-panel-front{position:absolute;left:0;width:100%;height:51%;-webkit-backface-visibility:hidden;backface-visibility:hidden;transform-style:preserve-3d}.tick-flip-panel-back-text,.tick-flip-panel-front-text{position:absolute;left:-1px;top:0;right:-1px;height:100%;overflow:hidden}.tick-flip-panel-text-wrapper{position:absolute;left:0;top:0;right:0;height:100%}.tick-flip-panel-back-text .tick-flip-panel-text-wrapper{height:200%;top:-100%}.tick-flip-panel-front{transform-origin:center bottom;top:0;z-index:2;box-shadow:inset 0 1px hsla(0,0%,100%,.05)}.tick-flip-panel-back{transform-origin:center top;top:50%;z-index:1;box-shadow:inset 0 -1px rgba(0,0,0,.1)}.tick-flip-panel-back:after{z-index:1;content:"";position:absolute;left:0;top:0;width:100%;height:100%;background-image:linear-gradient(180deg,rgba(0,0,0,.3) 1px,rgba(0,0,0,.15) 0,transparent 30%)}.tick-flip-panel-back-shadow{z-index:2}.tick-flip-panel-back-highlight{z-index:3}.tick-flip-panel-back-highlight,.tick-flip-panel-back-shadow{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.tick-flip-panel-back-highlight,.tick-flip-panel-back-shadow,.tick-flip-panel-front-shadow{position:absolute;left:0;top:0;right:0;bottom:0;opacity:0}.tick-flip-panel-front-shadow{background-image:linear-gradient(0deg,rgba(0,0,0,.8),rgba(0,0,0,.3))}.tick-flip-panel-back-shadow{background-image:linear-gradient(180deg,rgba(0,0,0,.7),rgba(0,0,0,.5))}.tick-flip-panel-back-highlight{background-image:linear-gradient(180deg,hsla(0,0%,100%,.15),hsla(0,0%,100%,.3))}.tick [data-style*="shadow:inner"],.tick [data-style*="shadow:inner"] .tick-flip-card-shadow,.tick [data-style*="shadow:none"] .tick-flip-card-shadow,.tick [data-style*="shadow:none"] .tick-flip-panel-back,.tick [data-style*="shadow:none"] .tick-flip-panel-front,.tick [data-style*="shadow:none"] .tick-flip-shadow{box-shadow:none}.tick [data-style*="shadow:none"] .tick-flip-back:after,.tick [data-style*="shadow:none"] .tick-flip-panel-back-shadow,.tick [data-style*="shadow:none"] .tick-flip-panel-back-text:after,.tick [data-style*="shadow:none"] .tick-flip-panel-front-shadow{background-image:none}.tick [data-style*="rounded:none"]{border-radius:0}.tick [data-style*="rounded:panels"] .tick-flip-front,.tick [data-style*="rounded:panels"] .tick-flip-shadow-bottom{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.tick [data-style*="rounded:panels"] .tick-flip-back,.tick [data-style*="rounded:panels"] .tick-flip-panel-back:after,.tick [data-style*="rounded:panels"] .tick-flip-shadow-top{border-top-left-radius:inherit;border-top-right-radius:inherit}.tick-flip{margin-left:.0625em;margin-right:.0625em;min-width:1.125em;border-radius:.125em;letter-spacing:.25em;text-indent:.25em}.tick-flip-panel{color:#edebeb;background-color:#333232}.tick-flip-shadow{box-shadow:0 .125em .3125em rgba(0,0,0,.25),0 .02125em .06125em rgba(0,0,0,.25)}