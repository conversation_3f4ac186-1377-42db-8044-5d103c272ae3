export const tickAnimationData = {
  'v': '5.6.10',
  'fr': 29.9700012207031,
  'ip': 0,
  'op': 107.000004358199,
  'w': 360,
  'h': 360,
  'nm': '圆形勾选',
  'ddd': 0,
  'assets': [],
  'layers': [{
    'ddd': 0,
    'ind': 1,
    'ty': 4,
    'nm': '勾',
    'sr': 1,
    'ks': {
      'o': { 'a': 0, 'k': 100, 'ix': 11 },
      'r': { 'a': 0, 'k': 0, 'ix': 10 },
      'p': { 'a': 0, 'k': [180.056, 180.011, 0], 'ix': 2 },
      'a': { 'a': 0, 'k': [28.402, 25.421, 0], 'ix': 1 },
      's': {
        'a': 1,
        'k': [{
          'i': { 'x': [0.833, 0.833, 0.833], 'y': [0.833, 0.833, 1] },
          'o': { 'x': [1, 1, 0.333], 'y': [0.003, 0.003, 0] },
          't': 35,
          's': [0, 0, 100],
        }, {
          'i': { 'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1] },
          'o': { 'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0] },
          't': 41,
          's': [150, 150, 100],
        }, {
          'i': { 'x': [0.599, 0.599, 0.833], 'y': [1.945, 1.945, 1] },
          'o': { 'x': [0.211, 0.211, 0.167], 'y': [1.647, 1.647, 0] },
          't': 45,
          's': [80, 80, 100],
        }, { 't': 51.0000020772726, 's': [100, 100, 100] }],
        'ix': 6,
      },
    },
    'ao': 0,
    'shapes': [{
      'ty': 'gr',
      'it': [{
        'ind': 0,
        'ty': 'sh',
        'ix': 1,
        'ks': {
          'a': 0,
          'k': {
            'i': [[-2.283, -2.474], [0, 0], [0, 0], [-2.652, -2.073], [2.073, -2.652], [0, 0], [2.549, 2.761], [0, 0], [-2.474, 2.283]],
            'o': [[0, 0], [0, 0], [2.074, -2.651], [2.652, 2.074], [0, 0], [-2.315, 2.96], [0, 0], [-2.283, -2.474], [2.473, -2.283]],
            'v': [[-16.911, -1.097], [-7.709, 8.873], [16.476, -22.051], [25.032, -23.098], [26.079, -14.541], [-2.523, 22.03], [-11.804, 22.41], [-25.868, 7.172], [-25.524, -1.442]],
            'c': true,
          },
          'ix': 2,
        },
        'nm': '路径 1',
        'mn': 'ADBE Vector Shape - Group',
        'hd': false,
      }, {
        'ty': 'fl',
        'c': { 'a': 0, 'k': [1, 1, 1, 1], 'ix': 4 },
        'o': { 'a': 0, 'k': 100, 'ix': 5 },
        'r': 1,
        'bm': 0,
        'nm': 'Fill 1',
        'mn': 'ADBE Vector Graphic - Fill',
        'hd': false,
      }, {
        'ty': 'tr',
        'p': { 'a': 0, 'k': [28.401, 25.421], 'ix': 2 },
        'a': { 'a': 0, 'k': [0, 0], 'ix': 1 },
        's': { 'a': 0, 'k': [100, 100], 'ix': 3 },
        'r': { 'a': 0, 'k': 0, 'ix': 6 },
        'o': { 'a': 0, 'k': 100, 'ix': 7 },
        'sk': { 'a': 0, 'k': 0, 'ix': 4 },
        'sa': { 'a': 0, 'k': 0, 'ix': 5 },
        'nm': '变换',
      }],
      'nm': 'Group 1',
      'np': 2,
      'cix': 2,
      'bm': 0,
      'ix': 1,
      'mn': 'ADBE Vector Group',
      'hd': false,
    }],
    'ip': 15.0000006109625,
    'op': 383.00001559991,
    'st': 15.0000006109625,
    'bm': 0,
  }, {
    'ddd': 0,
    'ind': 3,
    'ty': 4,
    'nm': '绿色圆',
    'sr': 1,
    'ks': {
      'o': {
        'a': 1,
        'k': [{ 'i': { 'x': [0.833], 'y': [1] }, 'o': { 'x': [0.167], 'y': [0] }, 't': 15, 's': [0] }, {
          'i': {
            'x': [0.833],
            'y': [0.833],
          }, 'o': { 'x': [0.167], 'y': [0.167] }, 't': 25, 's': [0],
        }, { 't': 35.0000014255792, 's': [100] }],
        'ix': 11,
      },
      'r': { 'a': 0, 'k': 0, 'ix': 10 },
      'p': { 'a': 0, 'k': [180, 179.999, 0], 'ix': 2 },
      'a': { 'a': 0, 'k': [60.25, 60.25, 0], 'ix': 1 },
      's': {
        'a': 1,
        'k': [{
          'i': { 'x': [0.833, 0.833, 0.833], 'y': [0.833, 0.833, 1] },
          'o': { 'x': [1, 1, 0.333], 'y': [0.003, 0.003, 0] },
          't': 35,
          's': [0, 0, 100],
        }, {
          'i': { 'x': [0.833, 0.833, 0.833], 'y': [1, 1, 1] },
          'o': { 'x': [0.333, 0.333, 0.333], 'y': [0, 0, 0] },
          't': 41,
          's': [150, 150, 100],
        }, {
          'i': { 'x': [0.599, 0.599, 0.833], 'y': [1.573, 1.573, 1] },
          'o': { 'x': [0.211, 0.211, 0.167], 'y': [0.998, 0.998, 0] },
          't': 45,
          's': [67, 67, 100],
        }, { 't': 51.0000020772726, 's': [100, 100, 100] }],
        'ix': 6,
      },
    },
    'ao': 0,
    'shapes': [{
      'ty': 'gr',
      'it': [{
        'ind': 0,
        'ty': 'sh',
        'ix': 1,
        'ks': {
          'a': 0,
          'k': {
            'i': [[-33.138, 0], [0, 33.138], [33.137, 0], [0, -33.137]],
            'o': [[33.137, 0], [0, -33.137], [-33.138, 0], [0, 33.138]],
            'v': [[0, 60], [60, 0], [0, -60], [-60, 0]],
            'c': true,
          },
          'ix': 2,
        },
        'nm': '路径 1',
        'mn': 'ADBE Vector Shape - Group',
        'hd': false,
      }, {
        'ty': 'fl',
        'c': { 'a': 0, 'k': [0.211764705882, 0.709803921569, 0.61568627451, 1], 'ix': 4 },
        'o': { 'a': 0, 'k': 100, 'ix': 5 },
        'r': 1,
        'bm': 0,
        'nm': 'Fill 1',
        'mn': 'ADBE Vector Graphic - Fill',
        'hd': false,
      }, {
        'ty': 'tr',
        'p': { 'a': 0, 'k': [60.25, 60.25], 'ix': 2 },
        'a': { 'a': 0, 'k': [0, 0], 'ix': 1 },
        's': { 'a': 0, 'k': [100, 100], 'ix': 3 },
        'r': { 'a': 0, 'k': 0, 'ix': 6 },
        'o': { 'a': 0, 'k': 100, 'ix': 7 },
        'sk': { 'a': 0, 'k': 0, 'ix': 4 },
        'sa': { 'a': 0, 'k': 0, 'ix': 5 },
        'nm': '变换',
      }],
      'nm': 'Group 1',
      'np': 2,
      'cix': 2,
      'bm': 0,
      'ix': 1,
      'mn': 'ADBE Vector Group',
      'hd': false,
    }],
    'ip': 15.0000006109625,
    'op': 378.000015396256,
    'st': 15.0000006109625,
    'bm': 0,
  }, {
    'ddd': 0,
    'ind': 4,
    'ty': 4,
    'nm': '绿色发散',
    'sr': 1,
    'ks': {
      'o': {
        'a': 1,
        'k': [{
          'i': { 'x': [0.833], 'y': [0.896] },
          'o': { 'x': [0.167], 'y': [0.104] },
          't': 31,
          's': [0],
        }, {
          'i': { 'x': [0.833], 'y': [0.896] },
          'o': { 'x': [0.167], 'y': [0.104] },
          't': 33,
          's': [80],
        }, { 't': 36.0000014663101, 's': [0] }],
        'ix': 11,
      },
      'r': { 'a': 0, 'k': 0, 'ix': 10 },
      'p': { 'a': 0, 'k': [180, 179.999, 0], 'ix': 2 },
      'a': { 'a': 0, 'k': [89.955, 89.955, 0], 'ix': 1 },
      's': {
        'a': 1,
        'k': [{
          'i': { 'x': [0.833, 0.833, 0.833], 'y': [0.833, 0.833, 1] },
          'o': { 'x': [0.167, 0.167, 0.167], 'y': [0.167, 0.167, 0] },
          't': 15,
          's': [0, 0, 100],
        }, {
          'i': { 'x': [0.833, 0.833, 0.833], 'y': [0.898, 0.898, 1] },
          'o': { 'x': [0.167, 0.167, 0.167], 'y': [0.102, 0.102, 0] },
          't': 31,
          's': [72, 72, 100],
        }, { 't': 36.0000014663101, 's': [200, 200, 100] }],
        'ix': 6,
      },
    },
    'ao': 0,
    'shapes': [{
      'ty': 'gr',
      'it': [{
        'ind': 0,
        'ty': 'sh',
        'ix': 1,
        'ks': {
          'a': 0,
          'k': {
            'i': [[-44.183, 0], [0, 44.184], [44.183, 0], [0, -44.183]],
            'o': [[44.183, 0], [0, -44.183], [-44.183, 0], [0, 44.184]],
            'v': [[0, 80], [80, 0], [0, -80], [-80, 0]],
            'c': true,
          },
          'ix': 2,
        },
        'nm': '路径 1',
        'mn': 'ADBE Vector Shape - Group',
        'hd': false,
      }, {
        'ty': 'st',
        'c': { 'a': 0, 'k': [0.211764705882, 0.709803921569, 0.61568627451, 1], 'ix': 3 },
        'o': { 'a': 0, 'k': 100, 'ix': 4 },
        'w': { 'a': 0, 'k': 20, 'ix': 5 },
        'lc': 1,
        'lj': 1,
        'ml': 10,
        'bm': 0,
        'nm': 'Stroke 1',
        'mn': 'ADBE Vector Graphic - Stroke',
        'hd': false,
      }, {
        'ty': 'tr',
        'p': { 'a': 0, 'k': [89.955, 89.955], 'ix': 2 },
        'a': { 'a': 0, 'k': [0, 0], 'ix': 1 },
        's': { 'a': 0, 'k': [100, 100], 'ix': 3 },
        'r': { 'a': 0, 'k': 0, 'ix': 6 },
        'o': { 'a': 0, 'k': 100, 'ix': 7 },
        'sk': { 'a': 0, 'k': 0, 'ix': 4 },
        'sa': { 'a': 0, 'k': 0, 'ix': 5 },
        'nm': '变换',
      }],
      'nm': 'Group 1',
      'np': 2,
      'cix': 2,
      'bm': 0,
      'ix': 1,
      'mn': 'ADBE Vector Group',
      'hd': false,
    }],
    'ip': 15.0000006109625,
    'op': 65.0000026475043,
    'st': 15.0000006109625,
    'bm': 0,
  }, {
    'ddd': 0,
    'ind': 5,
    'ty': 4,
    'nm': '绿色描边旋转一周 2',
    'sr': 1,
    'ks': {
      'o': {
        'a': 1,
        'k': [{
          'i': { 'x': [0.833], 'y': [0.833] },
          'o': { 'x': [0.167], 'y': [0.167] },
          't': 25,
          's': [100],
        }, { 't': 35.0000014255792, 's': [0] }],
        'ix': 11,
      },
      'r': { 'a': 0, 'k': 0, 'ix': 10 },
      'p': { 'a': 0, 'k': [180, 179.999, 0], 'ix': 2 },
      'a': { 'a': 0, 'k': [67.465, 67.465, 0], 'ix': 1 },
      's': {
        'a': 1,
        'k': [{
          'i': { 'x': [0.833, 0.833, 0.833], 'y': [0.833, 0.833, 1] },
          'o': { 'x': [0.167, 0.167, 0.167], 'y': [0.167, 0.167, 0] },
          't': 25,
          's': [100, 100, 100],
        }, { 't': 35.0000014255792, 's': [150, 150, 100] }],
        'ix': 6,
      },
    },
    'ao': 0,
    'shapes': [{
      'ty': 'gr',
      'it': [{
        'ind': 0,
        'ty': 'sh',
        'ix': 1,
        'ks': {
          'a': 0,
          'k': {
            'i': [[33.137, 0], [0, 33.138], [-33.138, 0], [0, -33.137]],
            'o': [[-33.138, 0], [0, -33.137], [33.137, 0], [0, 33.138]],
            'v': [[0, 60], [-60, 0], [0, -60], [60, 0]],
            'c': true,
          },
          'ix': 2,
        },
        'nm': '路径 1',
        'mn': 'ADBE Vector Shape - Group',
        'hd': false,
      }, {
        'ty': 'tm',
        's': {
          'a': 1,
          'k': [{
            'i': { 'x': [0.833], 'y': [0.833] },
            'o': { 'x': [0.167], 'y': [0.167] },
            't': 15,
            's': [100],
          }, { 't': 25.0000010182709, 's': [0] }],
          'ix': 1,
        },
        'e': { 'a': 0, 'k': 100, 'ix': 2 },
        'o': { 'a': 0, 'k': 0, 'ix': 3 },
        'm': 1,
        'ix': 2,
        'nm': 'Trim Paths 1',
        'mn': 'ADBE Vector Filter - Trim',
        'hd': false,
      }, {
        'ty': 'st',
        'c': { 'a': 0, 'k': [0.211764705882, 0.709803921569, 0.61568627451, 1], 'ix': 3 },
        'o': { 'a': 0, 'k': 100, 'ix': 4 },
        'w': { 'a': 0, 'k': 20, 'ix': 5 },
        'lc': 1,
        'lj': 1,
        'ml': 10,
        'bm': 0,
        'nm': 'Stroke 1',
        'mn': 'ADBE Vector Graphic - Stroke',
        'hd': false,
      }, {
        'ty': 'tr',
        'p': { 'a': 0, 'k': [67.465, 67.465], 'ix': 2 },
        'a': { 'a': 0, 'k': [0, 0], 'ix': 1 },
        's': { 'a': 0, 'k': [100, 100], 'ix': 3 },
        'r': { 'a': 0, 'k': 0, 'ix': 6 },
        'o': { 'a': 0, 'k': 100, 'ix': 7 },
        'sk': { 'a': 0, 'k': 0, 'ix': 4 },
        'sa': { 'a': 0, 'k': 0, 'ix': 5 },
        'nm': '变换',
      }],
      'nm': 'Group 1',
      'np': 3,
      'cix': 2,
      'bm': 0,
      'ix': 1,
      'mn': 'ADBE Vector Group',
      'hd': false,
    }],
    'ip': 15.0000006109625,
    'op': 65.0000026475043,
    'st': 15.0000006109625,
    'bm': 0,
  }, {
    'ddd': 0,
    'ind': 7,
    'ty': 4,
    'nm': '灰色描边',
    'sr': 1,
    'ks': {
      'o': {
        'a': 1,
        'k': [{
          'i': { 'x': [0.833], 'y': [0.833] },
          'o': { 'x': [0.167], 'y': [0.167] },
          't': 10,
          's': [100],
        }, { 't': 20.0000008146167, 's': [0] }],
        'ix': 11,
      },
      'r': { 'a': 0, 'k': 0, 'ix': 10 },
      'p': { 'a': 0, 'k': [180, 179.999, 0], 'ix': 2 },
      'a': { 'a': 0, 'k': [67.465, 67.465, 0], 'ix': 1 },
      's': { 'a': 0, 'k': [100, 100, 100], 'ix': 6 },
    },
    'ao': 0,
    'shapes': [{
      'ty': 'gr',
      'it': [{
        'ind': 0,
        'ty': 'sh',
        'ix': 1,
        'ks': {
          'a': 0,
          'k': {
            'i': [[-33.138, 0], [0, 33.138], [33.137, 0], [0, -33.137]],
            'o': [[33.137, 0], [0, -33.137], [-33.138, 0], [0, 33.138]],
            'v': [[0, 60], [60, 0], [0, -60], [-60, 0]],
            'c': true,
          },
          'ix': 2,
        },
        'nm': '路径 1',
        'mn': 'ADBE Vector Shape - Group',
        'hd': false,
      }, {
        'ty': 'st',
        'c': { 'a': 0, 'k': [0.929411764706, 0.929411764706, 0.929411764706, 1], 'ix': 3 },
        'o': { 'a': 0, 'k': 100, 'ix': 4 },
        'w': { 'a': 0, 'k': 20, 'ix': 5 },
        'lc': 1,
        'lj': 1,
        'ml': 10,
        'bm': 0,
        'nm': 'Stroke 1',
        'mn': 'ADBE Vector Graphic - Stroke',
        'hd': false,
      }, {
        'ty': 'tr',
        'p': { 'a': 0, 'k': [67.465, 67.465], 'ix': 2 },
        'a': { 'a': 0, 'k': [0, 0], 'ix': 1 },
        's': { 'a': 0, 'k': [100, 100], 'ix': 3 },
        'r': { 'a': 0, 'k': 0, 'ix': 6 },
        'o': { 'a': 0, 'k': 100, 'ix': 7 },
        'sk': { 'a': 0, 'k': 0, 'ix': 4 },
        'sa': { 'a': 0, 'k': 0, 'ix': 5 },
        'nm': '变换',
      }],
      'nm': 'Group 1',
      'np': 2,
      'cix': 2,
      'bm': 0,
      'ix': 1,
      'mn': 'ADBE Vector Group',
      'hd': false,
    }],
    'ip': 0,
    'op': 50.0000020365418,
    'st': 0,
    'bm': 0,
  }],
  'markers': [],
}
