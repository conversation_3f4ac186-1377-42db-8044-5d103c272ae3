import { ActivatedRouteSnapshot, BaseRouteReuseStrategy, DetachedRouteHandle, Route } from '@angular/router'

export class MemoRouteReuseStrategy extends BaseRouteReuseStrategy {

  private storeMap = new Map<string, DetachedRouteHandle>()

  override shouldDetach(route: ActivatedRouteSnapshot): boolean {
    const config = this.getRouteFromActivatedRouteSnapshot(route)
    return !!config && this.shouldKeepAlive(config)
  }

  override shouldAttach(route: ActivatedRouteSnapshot): boolean {
    const config = this.getRouteFromActivatedRouteSnapshot(route)
    const path = config?.path

    return !!config && !!path && this.shouldKeepAlive(config) && this.storeMap.has(path) && this.storeMap.get(path) !== null
  }

  override store(route: ActivatedRouteSnapshot, detachedTree: DetachedRouteHandle): void {
    const path = this.getRouteFromActivatedRouteSnapshot(route)?.path
    path && this.storeMap.set(path, detachedTree)
  }

  override retrieve(route: ActivatedRouteSnapshot): DetachedRouteHandle | null {
    const path = this.getRouteFromActivatedRouteSnapshot(route)?.path
    return (path && this.storeMap.get(path)) || null
  }

  private shouldKeepAlive(config: Route): boolean {
    return !!config.data?.keepAlive
  }

  private getRouteFromActivatedRouteSnapshot(snapshot: ActivatedRouteSnapshot): Route | null {
    let route: ActivatedRouteSnapshot = snapshot

    while (route.firstChild) {
      route = route.firstChild
    }

    return route.routeConfig
  }
}
