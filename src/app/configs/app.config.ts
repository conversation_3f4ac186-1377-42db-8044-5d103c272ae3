import { InjectionToken } from '@angular/core'

export const AppConfig = {
  sentryDsn: 'https://<EMAIL>/3',
  activityBasePath: '/pages/activity-center/group-study',
  defaultAvatarPath: 'assets/images/default-avatar.png',
}

export const IS_DEV_MODE = new InjectionToken<boolean>('IS_DEV_MODE')

export const PRODUCT_EXAMPLE = {
  goods_catalog: 'product',
  goods_type: 'memo',
  goods_sn: '**********',
  goods_ios: 'com.maimemo.ios.momo.product.RC1',
  goods_name: -1,
  goods_desc: '增加1个补签卡',
  goods_full_desc: '增加1个补签卡',
  bonus: 0,
  shop_price: 6,
  promote_price: 6,
  promotion_desc: null,
  amount: 0,
  currency: '¥', // 若字段不存在，默认为 ¥
  // extra: {
  //   cashback: {
  //     redpack: 0,
  //     paybank: 1,
  //     transfer: 0,
  //   },
  //   purchase: {},
  //   promotion: {},
  //   subscribe: {},
  //   is_selected: false,
  //   tags: [],
  // },
}
