import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgModule, inject, provideAppInitializer } from '@angular/core'
import { BrowserModule } from '@angular/platform-browser'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { provideRouter, Router, RouteReuseStrategy, RouterOutlet, TitleStrategy, withViewTransitions, type Routes } from '@angular/router'
import { IonicModule } from '@ionic/angular'
import { provideSvgIcons } from '@ngneat/svg-icon'
import * as Sentry from '@sentry/angular'
import { providePrimeNG } from 'primeng/config'

import * as icons from '../app/svg/index'
import { environment } from '../environments/environment'
import { AppComponent } from './app.component'
import { IS_DEV_MODE } from './configs/app.config'
import { GlobalErrorHandler } from './modules/core/handlers/error.handler'
import { AuthInterceptor } from './modules/core/interceptors/auth.interceptor'
import { ErrorInterceptor } from './modules/core/interceptors/error.interceptor'
import { MemoTitleStrategy } from './modules/core/memo-title-stategy'
import { MemoRouteReuseStrategy } from './configs/route-reuse-strategy'
import { UserInfoResolver } from './modules/core/resolvers/user.resolver'
import { MaimemoPrimeNGPreset } from './configs/primeng-preset'

const routes: Routes = [
  {
    path: 'group-study',
    loadChildren: () => import('./modules/entry/group-study.module').then(x => x.GroupStudyModule),
    resolve: {
      user: UserInfoResolver,
    },
  },
  {
    path: 'error',
    loadComponent: () => import('./error/error.component').then(c => c.ErrorComponent),
  },
]

@NgModule({
  declarations: [
    AppComponent,
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    IonicModule.forRoot({
      mode: 'ios',
      spinner: 'crescent',
    }),
    RouterOutlet,
  ],
  providers: [
    {
      provide: TitleStrategy,
      useClass: MemoTitleStrategy,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ErrorInterceptor,
      multi: true,
    },
    {
      provide: Sentry.TraceService,
      deps: [Router],
    },
    provideAppInitializer(() => {
      const initializerFn = (() => () => {
        // noop
        inject(Sentry.TraceService)
      })()
      return initializerFn()
    }),
    {
      provide: ErrorHandler,
      useClass: GlobalErrorHandler,
    },
    {
      provide: IS_DEV_MODE,
      useValue: !environment.production,
    },

    {
      provide: RouteReuseStrategy,
      useClass: MemoRouteReuseStrategy,
    },
    provideRouter(routes, withViewTransitions({
      skipInitialTransition: true,
    })),
    provideSvgIcons(Object.values(icons)),
    provideHttpClient(withInterceptorsFromDi()),
    providePrimeNG({
      theme: {
        preset: MaimemoPrimeNGPreset,
        options: {
          darkModeSelector: '.dark',
        },
      },
    }),
  ],
  exports: [
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
