import { signal } from '@angular/core'

const TAP_OFFSET = 5

type Direction = '' | 'vertical' | 'horizontal'

function getDirection(x: number, y: number) {
  if (x > y) {
    return 'horizontal'
  }
  if (y > x) {
    return 'vertical'
  }
  return ''
}

export function useTouch() {
  const startX = signal(0)
  const startY = signal(0)
  const deltaX = signal(0)
  const deltaY = signal(0)
  const offsetX = signal(0)
  const offsetY = signal(0)
  const direction = signal<Direction>('')
  const isTap = signal(true)

  const isVertical = () => direction() === 'vertical'
  const isHorizontal = () => direction() === 'horizontal'

  const reset = () => {
    deltaX.set(0)
    deltaY.set(0)
    offsetX.set(0)
    offsetY.set(0)
    direction.set('')
    isTap.set(true)
  }

  const start = ((event: TouchEvent) => {
    reset()
    startX.set(event.touches[0].clientX)
    startY.set(event.touches[0].clientY)
  }) as EventListener

  const move = ((event: TouchEvent) => {
    const touch = event.touches[0]
    // safari back will set clientX to negative number
    deltaX.set((touch.clientX < 0 ? 0 : touch.clientX) - startX())
    deltaY.set(touch.clientY - startY())
    offsetX.set(Math.abs(deltaX()))
    offsetY.set(Math.abs(deltaY()))

    // lock direction when distance is greater than a certain value
    const LOCK_DIRECTION_DISTANCE = 10
    if (
      !direction()
      || (offsetX() < LOCK_DIRECTION_DISTANCE
        && offsetY() < LOCK_DIRECTION_DISTANCE)
    ) {
      direction.set(getDirection(offsetX(), offsetY()))
    }

    if (
      isTap()
      && (offsetX() > TAP_OFFSET || offsetY() > TAP_OFFSET)
    ) {
      isTap.set(false)
    }
  }) as EventListener

  return {
    move,
    start,
    reset,
    startX,
    startY,
    deltaX,
    deltaY,
    offsetX,
    offsetY,
    direction,
    isVertical,
    isHorizontal,
    isTap,
  }
}
