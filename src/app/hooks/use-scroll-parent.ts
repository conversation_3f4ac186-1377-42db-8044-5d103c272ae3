import { ElementRef, Signal, afterNextRender, signal } from '@angular/core'

type ScrollElement = HTMLElement | Window

const overflowScrollReg = /scroll|auto|overlay/i
const defaultRoot = typeof window !== 'undefined' ? window : undefined

function isElement(node: Element) {
  const ELEMENT_NODE_TYPE = 1
  return (
    node.tagName !== 'HTML'
    && node.tagName !== 'BODY'
    && node.nodeType === ELEMENT_NODE_TYPE
  )
}

// https://github.com/vant-ui/vant/issues/3823
export function getScrollParent(
  el: Element,
  root: ScrollElement | undefined = defaultRoot,
) {
  let node = el

  while (node && node !== root && isElement(node)) {
    const { overflowY } = window.getComputedStyle(node)
    if (overflowScrollReg.test(overflowY)) {
      return node
    }
    node = node.parentNode as Element
  }

  return root
}

export function useScrollParent(
  target: Signal<Element | ElementRef | undefined>,
  root: ScrollElement | undefined = defaultRoot,
) {
  const scrollParent = signal<Element | Window | undefined>(undefined)

  afterNextRender({
    write: () => {
      const elValue = target()

      if (!elValue) {
        return
      }
      const el = elValue instanceof ElementRef ? elValue.nativeElement : elValue
      scrollParent.set(getScrollParent(el, root))
    },
  })

  return scrollParent
}
