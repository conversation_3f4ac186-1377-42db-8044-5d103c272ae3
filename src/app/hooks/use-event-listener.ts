import { DestroyRef, ElementRef, Signal, effect, inject } from '@angular/core'

export const useEventListener = <T extends HTMLElement | undefined, K extends keyof HTMLElementEventMap>(
  target: Signal<T | ElementRef<T>>,
  eventName: K | K[],
  callback: (event: HTMLElementEventMap[K]) => void,
  options?: boolean | AddEventListenerOptions,
) => {
  const destroyRef = inject(DestroyRef)

  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
  const cleanups: Function[] = []
  const cleanup = () => {
    cleanups.forEach(fn => fn())
    cleanups.length = 0
  }

  const register = (
    el: EventTarget,
    event: K,
    listener: any,
    opts?: boolean | AddEventListenerOptions,
  ) => {
    el.addEventListener(event, listener, opts)
    return () => el.removeEventListener(event, listener, opts)
  }

  const effectRef = effect(() => {
    cleanup()
    const elValue = target()
    const el = elValue instanceof ElementRef ? elValue.nativeElement : elValue

    if (!el) {
      return
    }
    const events = Array.isArray(eventName) ? eventName : [eventName]

    events.forEach(evt => {
      cleanups.push(register(el, evt, callback, options))
    })
  })

  const stop = () => {
    cleanup()
    effectRef.destroy()
  }

  destroyRef.onDestroy(() => {
    cleanup()
  })

  return stop
}
