import { ElementRef, Signal, computed, effect, signal } from '@angular/core'
import { useIntersectionObserver } from './use-intersection-observer'

export interface UseInfiniteScrollOptions {
  /**
   * 触发加载的阈值，1 表示元素 100% 可见时触发
   * @default 1
   */
  threshold?: number
  canLoadMore?: Signal<boolean>
  /**
   * 滚动容器
   */
  scrollTarget?: Signal<HTMLElement | undefined>
}

export function useInfiniteScroll(
  sentinelRef: Signal<ElementRef<HTMLElement> | undefined>,
  onLoadMore: () => void,
  options: UseInfiniteScrollOptions = {},
) {
  const {
    threshold = 1,
    canLoadMore = signal(true),
    scrollTarget,
  } = options

  const isIntersecting = signal(false)

  // 计算是否应该触发加载更多
  const shouldTrigger = computed(() =>
    isIntersecting() && canLoadMore(),
  )

  const promise = signal<Promise<any> | null>(null)
  const isLoading = computed(() => !!promise())

  useIntersectionObserver(sentinelRef, entries => {
    let latestTime = 0
    for (const entry of entries) {
      if (entry.time >= latestTime) {
        latestTime = entry.time
        isIntersecting.set(entry.isIntersecting)
      }
    }
  }, {
    scrollTarget,
    threshold,
  })

  // 监听触发条件变化，当满足条件时调用 onLoadMore
  effect(() => {
    if (shouldTrigger()) {
      if (!promise()) {
        promise.set(
          Promise.all([
            onLoadMore(),
            new Promise(resolve => setTimeout(resolve, 100)),
          ])
            .finally(() => {
              promise.set(null)
            }),
        )
      }
    }
  })

  return {
    isIntersecting: isIntersecting.asReadonly(),
    shouldTrigger,
    isLoading,
  }
}
