import { DestroyRef, ElementRef, Signal, effect, inject } from '@angular/core'

export interface UseIntersectionObserverOptions {
  scrollTarget?: Signal<HTMLElement | undefined>
  /**
   * @default 1
   */
  threshold?: number
}

export function useIntersectionObserver(
  target: Signal<ElementRef<HTMLElement> | undefined>,
  callback: IntersectionObserverCallback,
  options: UseIntersectionObserverOptions = {},
) {
  const {
    scrollTarget,
    threshold = 1,
  } = options

  const destroyRef = inject(DestroyRef)

  let cleanup = () => {}

  effect(() => {
    const t = target()
    if (!t) return
    const targets = [t.nativeElement]
    const root = scrollTarget ? scrollTarget() : undefined
    cleanup()
    if (!targets.length) return

    const observer = new IntersectionObserver(
      callback,
      {
        root,
        threshold,
      },
    )

    targets.forEach(el => el && observer.observe(el))

    cleanup = () => {
      observer.disconnect()
      cleanup = () => {}
    }
  })

  destroyRef.onDestroy(() => {
    cleanup()
  })
}
