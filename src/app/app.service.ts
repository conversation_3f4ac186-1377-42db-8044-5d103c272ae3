import { DOCUMENT } from '@angular/common'
import { Inject, Injectable, signal } from '@angular/core'
import { Title } from '@angular/platform-browser'
import { debounceTime, distinctUntilChanged, fromEvent, map, startWith } from 'rxjs'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { UserService } from './modules/core/services/user.service'
import { GroupStudyClientService } from './modules/group-study/service/services/group-study-client.service'

@Injectable({
  providedIn: 'root',
})
export class AppService {
  private _token?: string
  isMockEnvironment = false

  private readonly MAX_PAGE_WIDTH = 820

  pageGap = signal(0)

  // TODO: 兼容官方组队，后续 token 都直接从 UserService 里拿
  get token() {
    return this.user.legacyToken
  }

  constructor(
    @Inject(DOCUMENT) private _doc: Document,
    private client: GroupStudyClientService,
    private title: Title,
    private user: UserService,
  ) {
    const _window = this._doc.defaultView || window
    fromEvent(_window, 'resize')
      .pipe(
        startWith(0),
        debounceTime(150),
        map(() => {
          if (_window.innerWidth <= this.MAX_PAGE_WIDTH) {
            return 0
          }
          return (_window.innerWidth - this.MAX_PAGE_WIDTH) / 2
        }),
        distinctUntilChanged(),
        takeUntilDestroyed(),
      ).subscribe(gap => {
        this.pageGap.set(gap)
      })
  }

  setTitle(title: string) {
    this.title.setTitle(title)
    this.client.clientSetPageTitle(title)
  }
}
