import { Activity } from '../modules/group-study-v2/models/activity.model'
import { Group } from '../modules/group-study-v2/models/group.model'
import { History } from '../modules/group-study-v2/models/history.model'
import { Member, UserBase } from '../modules/group-study-v2/models/member.model'
import { CalendarData, CalendarDateItem, DailyStatsData } from '../modules/group-study-v2/utils/study'

export interface StudyWorker {
  generateDailyStatsData(
    members: Member[],
    histories: History[],
    calendarItems: CalendarDateItem[],
    currentUser: UserBase,
  ): DailyStatsData

  storeStudyHistory(history: History[], dataMap: Map<number, Set<string>>): void

  generateCalendars(group: Group, activity: Activity): CalendarData
}
