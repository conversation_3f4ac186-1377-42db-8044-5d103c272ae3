/// <reference lib="webworker" />
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import { expose } from 'comlink'
import { History } from '../modules/group-study-v2/models/history.model'
import { ColorifyStrategy, DATE_FORMAT, StudyColorifier, generateStudyStatsFromRecord } from '../modules/group-study-v2/pages/study-detail/helper'
import { DailyStatsData, checkIsMemberExitedAtSomeTime } from '../modules/group-study-v2/utils/study'
import { fromSeconds, toStudyDate } from '../modules/shared/helper/time'
import { Group } from '../modules/group-study-v2/models/group.model'
import { Activity } from '../modules/group-study-v2/models/activity.model'
import { StudyWorker } from './study.type'
import type { Member, UserBase } from '../modules/group-study-v2/models/member.model'
import type { MemberDailyStudyStats } from '../modules/group-study-v2/pages/study-report/type'
import type { CalendarData, CalendarDateItem } from '../modules/group-study-v2/utils/study'

dayjs.extend(timezone)
dayjs.extend(utc)
dayjs.extend(duration)
dayjs.tz.setDefault('Asia/Shanghai')

const studyWorker: StudyWorker = {
  generateDailyStatsData(
    members: Member[],
    histories: History[],
    calendarItems: CalendarDateItem[],
    currentUser: UserBase,
  ): DailyStatsData {
    /**
   * 记录每天每个用户的学习记录
   *
   * {
   *  [DATE_FORMAT]: {
   *    [userId]: MemberDailyStudyStats
   *  }
   * }
   */
    const statsMap: Record<string, Record<number, MemberDailyStudyStats>> = {}

    let isCurrentUserInGroup = false

    /**
     * 将 histories 用 object 分类储存，以便后面查找，避免多次遍历
     * {
     *  [userId]: {
     *    [DATE_FORMAT]: History
     *  }
     * }
     */
    const historiesGroupByUserIdAndFormattedDate = histories.reduce((acc, record) => {
      const stored: Record<string, History> = acc[record.memberId] || {}
      const formattedDate = toStudyDate(record.createdTime).format(DATE_FORMAT)
      stored[formattedDate] = record
      acc[record.memberId] = stored
      return acc
    }, {} as Record<number, Record<string, History>>)

    const colorifier = new StudyColorifier()

    // 确保每一天每个用户都有值
    for (const calendarDate of calendarItems) {
      const { formattedDate } = calendarDate
      const studyStatsMap = statsMap[formattedDate] || {}
      let isDependingOnRewardedAmount = false

      colorifier.reset()

      // 获取每个用户的学习记录
      members.forEach((member, index) => {
        const memberStudyRecord = historiesGroupByUserIdAndFormattedDate[member.userId]?.[formattedDate]
        const isCurrentUser = member.isCurrentUser || member.userId === currentUser.userId
        const studyStats = memberStudyRecord
          ? generateStudyStatsFromRecord(memberStudyRecord, member)
          : {
              avatar: member.avatar,
              userId: member.userId,
              name: member.name,
              hasSigned: false,
              isFinished: false,
              isExited: checkIsMemberExitedAtSomeTime(member, calendarDate.date),
            }

        if (isCurrentUser && !isCurrentUserInGroup) {
          isCurrentUserInGroup = true
        }

        // 已退出的用户会排在队伍最后，因此不加入染色队伍
        (!studyStats.isExited) && colorifier.registerStats(studyStats)

        if (!isDependingOnRewardedAmount && studyStats.studyLog?.rewardReason === 'CUSTOM_FIRST_DAY') {
          isDependingOnRewardedAmount = true
        }

        studyStatsMap[member.userId] = studyStats
      })

      statsMap[formattedDate] = studyStatsMap

      colorifier.resolvePendingStats(
        isDependingOnRewardedAmount
          ? ColorifyStrategy.REWARD
          : ColorifyStrategy.SIGN_AVATAR,
      )
    }

    return {
      statsMap,
      isCurrentUserInGroup,
    }
  },

  storeStudyHistory(history: History[], dataMap: Map<number, Set<string>>) {
    history.forEach(v => {
      if (v.studySucceededTime) {
        let dataSet = dataMap.get(v.memberId)
        if (!dataSet) {
          dataMap.set(v.memberId, new Set())
          dataSet = dataMap.get(v.memberId)
        }

        if (dataSet) {
          const formattedDate = toStudyDate(v.studySucceededTime).format(DATE_FORMAT)
          !dataSet.has(formattedDate) && dataSet.add(formattedDate)
        }
      }
    })
  },

  generateCalendars(group: Group, activity: Activity): CalendarData {
    if (!group) {
      return {}
    }
    const activityConfig = activity?.config

    const result: CalendarData = {}

    // 确保是自建组队
    if (!activityConfig || 'registration_start_time' in activityConfig) {
      return result
    }
    const firstDate = toStudyDate(group.studyStartTime!).startOf('day')

    const calendars = Array.from({ length: fromSeconds(activityConfig.studyDuration, 'day') + 1 }, (_, i): CalendarDateItem => {
      const date = firstDate.clone().add(i, 'day')
      const formattedDate = date.format(DATE_FORMAT)
      const offsetToToday = Math.ceil(date.diff(toStudyDate(Date.now()), 'd', true))
      if (offsetToToday === 0) {
        result.todayIndex = i
      }

      return {
        date: date.toISOString(),
        formattedDate,
        offsetToToday,
        relativeIndex: i,
        desc: `第 ${i + 1} 天`,
      }
    })

    result.fullCalendars = calendars
    result.datesWithRecord = result.todayIndex !== undefined && result.todayIndex !== -1
      ? calendars.slice(0, result.todayIndex! + 1)
      : calendars

    if (calendars.length) {
      result.summaryDateItem = {
        date: '',
        formattedDate: `${calendars[0].formattedDate} - ${calendars[calendars.length - 1].formattedDate}`,
        offsetToToday: 999,
        relativeIndex: -2,
        desc: '累计获得',
      }
    }
    return result
  },

}

expose(studyWorker)
