import { formatTime } from '../handler'

const eventTypes = [
  'CREATE_GROUP',
  'JOIN_GROUP',
  'GROUP_FAILED',
  'TEST_FAILED',
  'GROUP_SUCCEEDED',
  'TEST_SUCCEEDED',
  'LEARN_SUCCEED_TODAY',
  'TICKLE',
  'LEARN_SUCCEEDED',
  'LIKE',
  'CLICK',
]

export type EventType = typeof eventTypes[number]

const tickleTypes = [
  'DONE_DONE',
  'DONE_UNDONE',
  'UNDONE_DONE',
  'UNDONE_UNDONE',
]

type TickleTypes = typeof tickleTypes[number]


export class Event {
  id: string
  activityId: string
  groupId: string
  creator: number
  eventType: EventType
  tickleType?: TickleTypes
  targetId?: string
  targetClass: string
  comment: string
  createdTime: string
  constructor(event: EventRes) {
    this.id = event._id
    this.activityId = event.activity_id
    this.groupId = event.group_id
    this.creator = event.creator
    this.eventType = event.event_type
    this.tickleType = event.tickle_type
    this.targetId = event.target_id
    this.targetClass = event.target_class
    this.comment = event.comment
    this.createdTime = formatTime(event.created_time)
  }
}

export interface EventRes {
  _id: string
  activity_id: string
  group_id: string
  // 创建者 id
  creator: number
  // 事件类型
  event_type: string
  // 拍一拍类型
  tickle_type?: string
  // 目标 id
  target_id?: string
  // 目标类别
  target_class: string
  // 内容
  comment: string
  created_time: string
}
