import { Injectable } from '@angular/core'
import { groupBy } from 'lodash-es'
import { map } from 'rxjs'
import { ApiService } from '../../core/services/api.service'
import { dayjsWithTZ } from '../../shared/helper/time'
import { Event, EventRes, EventType } from './event.model'

@Injectable({
  providedIn: 'root',
})
export class EventService {
  events: Event[] = []

  constructor(
    public api: ApiService,
  ) { }

  getGroupStatus(activityId: string, invitedCode?: string) {
    return this.api.getGroupStatus(activityId, invitedCode)
  }

  getEvents() {
    return this.events
  }

  getEventMap(events: Event[]): Record<string, Event[]> {
    return groupBy(events, (event: Event) => {
      return dayjsWithTZ(event.createdTime).format('YYYY-MM-DD')
    })
  }

  getEventByType(events: Event[], type: EventType): Event[] {
    return events.filter(it => it.eventType === type)
  }

  updateEvents(activityId: string) {
    return this.getGroupStatus(activityId).pipe(
      map(r => {
        this.events = r.events?.map((event: EventRes) => new Event(event))
      }),
    )
  }
}
