import { Group, GroupRes } from './group/group.model'
import { Event, EventRes } from './event/event.model'
import { HistoryRes } from './history/history.model'
import { History } from './history/history.model'

export class GroupInformation {
  group: Group | null
  history?: Array<History>
  currentUserId: number
  inviteUserId?: number
  inviteUserGroup?: Group
  events?: Array<Event>
  todayHistory: Array<History> = []

  constructor(groupInformation: GroupInformationRes) {
    this.group = groupInformation.group === null ? null : new Group(groupInformation.group)
    this.currentUserId = groupInformation.current_user_id
    this.inviteUserId = groupInformation.invite_user_id
    this.history = groupInformation.history ? groupInformation.history.map(history => new History(history)) : undefined
    this.events = groupInformation.events?.map((event: any) => new Event(event))
    this.inviteUserGroup = groupInformation.invite_user_group ? new Group(groupInformation.invite_user_group) : undefined
    //this.history = groupInformation.history?.map((history:any) => new History(history))
  }
}

export interface GroupInformationRes {
  group: GroupRes | null
  history?: Array<HistoryRes>
  events?: Array<EventRes>
  current_user_id: number
  invite_user_id?: number
  invite_user_group?: GroupRes
}
