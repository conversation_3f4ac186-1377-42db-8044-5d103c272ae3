import { Dayjs } from 'dayjs'
import { dayjsWithTZ } from '../shared/helper/time'

/**
 * 对于某些数据需要进行减去4小时的处理
 * @param value
 */
export const formatTime = function (value: string): string {
  return dayjsWithTZ(value).subtract(4, 'hours').toISOString()
}

/**
 * 因为业务中对与今天的定义是今日4点到次日凌晨4点，所以对于“今天”的定义需要专门判断
 * @param value
 */
export const isToday = function (value: string): boolean {
  const today = getNow()
  const time = dayjsWithTZ(value)
  return (time.month() == today.month()) && (time.date() == today.date())
}
/**
 * 这里的getNow是为了在业务中判断是否是今天而实现得到
 */
export const getNow = function (): Dayjs {
  return dayjsWithTZ().subtract(4, 'hours')
}

/**
 *  计算当前事件与现在时间的时间差，单位为毫秒
 */
export const getTimeDifference = function (targetTime: string, currentTime?: string): number {
  return getNow().valueOf() - dayjsWithTZ(targetTime).valueOf()
}


