import { Injectable } from '@angular/core'
import { groupBy } from 'lodash-es'
import { dayjsWithTZ } from '../../shared/helper/time'
import { isToday } from '../handler'
import { History } from './history.model'

@Injectable({
  providedIn: 'root',
})
export class HistoryService {

  getHistoryCreatedTime(history: History) {
    const temp = dayjsWithTZ(history.createdTime)
    return temp.month() + 1 + '.' + temp.date()
  }

  getHistoriesByDate(histories: History[]) {
    return groupBy(histories, this.getHistoryCreatedTime)
  }

  getTodayHistory(histories: History[]) {
    return histories.filter(history => isToday(history.createdTime))
  }

  getCertainDayHistory(histories: History[], dayIndex: number) {
    return Object.values(this.getHistoriesByDate(histories))[dayIndex]
  }
}
