import { formatTime } from '../handler'

const rewardReasons = [11, 12, 111, 112, 211, 123]
export type RewardReasons = typeof rewardReasons[number]

export class History {
  memberId: number
  deviceId: string
  isSigned: boolean
  signAvatar: number
  signDate?: string
  // 学习单词数达标时间
  studyVocSucceededTime?: string
  actualStudyVocSucceededTime?: string
  // 学习时长达标时间
  studyTimeSucceededTime?: string
  actualStudyTimeSucceededTime?: string
  // 学习成功时间
  studySucceededTime?: string
  actualStudySucceededTime?: string
  // 当日学习时长（秒）
  studyTime: number
  // 当日学习单词数
  learnedVocCount: number
  // 当日新学单词数
  learnedNewVocCount: number
  // 奖励类型
  rewardType: string
  // 奖励数量
  rewardCount: number
  // 奖励原因
  rewardReason: RewardReasons
  // 奖励状态
  rewardStatus: string
  // 撤回原因
  rewardCancelledReason: string
  createdTime: string
  updatedTime: string

  constructor(history: HistoryRes) {
    this.memberId = history.member_id
    this.deviceId = history.device_id
    this.isSigned = history.is_signed
    this.signAvatar = history.sign_avatar
    this.signDate = history.sign_date ? formatTime(history.sign_date) : undefined
    this.learnedVocCount = history.learned_voc_count
    this.learnedNewVocCount = history.learned_new_voc_count
    this.rewardType = history.reward_type
    this.rewardCount = history.reward_count
    this.rewardReason = history.reward_reason
    this.rewardStatus = history.reward_status
    this.rewardCancelledReason = history.reward_cancelled_reason
    this.createdTime =  formatTime(history.created_time)
    this.updatedTime = formatTime(history.updated_time)
    this.studyVocSucceededTime = history.study_voc_succeeded_time ? formatTime(history.study_voc_succeeded_time) : undefined
    this.studyTimeSucceededTime = history.study_time_succeeded_time ? formatTime(history.study_time_succeeded_time) : undefined
    this.studySucceededTime = history.study_succeeded_time ? formatTime(history.study_succeeded_time) : undefined
    this.actualStudyVocSucceededTime = history.study_voc_succeeded_time
    this.actualStudySucceededTime = history.study_time_succeeded_time
    this.actualStudySucceededTime = history.study_succeeded_time
    this.studyTime = history.study_time
  }
}

export interface HistoryRes {
  member_id: number
  device_id: string
  is_signed: boolean
  sign_avatar: number
  sign_date?: string
  study_voc_succeeded_time?: string
  study_time_succeeded_time?: string
  study_succeeded_time?: string
  study_time: number
  learned_voc_count: number
  learned_new_voc_count: number
  reward_type: string
  reward_count: number
  reward_reason: RewardReasons
  reward_status: string
  reward_cancelled_reason: string
  created_time: string
  updated_time: string
}
