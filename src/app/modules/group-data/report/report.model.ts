export class Report {
  constructor(report: any) {
    this.id = report._id
    this.activityId = report.activity_id
    this.creator = report.creator
    this.familiarVocCount = report.familiar_voc_count
    this.firstReadTime = report.first_read_time
    this.rewardedTime = report.rewarded_time
    this.createdTime = report.created_time
    this.updatedTime = report.updated_time
    this.groupId = report.group_id
  }
  id: string
  activityId: string
  groupId: string
  creator: number
  familiarVocCount: number
  firstReadTime: string | undefined
  rewardedTime: string | undefined
  createdTime: string
  updatedTime: string
}

export interface ReportRes {
  // 报告 Id
  _id: string
  // 活动 Id
  activity_id: string
  // 队伍 Id
  group_id: string
  // 创建者 id
  creator: number
  // 巩固单词数
  familiar_voc_count?: number
  // 上次阅读报告时间
  first_read_time?: string
  // 奖励领取时间
  rewarded_time?: string
  // 创建时间
  created_time: string
  // 更新时间
  updated_time: string
}
