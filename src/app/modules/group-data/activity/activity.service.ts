import { Injectable } from '@angular/core'
import { ApiService } from '../../core/services/api.service'
import { ACTIVE_GROUP_STATUS } from '../../shared/constants'
import { dayjsWithTZ } from '../../shared/helper/time'
import { Activity, ActivityRes } from './activity.model'

@Injectable({
  providedIn: 'root',
})
export class ActivityService {
  activity!: Activity
  activityId!: string
  userActivities: Array<Activity> = []
  constructor(
    public api: ApiService,
  ) {}

  isActivityToEnd(): boolean {
    return dayjsWithTZ().valueOf() > dayjsWithTZ(this.activity.config?.registrationEndTime).valueOf()
  }

  isActivityHasReport() {
    return idOfActivityWithoutReport.includes(this.activityId)
  }

  getActivities() {
    return this.api.getActivities()
  }

  getNextActiveActivity(activities: ActivityRes[], currentActivityId: string): ActivityRes | undefined {
    const activeActivities = activities
      .slice()
      .filter(v => {
        const isUserActiveInActivity = v.user_status === 'DOING' || v.user_status === 'UNREWARDED'
        const isGroupActive = v.user_activity && v.user_activity.group_study && ACTIVE_GROUP_STATUS.includes(v.user_activity.group_study.group_status)
        return isUserActiveInActivity && isGroupActive
      })
      .sort((a, b) => +new Date(b.created_time) - +new Date(a.created_time))

    if (activeActivities.length <= 1) {
      return undefined
    }

    const currentActivityIndex = activeActivities.findIndex(v => v._id === currentActivityId)
    if (currentActivityIndex === -1) {
      return undefined
    }
    const nextActivityIndex = currentActivityIndex === activeActivities.length - 1 ? 0 : currentActivityIndex + 1
    return activeActivities[nextActivityIndex]

  }

  getActivityDetail(activityId: string) {
    return this.api.getActivityDetail(activityId)
  }

  isLastActivity() {
    return this.activityId === this.userActivities.sort((a, b) => {
      return dayjsWithTZ(a.createdTime).valueOf() - dayjsWithTZ(b.createdTime).valueOf()
    })[0].id
  }

  getActivityId() {
    return this.activity.id
  }
}


export const activitiesExcludeTickle  = [
  '62de120c7ff2135a3e3a590e',
  '62d169ef5f0183d868dd9c41',
  '62bedbd3a441ec2f6113f2fd',
  '62ac4eaa22fb194321c1b201',
  '628ef2fb107f639b78787231',
  '627cc478f931332cc581c9ed',
  '62bc615979d4af4e51a5ab82',
]

export const idOfActivityWithoutReport = [
  '628ef2fb107f639b78787231',
  '627cc478f931332cc581c9ed',
  '62bc615979d4af4e51a5ab82',
]
