import { GroupStatus } from '../group/group.model'

class GroupStudyActivityConfig {
  // 报名开始时间
  registrationStartTime: string
  // 报名截止时间
  registrationEndTime: string
  // 磨合期时长 (秒)
  testingDuration: number
  // 学习期时长 （秒）
  studyDuration: number
  // 组队学习倒计时 （秒）
  groupDuration: number
  // 每日学习时长 (秒)
  dailyStudyDuration: number
  // 每日学习单词数
  dailyStudyVoc: number
  // 奖励数量
  rewardRule: RewardRule

  /**
   * 特殊活动
   */
  specialEvent?: string

  /**
   * 特殊活动配置 开始时间
   */
  specialEvent520StartDate?: string

  /**
   * 特殊活动配置 特殊时间
   */
  specialEvent520SpecialDate?: string

  /**
   * 特殊活动配置 结束时间
   */
  specialEvent520EndDate?: string

  constructor(config: GroupStudyActivityConfigRes) {
    this.registrationStartTime = config.registration_start_time
    this.registrationEndTime = config.registration_end_time
    this.testingDuration = config.testing_duration
    this.studyDuration = config.study_duration
    this.groupDuration = config.group_duration
    this.dailyStudyDuration = config.daily_study_duration
    this.dailyStudyVoc = config.daily_study_voc
    this.rewardRule = config.reward_rule
    this.specialEvent = config.special_event
    this.specialEvent520StartDate = config.special_event_520_start_date
    this.specialEvent520SpecialDate = config.special_event_520_special_date
    this.specialEvent520EndDate = config.special_event_520_end_date
  }
}

interface GroupStudyActivityConfigRes {
  // 报名开始时间
  registration_start_time: string
  // 报名截止时间
  registration_end_time: string
  // 磨合期时长 (秒)
  testing_duration: number
  // 学习期时长 （秒）
  study_duration: number
  // 组队学习倒计时 （秒）
  group_duration: number
  // 每日学习时长 (秒)
  daily_study_duration: number
  // 每日学习单词数
  daily_study_voc: number
  // 奖励数量
  reward_rule: RewardRule

  /**
   * 特殊活动
   */
  special_event?: string

  /**
   * 特殊活动配置 开始时间
   */
  special_event_520_start_date?: string

  /**
   * 特殊活动配置 特殊时间
   */
  special_event_520_special_date?: string

  /**
   * 特殊活动配置 结束时间
   */
  special_event_520_end_date?: string
}

export class Activity {
  // 活动 Id
  id: string
  // 活动名称
  name: string
  // 活动标题 活动圈用
  title: string

  creator?: number
  // 描述 活动圈用
  description: string
  decorate?: string
  decorateStyle?: string
  sealImg?: string
  isRead?: boolean
  type: string
  startTime: string
  endTime?: string
  activityStatus?: string
  config?: GroupStudyActivityConfig
  userStatus?: string
  eventCenterStatus: string
  createdTime: string
  updatedTime: string

  constructor(activityRes: ActivityRes) {
    this.id = activityRes._id
    this.name = activityRes.name
    this.title = activityRes.title
    this.description = activityRes.description
    this.decorate = activityRes.decorate
    this.decorateStyle = activityRes.decorate_style
    this.sealImg = activityRes.seal_img
    this.isRead = activityRes.is_read
    this.type = activityRes.type
    this.startTime = activityRes.start_time
    this.endTime = activityRes.end_time
    this.activityStatus = activityRes.activity_status
    this.config = activityRes.config &&  new GroupStudyActivityConfig(activityRes.config)
    this.userStatus = activityRes.user_status
    this.eventCenterStatus = activityRes.event_center_status
    this.createdTime = activityRes.created_time
    this.updatedTime = activityRes.updated_time
    this.creator = activityRes.creator
  }
}

export interface ActivityRes {
  _id: string
  name: string
  title: string
  description: string
  decorate?: string
  decorate_style?: string
  seal_img?: string
  is_read?: boolean
  type: string
  creator?: number
  start_time: string
  end_time?: string
  activity_status?: string
  config: GroupStudyActivityConfigRes
  user_status?: string
  user_activity?: {
    is_read?: boolean
    last_read_time: string
    group_study?: {
      group_status: GroupStatus
    }
    created_time: string
    updated_time: string
  }
  event_center_status: string
  created_time: string
  updated_time: string
}

interface RewardRule {
  pattern_11?: number
  pattern_12?: number
  pattern_111?: number
  pattern_112?: number
  pattern_211?: number
  pattern_123?: number
}
