import { formatTime } from '../handler'
import { Member, MemberRes } from './member.model'

const groupStatuses = [
  // 组队中
  'GROUPING',
  // 组队失败
  'GROUP_FAILED',
  // 组队成功
  'GROUP_SUCCEEDED',
  // 磨合期
  'TESTING',
  // 磨合失败
  'TEST_FAILED',
  // 学习期
  'LEARNING',
  // 学习成功
  'LEARN_SUCCEED',
  // 手动解散
  'GROUP_DISMISSED',
] as const

export type GroupStatus = typeof groupStatuses[number]

export class Group {
  groupId: string
  activityId: string
  name: string
  type: 'COUPLE' | 'TRIO' | 'CUSTOM'
  groupSucceededTime?: string
  studyStartTime?: string
  status: GroupStatus
  members: Array<Member>
  createdTime: string
  updatedTime?: string
  constructor(group: GroupRes) {
    this.groupId = group.group_id
    this.activityId = group.activity_id
    this.name = group.name
    this.type = group.type
    this.status = group.status
    this.members = group.members.map((member: any) => new Member(member))
    this.createdTime = formatTime(group.created_time)
    this.updatedTime = formatTime(group.updated_time)
    this.groupSucceededTime = group.group_succeeded_time ? formatTime(group.group_succeeded_time) : undefined
    this.studyStartTime = group.study_start_time ? formatTime(group.study_start_time) : undefined
  }
}

export interface GroupRes {
  id: string
  group_id: string
  activity_id: string
  name: string
  type: 'COUPLE' | 'TRIO'
  group_succeeded_time?: string
  study_start_time?: string
  status: GroupStatus
  members: Array<MemberRes>
  created_time: string
  updated_time: string
}
