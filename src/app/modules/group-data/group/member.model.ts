import { formatTime } from '../handler'
import { History } from '../history/history.model'

type MemberType = 'CREATOR' | 'PARTICIPANT'
export class Member {
  userId: number
  avatar: string
  name: string
  joinTime: string
  lastReadTime: string
  isCurrentUser: boolean
  totalAwards?: number
  totalVocCount?: number
  totalStudyTime?: number
  todayHistory?: History
  /**
   * 成员身份类型
   */
  type: MemberType

  constructor(member: MemberRes) {
    this.userId = member.user_id
    this.avatar = member.avatar
    this.name = member.name
    this.joinTime = formatTime(member.joined_time)
    this.lastReadTime = formatTime(member.last_read_time)
    this.isCurrentUser = member.is_current_user
    this.type = member.type
    //todo 等待重构history model修改
    //this.lastReadTime = formatTime(member.last_read_time)
  }
}

export interface MemberRes {
  user_id: number
  avatar: string
  name: string
  joined_time: string
  last_read_time: string
  is_current_user: boolean
  /**
   * 成员身份类型
   */
  type: MemberType
}
