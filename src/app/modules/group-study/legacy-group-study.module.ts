import { CommonModule } from '@angular/common'
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { SwiperModule } from 'swiper/angular'
import { DialogModule } from 'primeng/dialog'
import { ROUTE_BASE_HREF } from '../entry/group-study.module'
import { ClickDirective } from '../shared/directives/click.directive'
import { DbclickDirective } from '../shared/directives/dbclick.directive'
import { ToastDirective } from '../shared/directives/toast.directive'
import { GroupStudyCommonModule } from '../shared/modules/group-study/group-study-common.module'
import { CdnPipe } from '../shared/pipes/cdn.pipe'
import { ListPipe } from '../shared/pipes/list.pipe'
import { TestComponent } from '../test/components/test/test.component'
import { MemoIconComponent } from '../ui/icon/icon.component'
import { MemoMarqueeComponent } from '../ui/marquee/marquee.component'
import { ActivityGuideComponent } from './components/activity-guide/activity-guide.component'
import { AwardRuleComponent } from './components/award-rule/award-rule.component'
import { CalenderComponent } from './components/calender/calender.component'
import { CompletionStatusComponent } from './components/completion-status/completion-status.component'
import { DanmuComponent } from './components/danmu/components/danmu.component'
import { CountdownThreeHoursComponent } from './components/flip-clock/countdown-three-hours/countdown-three-hours.component'
import { CountdownComponent } from './components/flip-clock/countdown/countdown.component'
import { FlipClockComponent } from './components/flip-clock/flip-clock.component'
import { InteractiveBoardComponent } from './components/interactive-board/interactive-board.component'
import { EmotionComponent } from './components/interactive-board/message/emotion/emotion.component'
import { MessageComponent } from './components/interactive-board/message/message.component'
import { MedalWallerComponent } from './components/medal-waller/medal-waller.component'
import { PortraitsComponent } from './components/portraits/portraits.component'
import { TeamSuccessConfettiComponent } from './components/team-success-confetti/team-success-confetti.component'
import { TitleComponent } from './components/title/title.component'
import { TotalAwardComponent } from './components/total-award/total-award.component'
import { GroupStudyRoutingModule } from './group-study-routing.module'
import { GroupStudyComponent } from './group-study.component'
import { ActivityStepperComponent } from './pages/activity-stepper/activity-stepper.component'
import { ActivityTerminationComponent } from './pages/activity-termination/activity-termination.component'
import { AdjustPeriodFailedComponent } from './pages/adjust-period-failed/adjust-period-failed.component'
import { AdjustPeriodComponent } from './pages/adjust-period/adjust-period.component'
import { GroupFailedComponent } from './pages/group-failed/group-failed.component'
import { GroupSucceedComponent } from './pages/group-succeed/group-succeed.component'
import { InviteOthersComponent } from './pages/invite-others/invite-others.component'
import { JoinGroupComponent } from './pages/join-group/join-group.component'
import { LearningSucceedComponent } from './pages/learning-succeed/learning-succeed.component'
import { LearningComponent } from './pages/learning/learning.component'
import { StudyReportComponent } from './pages/study-report/study-report.component'
import { CreateGroupDialogComponent } from './components/create-group-dialog/create-group-dialog.component'

@NgModule({
  declarations: [
    AwardRuleComponent,
    ActivityGuideComponent,
    ActivityStepperComponent,
    ActivityTerminationComponent,
    InviteOthersComponent,
    GroupSucceedComponent,
    GroupFailedComponent,
    AdjustPeriodComponent,
    AdjustPeriodFailedComponent,
    LearningComponent,
    LearningSucceedComponent,
    JoinGroupComponent,
    CountdownComponent,
    FlipClockComponent,
    CountdownThreeHoursComponent,
    CompletionStatusComponent,
    GroupStudyComponent,
    CalenderComponent,
    TitleComponent,
    PortraitsComponent,
    TestComponent,
    TotalAwardComponent,
    MedalWallerComponent,
    ClickDirective,
    TeamSuccessConfettiComponent,
    CdnPipe,
    DanmuComponent,
    InteractiveBoardComponent,
    MessageComponent,
    DbclickDirective,
    ToastDirective,
    EmotionComponent,
    StudyReportComponent,
  ],
  imports: [
    CommonModule,
    GroupStudyRoutingModule,
    FormsModule,
    SwiperModule,
    MemoIconComponent,
    ListPipe,
    GroupStudyCommonModule,
    MemoMarqueeComponent,
    CreateGroupDialogComponent,
    DialogModule,
  ],
  providers: [
    {
      provide: ROUTE_BASE_HREF,
      useValue: '/group-study/v1/',
    },
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class LegacyGroupStudyModule { }
