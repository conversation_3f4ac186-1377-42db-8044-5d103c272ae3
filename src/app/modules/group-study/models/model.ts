/* eslint-disable camelcase */
import { Member } from '../../group-data/group/member.model'
import { History } from '../../group-data/history/history.model'

declare global {
  interface Window {
    Memo: any
    Tick: any
    randomIndex: number
  }
}

const groupStatuses = [
  'GROUPING',
  'LEARN_SUCCEED',
  'GROUP_SUCCEED',
  'TESTING',
  'TEST_FAILED',
  'LEARNING',
  'LEARN_FAILED',
  'GROUP_FAILED',
] as const

export type GroupStatus = typeof groupStatuses[number]

const rewardReasons = [11, 12, 111, 112, 211, 123]

export type RewardReasons = typeof rewardReasons[number]

export interface Reward {
  member_id: number
  reward_type: 'VOC'
  reward_count: number
  reward_reason: string
  created_time: string
  updated_time: string
  is_signed?: boolean
  isFinish?: boolean
}

export type Error = {
  code: string
  message: string
  info: string
}

export type SignDay = {
  reward_status?: string
  isToday?: boolean
  isTodayBefore?: boolean
  colorType?: ColorType
  specialSkin?: SpecialSkinConfig
  records: {
    user: Member
    currentRecord?: History
    calendarCardIconType?: CalendarCardIconType
  }[]
}

/**
 * @deprecated 官方组队使用这个类型，自建组队用 `ExColorType`
 */
export type ColorType = 'red' | 'yellow' | 'green' | 'normal' | 'opacity'

export type ColorTypeBase = 'red' | 'yellow' | 'green' | `green-${number}` | `grey-${number}`

/**
 * 之后会逐渐使用这个
 */
export type ExColorType = ColorTypeBase | `${ColorTypeBase}/${number}`

export type CalendarCardIconType = 'cross' | 'number'

export type MemberStatus = {
  isPlayTickAnimation?: boolean
  avatarBackgroundColor?: number[]
  memberId: number
  avatar: string
  name: string
  currentUserLastReadTime?: string
  isSign?: boolean
  todayHistory?: History
  learnedVocCountToday?: number
  studyTimeToday?: number
  createdTime?: string
  updateTime?: string
  initialSegment?: [number, number]
  signAvatar?: number
  isFinish?: boolean
}

export type SpecialSkinConfig = {
  /**
   * 头像文件名称 img_xxx.png img_xxx_grey.png
   */
  avatarName: string

  /**
   * rgb
   */
  avatarBackgroundColor: [number, number, number]

  calendarColor: ColorType
}
