import { Component, computed, inject, OnInit, signal, TemplateRef, viewChild, ViewChild } from '@angular/core'
import { ActivatedRoute, ActivationEnd, NavigationEnd, Router } from '@angular/router'
import { filter } from 'rxjs'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { environment } from '../../../environments/environment'
import { AppService } from '../../app.service'
import { ClientFrontendCommunicationService } from '../core/services/client-frontend-communication.service'
import { UserService } from '../core/services/user.service'
import { Activity, ActivityRes } from '../group-data/activity/activity.model'
import { ActivityService } from '../group-data/activity/activity.service'
import { EventService } from '../group-data/event/event.service'
import { TestService } from '../test/components/test/test.component'
import { MemoModalService } from '../ui/modal/modal.service'
import { InteractiveBoardComponent } from './components/interactive-board/interactive-board.component'
import { GroupStudyService } from './service/group-study.service'
import { TickleService } from './service/services/tickle.service'

@Component({
  selector: 'app-group-study',
  templateUrl: './group-study.component.html',
  styleUrls: ['./group-study.component.scss'],
  host: {
    '[style.--page-padding.px]': 'pagePadding()',
  },
  standalone: false,
})
export class GroupStudyComponent implements OnInit {
  private userService = inject(UserService)
  private modal = inject(MemoModalService)

  environment = environment
  isShowInteractiveBoard = false
  isShowTemp = true
  nextActiveActivity?: ActivityRes
  @ViewChild('boardRef') boardRef?: InteractiveBoardComponent
  createGroupDialog = viewChild('createGroupDialog', {
    read: TemplateRef,
  })

  get isShowRedDot() {
    return this.groupStudyService.status.isShowRedDot
  }

  get isShowInteractiveButton() {
    return this.groupStudyService.status.isShowInteractiveButton
  }

  get isDismissButtonVisible() {
    const group = this.groupStudyService.groupInformation?.group
    return group?.status === 'GROUPING' && group?.members?.length === 1 && group.members[0].type === 'CREATOR'
  }

  get isShowAwardRule() {
    return this.groupStudyService.isShowAwardRule
  }

  pagePadding = computed(() => this.app.pageGap())

  isSignUpDialogVisible = signal(false)

  constructor(
    public app: AppService,
    public router: Router,
    public route: ActivatedRoute,
    public groupStudyService: GroupStudyService,
    public testService: TestService,
    public tickle: TickleService,
    public eventService: EventService,
    public activityService: ActivityService,
    private cfc: ClientFrontendCommunicationService,
  ) {
    this.route.queryParams.pipe(
      filter(params => Object.keys(params).length > 0),
      takeUntilDestroyed(),
    )
      .subscribe(() => {
        this.handleActivityId()
        this.handleInviteCode()

        const activityId = this.groupStudyService.activityService.activityId
        if (activityId) {
          if (this.app.token) {
            this.groupStudyService.getActivityDetail(activityId).subscribe(it => {
              this.groupStudyService.activityService.activity = it.activity
              this.groupStudyService.activityService.activityId = it.activity.id || ''
              this.groupStudyService.setupSpecialEventConfigIfNeeded()
              this.groupStudyService.totalSignDayNumber = this.groupStudyService.getTotalSignDayNumber()
              this.groupStudyService.navigate(this.groupStudyService.invitedCode)
              this.activityService.getActivities().subscribe(({ activities = [] }) => {
                const nextActiveActivity = this.activityService.getNextActiveActivity(activities, it.activity.id)
                this.nextActiveActivity = nextActiveActivity
              })
            })
          }
        } else {
          this.groupStudyService.getActivities().subscribe({
            next: it => {
              const activities = it.activities
                .filter(v => v.type.startsWith('GROUP_STUDY') && v.type !== 'GROUP_STUDY_CUSTOM')
                .map(activity => new Activity(activity))

              this.groupStudyService.activityService.userActivities = activities
              if (activities.length > 0) {
                let activity
                const unRewarded = activities.filter(v => v.userStatus == 'UNREWARDED')
                if (this.groupStudyService.invitedCode) {
                  activity = activities[0]
                } else if (unRewarded.length !== 0) {
                  activity = unRewarded[unRewarded.length - 1]
                } else {
                  activity = activities[0]
                }
                this.groupStudyService.activityService.activity = activity
                this.groupStudyService.totalSignDayNumber = this.groupStudyService.getTotalSignDayNumber()
                this.groupStudyService.activityService.activityId = activity?.id ? activity?.id : ''
                this.groupStudyService.setupSpecialEventConfigIfNeeded()
                this.nextActiveActivity = this.activityService.getNextActiveActivity(it.activities, activity.id)
              }
              // this.router.navigateByUrl('/group-study/adjustPeriod')
              // this.groupStudyService.groupInformation = this.groupStudyService.formatGroupInformation(mockGroupInformation)
              // this.groupStudyService.isShowPage  = true
              this.groupStudyService.navigate(this.groupStudyService.invitedCode)
            },
            error: error => {
              if (error.status === 403) {
                this.router.navigateByUrl('/error', { skipLocationChange: true })
              }
            },
          })
        }
      })
  }

  ngOnInit() {
    this.groupStudyService.client.clientStartLoading()
    this.groupStudyService.mountClientFunction()
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd || event instanceof ActivationEnd),
    ).subscribe(e => {
      if (e instanceof NavigationEnd) {
        const it = e as NavigationEnd
        if (it.url.startsWith('/group-study/v1?')) {
          const groupStatus = this.groupStudyService.groupInformation?.group?.status
          if (groupStatus) {
            this.groupStudyService.navigateByGroupStatus(groupStatus)
          }
        }
      } else if (e instanceof ActivationEnd) {
        const it = e as ActivationEnd
        if (it.snapshot.children.length > 0) return
        this.groupStudyService.isShowAwardRule = !it?.snapshot.routeConfig?.data?.hideAwardRules
      }
    })
  }

  signUp() {
    if (this.userService.checkDebtLearning()) {
      return
    }
    // this.modal.create({
    //   component: CreateGroupDialogComponent,
    //   cssClass: ['rounded-modal', 'center-modal'],
    // })
    this.isSignUpDialogVisible.set(true)
  }

  handleInviteCode() {
    const invitedCode = new URL(window.location.href).searchParams.get('invitation_code')
    if (invitedCode) {
      this.groupStudyService.invitedCode = invitedCode
    }
  }

  handleActivityId() {
    let activityId = new URL(window.location.href).searchParams.get('activity_id')
    if (!activityId) {
      activityId = new URL(window.location.href).searchParams.get('activityId')
    }
    if (activityId) {
      this.groupStudyService.activityService.activityId = activityId
    }
  }

  openInteractiveBoard() {
    if (this.isShowRedDot) {
      this.isShowTemp = false
    }
    this.eventService.updateEvents(this.activityService.activityId).subscribe(() => {
      this.boardRef?.setContext()
      this.isShowInteractiveBoard = true
      this.groupStudyService.track('CLICK', 'INTERACTIVE_BOARD')
    })
  }

  closeInteractiveBoard() {
    this.isShowInteractiveBoard = false
  }

  onClickSwitchGroup() {
    const isCustomGroup = this.nextActiveActivity!.type === 'GROUP_STUDY_CUSTOM'
    const routePrefix = 'group-study'
    const routeCommands: string[] = isCustomGroup
      ? [routePrefix, 'v2']
      : [routePrefix, 'v1']

    this.router.navigate(routeCommands, {
      queryParams: {
        activity_id: this.nextActiveActivity!._id,
        invitation_code: null,
      },
      queryParamsHandling: 'merge',
      replaceUrl: true,
    })
  }

  onClickDismissGroup() {
    this.cfc.clientAlert({
      title: '你确定要解散队伍吗？',
      message: '',
      title_color: '#DC663E',
      buttons: [
        {
          id: 'cancel',
          text: '再想想',
        },
        {
          id: 'confirm',
          text: '确定',
        },
      ],
      button_callback: buttonId => {
        if (buttonId !== 'confirm') {
          return
        }
        this.groupStudyService.dismissGroup().subscribe(() => {
          this.cfc.clientToast('解散成功')
          setTimeout(() => {
            location.replace(`https://www.maimemo.com/pages/event-center/group-study-list?token=${this.userService.legacyToken}&ref=activity-center`)
          }, 1000)
        })
      },
    })
  }
}
