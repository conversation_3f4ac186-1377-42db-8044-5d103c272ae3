/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import { Injectable, signal } from '@angular/core'
import { Router } from '@angular/router'
import { Dayjs } from 'dayjs'
import { cloneDeep, isEqual } from 'lodash-es'
import { interval, map, Observable, switchMap, throwError } from 'rxjs'
import { AppService } from '../../../app.service'
import { ApiService } from '../../core/services/api.service'
import { Activity } from '../../group-data/activity/activity.model'
import { ActivityService } from '../../group-data/activity/activity.service'
import { EventType } from '../../group-data/event/event.model'
import { EventService } from '../../group-data/event/event.service'
import { GroupInformation } from '../../group-data/group-information.model'
import { Report } from '../../group-data/report/report.model'
import { dayjsWithTZ } from '../../shared/helper/time'
import { TotalAward } from '../components/total-award/total-award.component'
import { MemberStatus, SignDay, SpecialSkinConfig } from '../models/model'
import { AdjustPeriodComponent } from '../pages/adjust-period/adjust-period.component'
import { LearningSucceedComponent } from '../pages/learning-succeed/learning-succeed.component'
import { LearningComponent } from '../pages/learning/learning.component'
import { ABTestingHelper } from '../../core/services/ab-testing.service'
import { TEST_GROUP_NATIONAL_DAY_2024 } from '../../shared/constants'
import { UserService } from '../../core/services/user.service'
import { GroupStudyClientService } from './services/group-study-client.service'
import { HelperService } from './services/helper.service'
import { StatusService } from './services/status.service'

@Injectable({
  providedIn: 'root',
})
export class GroupStudyService {
  signDays?: SignDay[] = []
  memberStatus?: MemberStatus[]
  lastSignDayInfo?: SignDay
  currentRule?: 'COUPLE' | 'TRIO' = 'TRIO'
  initialFinishStatus?: boolean[]
  isTodayBefore = true
  individualTotalAward?: TotalAward[] = []
  isShowAwardRule = true
  reports: Report[] = []
  private readonly routeBase = '/group-study/v1/'

  isNationalDayThemeEnable = signal(false)

  get groupId() {
    return this.groupInformation?.group?.groupId
  }

  /**
   * 特殊节日活动对应的皮肤配置
   */
  private readonly specialSkinDict: Record<string, SpecialSkinConfig> = {
    valentine: {
      avatarName: 'heart',
      avatarBackgroundColor: [247, 141, 141],
      calendarColor: 'red',
    },
  }

  private registeredSpecialSkinDict: Record<string, SpecialSkinConfig> = {}

  /**
   * 获取特殊节日活动头像
   * @param date
   */
  getSpecialSkin = (date: Dayjs): SpecialSkinConfig | undefined => {
    const formated = date.format('YYYY-MM-DD')
    return this.registeredSpecialSkinDict[formated]
  }

  constructor(
    public api: ApiService,
    public helper: HelperService,
    public status: StatusService,
    public router: Router,
    public app: AppService,
    public client: GroupStudyClientService,
    public activityService: ActivityService,
    public eventService: EventService,
    private abTesting: ABTestingHelper,
    private userService: UserService,
  ) {
    this.groupTypeAndTeamNumMap.set('COUPLE', 2)
    this.groupTypeAndTeamNumMap.set('TRIO', 3)

    this.abTesting.getTestGroup(TEST_GROUP_NATIONAL_DAY_2024)
      .subscribe(({ group }) => {
        this.isNationalDayThemeEnable.set(group === 'ON')
      })
  }

  isTestStatus = false
  invitedCode?: string
  token = ''
  groupInformation?: GroupInformation
  groupTypeAndTeamNumMap: Map<string, number> = new Map<string, number>()
  isShowSignUpButton = false
  isShowPage = false
  totalSignDayNumber = 0

  /**
   * 根据当前队伍状态进行跳转
   * @param invitedCode 当前invitedCode为undefined不会依赖邀请码进行跳转
   */
  navigate(invitedCode?: string): void {
    const activityId = this.activityService.activityId
    if (invitedCode) {
      this.getGroupStatus(activityId, invitedCode).subscribe(groupInformation => {
        groupInformation = new GroupInformation(groupInformation)
        this.setServices(groupInformation)
        const inviteUserActivityId = groupInformation.inviteUserGroup?.activityId
        if (!activityId && !inviteUserActivityId) {
          console.error('activityId or inviteUserActivityId is null')
        }
        this.getActivityDetail(activityId || inviteUserActivityId).subscribe(it => {
          this.activityService.activity = it.activity
          this.activityService.activityId = it.activity.id || ''
          this.setupSpecialEventConfigIfNeeded()
          this.totalSignDayNumber = this.getTotalSignDayNumber()
          this.navigateByInvitationCode(groupInformation)
        },
        error => {
          if (error.status == 403) {
            this.router.navigateByUrl('/error', { skipLocationChange: true })
          }
          throw error
        },
        () => {
          this.status.setIsShowRedDot()
          if (this.groupInformation) {
            this.status.setIsShowInteractiveButton(this.groupInformation, this.activityService.activityId, this.invitedCode)
          }
        },
        )
      })
    } else {
      // 不通过邀请码获取队伍信息的情况
      this.getGroupStatus(this.activityService.activityId).subscribe(it => {
        this.api.updateActivityStatus(this.activityService.activity?.id)
        if (it.group == null) {
          this.router.navigateByUrl(`${this.routeBase}activityStepper`, { skipLocationChange: true })
          this.isShowPage = true
          this.client.clientEndLoading()
          this.isShowSignUpButton = true
          return
        }
        it = new GroupInformation(it)
        this.setServices(it)
        // todo 待优化
        this.eventService.events = it.events
        this.groupInformation = this.formatGroupInformation(it)
        this.setTitle()
        this.navigateByGroupStatus(this.groupInformation.group!.status)
      },
      error => {
        throw error
      },
      () => {
        this.status.setIsShowRedDot()
        if (this.groupInformation) {
          this.status.setIsShowInteractiveButton(this.groupInformation!, this.activityService.activityId, this.invitedCode)
        }
      },
      )
    }
  }

  /**
   * 根据队伍状态信息进行跳转
   * @param status
   */
  navigateByGroupStatus(status: string): void {
    if (this.activityService.isActivityToEnd() && (status === 'TEST_FAILED' || status === 'GROUP_FAILED')) {
      this.router.navigateByUrl(`${this.routeBase}activityTermination`, { skipLocationChange: true })
    } else {
      switch (status) {
        case 'GROUPING':
          this.router.navigateByUrl(`${this.routeBase}inviteOthers`, { skipLocationChange: true })
          break
        case 'GROUP_SUCCEEDED':
          this.router.navigateByUrl(`${this.routeBase}groupSucceed`, { skipLocationChange: true })
          break
        case 'GROUP_DISMISSED':
          this.reSignUp()
          break
        case 'GROUP_FAILED':
          this.router.navigateByUrl(`${this.routeBase}groupFailed`, { skipLocationChange: true })
          break
        case 'TESTING':
          this.router.navigateByUrl(`${this.routeBase}adjustPeriod`, { skipLocationChange: true })
          break
        case 'TEST_FAILED':
          this.router.navigateByUrl(`${this.routeBase}adjustPeriodFailed`, { skipLocationChange: true })
          break
        case 'LEARNING':
        case 'LEARN_SUCCEED':
          this.router.navigateByUrl(`${this.routeBase}learning`, { skipLocationChange: true })
          break
      }
    }
    this.client.clientEndLoading()
    this.isShowPage = true
  }

  /**
   * 同步网页title tag
   */
  setTitle() {
    const title = this.groupInformation?.group?.name ? this.groupInformation.group.name : '组队学习'
    document.getElementsByTagName('title')[0].innerText = title
    this.client.clientSetPageTitle(title)
  }

  formatGroupInformation(groupInformation: GroupInformation) {
    groupInformation.history?.forEach(it => {
      if (it.rewardStatus == 'CANCELLED') {
        it.rewardCount = 0
      }
    })
    groupInformation.todayHistory = groupInformation.history!.filter(it => {
      return this.helper.isToday(dayjsWithTZ(it.createdTime))
    })

    groupInformation.group?.members.forEach(it => {
      it.todayHistory = groupInformation.todayHistory?.find(item => item.memberId == it.userId)
    })

    return groupInformation
  }

  /**
   * 将每个成员的当天最近的学习记录与该成员绑定起来，方便视图渲染
   */
  generateMemberWithStudyRecord(groupInformation?: GroupInformation) {
    if (groupInformation) {
      this.groupInformation = groupInformation
    }
    const retArray = [] as MemberStatus[]
    const currentUserId = this.groupInformation?.currentUserId
    const currentUserLastReadTime = this.groupInformation?.group!.members.find(it => it.userId === currentUserId)?.lastReadTime

    this.groupInformation?.group!.members.forEach((it, index) => {
      const tempMemberStatus: MemberStatus = {
        currentUserLastReadTime: currentUserLastReadTime,
        memberId: it.userId,
        avatar: it.avatar,
        name: it.name,
      }
      const tempTodayHistory = groupInformation?.todayHistory!.find(item => item.memberId == it.userId)
      tempMemberStatus.todayHistory = tempTodayHistory
      tempMemberStatus.isSign = tempTodayHistory?.isSigned
      tempMemberStatus.isFinish = !!tempTodayHistory?.studySucceededTime
      tempMemberStatus.learnedVocCountToday = tempTodayHistory?.learnedNewVocCount! + tempTodayHistory?.learnedVocCount!
      tempMemberStatus.studyTimeToday = tempTodayHistory?.studyTime
      tempMemberStatus.createdTime = tempTodayHistory?.createdTime
      tempMemberStatus.updateTime = tempTodayHistory?.updatedTime
      tempMemberStatus.signAvatar = tempTodayHistory?.signAvatar
      retArray.push(tempMemberStatus)
    })
    return retArray
  }

  getActivityDetail(activityId: string) {
    return this.activityService.getActivityDetail(activityId).pipe(
      map(it => {
        const activity = new Activity(it.activity)
        return {
          activity: activity,
          group_study: it.group_study,
        }
      },
      ),
    )
  }

  getActivities() {
    return this.activityService.getActivities()
  }

  reSignUp(): void {
    this.isShowSignUpButton = true
    this.status.isShowInteractiveButton = false
    this.groupInformation!.group!.name = '组队学习'
    this.setTitle()
    this.router.navigateByUrl(`${this.routeBase}activityStepper`, { skipLocationChange: true })
  }

  navigateByInvitationCode(groupInformation: GroupInformation) {
    this.isShowPage = true
    this.client.clientEndLoading()
    this.groupInformation = groupInformation

    // 用户处于欠款状态，不支持官方组队
    if (this.userService.checkDebtLearning()) {
      this.navigate()
      return
    }

    // 如果用户已有队伍
    if (groupInformation.group != null) {
      // 如果队伍状态为失败
      if (
        groupInformation.group.status === 'GROUP_FAILED'
        || groupInformation.group.status === 'TEST_FAILED'
        || groupInformation.group.status === 'GROUP_DISMISSED'
      ) {
        // 如果邀请者队伍存在
        if (groupInformation.inviteUserGroup) {
          if (groupInformation.inviteUserGroup.activityId !== groupInformation.group.activityId) {
            this.navigate()
            this.client.clientAlertJoinFailed('提示', '暂无参与权限', 'group_already_joined')
            return
          }
          // 队伍满员的情况
          if (groupInformation?.inviteUserGroup?.members?.length == this.groupTypeAndTeamNumMap.get(groupInformation.inviteUserGroup?.type!)) {
            this.router.navigateByUrl(`${this.routeBase}activityStepper`, { skipLocationChange: true })
            this.isShowSignUpButton = true
            this.client.clientAlertJoinFailed('提示', '来晚了，你所加入的队伍已满员，报名期内你可以自己创建队伍', 'group_not_found')
            return
          }
          if (groupInformation.group.groupId == groupInformation.inviteUserGroup?.groupId) {
            this.navigate()
            this.client.clientAlertJoinFailed('提示', '你已经在此队伍中', 'group_already_joined')
            return
          }
          // 正常情况
          this.groupInformation!.group = groupInformation.inviteUserGroup
          this.router.navigateByUrl(`${this.routeBase}joinGroup`, { skipLocationChange: true })
          return
        } else {
          // 如果邀请者队伍不存在
          this.router.navigateByUrl(`${this.routeBase}activityStepper`, { skipLocationChange: true })
          this.isShowSignUpButton = true
          this.client.clientAlertJoinFailed('提示', '该队伍不存在', 'group_not_found')
          return
        }
      }

      // 邀请者队伍不存在
      if (groupInformation.inviteUserGroup == null) {
        this.navigate()
        this.client.clientAlertJoinFailed('提示', '你已有队伍，不能加入其他队伍', 'group_already_joined')
        return
      } else {
        // 已经在此队伍的情况
        if (groupInformation.group.groupId == groupInformation.inviteUserGroup?.groupId) {
          this.navigate()
          this.client.clientAlertJoinFailed('提示', '你已经在此队伍中', 'group_already_joined')
          return
        } else {
          // 两个都是官方组队
          if (groupInformation.group.type !== 'CUSTOM' && groupInformation.inviteUserGroup?.type !== 'CUSTOM') {
            this.navigate()
            this.client.clientAlertJoinFailed('提示', '你已有队伍，不能加入其他队伍', 'group_already_joined')
            return
          }

          // 一个官方组队，一个自建组队
          this.groupInformation.group = groupInformation.inviteUserGroup!
          this.router.navigateByUrl(`${this.routeBase}joinGroup`, { skipLocationChange: true })
          return
        }
      }
    } else {
      // 如果用户自己没有队伍
      if (groupInformation.inviteUserGroup == null) {
        // 如果邀请者不存在队伍
        this.router.navigateByUrl(`${this.routeBase}activityStepper`, { skipLocationChange: true })
        this.isShowSignUpButton = true
        this.client.clientAlertJoinFailed('提示', '该队伍不存在', 'group_not_found')
        return
      }

      // 队伍满员的情况
      if (groupInformation?.inviteUserGroup?.members?.length == this.groupTypeAndTeamNumMap.get(groupInformation.inviteUserGroup?.type!)) {
        this.router.navigateByUrl(`${this.routeBase}activityStepper`, { skipLocationChange: true })
        this.isShowSignUpButton = true
        this.client.clientAlertJoinFailed('提示', '来晚了，你所加入的队伍已满员', 'group_not_found')
        return
      }
      this.groupInformation = groupInformation
      if (!this.groupInformation) {
        return
      }

      // 正常情况
      this.groupInformation.group = groupInformation.inviteUserGroup!
      this.router.navigateByUrl(`${this.routeBase}joinGroup`, { skipLocationChange: true })
    }
  }

  getIntervalGetGroupStatus() {
    return interval(2000).pipe(
      // 取消超过2s的请求结果
      switchMap(() => {
        return this.getGroupStatus(this.activityService.activityId)
      }),
    )
  }

  getTotalSignDayNumber() {
    return (this.activityService.activity?.config?.studyDuration! + this.activityService.activity?.config?.testingDuration!) / 3600 / 24
  }

  getSignDays(that: LearningComponent | AdjustPeriodComponent | LearningSucceedComponent): SignDay[] {
    that.firstDayRewardHistory = dayjsWithTZ(that.groupInformation?.group!.studyStartTime)
    this.isTodayBefore = true
    const retArray = []
    // 就算没有签到数据，也要强制生成14个calendar card,
    for (let i = 0; i < 14; i++) {
      const temp = cloneDeep(that.firstDayRewardHistory)
      const nextDay = cloneDeep(temp?.add(i, 'days'))
      const tempRewards = that.groupInformation?.history?.filter(it => {
        return nextDay?.date() === dayjsWithTZ(it.createdTime).date()
      })
      const isToday = this.helper.isToday(nextDay!)
      this.isTodayBefore = this.getIsTodayBefore(nextDay!)
      const tempSignDay: SignDay = {
        records: [],
        isToday: isToday,
        isTodayBefore: this.isTodayBefore,
      }
      if (this.helper.isToday(nextDay!)) {
        tempSignDay.records = []
        that.groupInformation?.group!.members?.forEach(it => {
          const tempCurrentRecord = it.todayHistory
          tempSignDay.records.push({
            user: it,
            currentRecord: tempCurrentRecord,
          })
        })
      } else {
        tempSignDay.records = []
        that.groupInformation?.group!.members?.forEach(it => {
          const tempCurrentRecord = that.getCurrentUserRewardCount(it.userId, tempRewards!)
          tempSignDay.records.push({
            user: it,
            currentRecord: tempCurrentRecord,
          })
        })
        tempSignDay.reward_status = tempSignDay.records[0]?.currentRecord?.rewardStatus
      }

      const specialSkin = this.getSpecialSkin(nextDay)
      if (specialSkin) {
        tempSignDay.specialSkin = specialSkin
      }

      retArray.push(tempSignDay)
    }

    return retArray
  }

  setTimerTask(that: LearningComponent | AdjustPeriodComponent): void {
    let initialUpdateStatus = that.groupInformation?.todayHistory?.map(it => {
      return {
        userId: it.memberId,
        studyTime: it.studyTime,
        learnedVocCount: it.learnedVocCount,
        learnedNewVocCount: it.learnedNewVocCount,
        signAvatar: it.signAvatar,
      }
    })
    that.timer = this.getIntervalGetGroupStatus().subscribe(
      it => {
        it = new GroupInformation(it)
        this.setServices(it)
        const tempGroupInformation = this.formatGroupInformation(it)
        const updateStatus = tempGroupInformation.todayHistory?.map(it => {
          return {
            userId: it.memberId,
            studyTime: it.studyTime,
            learnedVocCount: it.learnedVocCount,
            learnedNewVocCount: it.learnedNewVocCount,
            signAvatar: it.signAvatar,
          }
        })
        if (isEqual(initialUpdateStatus, updateStatus)) {
          return
        }
        initialUpdateStatus = updateStatus
        that.groupInformation = tempGroupInformation
        that.signDays = this.getSignDays(that)
        this.signDays = that.signDays
        that.actualSignDays = this.signDays?.filter(it => it.isTodayBefore)
        if (that.groupInformation.group?.status == 'TESTING') {
          that.actualSignDays = this.signDays
        }
      },
      () => {
        that.timer?.unsubscribe()
      })
  }

  getIsTodayBefore(nextDay: Dayjs) {
    const temp = cloneDeep(nextDay)
    if (!this.isTodayBefore || this.helper.isToday(temp.subtract(1, 'days'))) {
      this.isTodayBefore = false
    }
    return this.isTodayBefore
  }

  getGroupStatus(activityId: string, invitedCode?: string) {
    return this.eventService.getGroupStatus(activityId, invitedCode)
  }

  track(event_type: EventType, target?: string): void {
    this.api.track({ event_type: event_type, group_id: this.groupInformation?.group?.groupId!, target: target }).subscribe()
  }

  getGroupInvitation(groupId: string): Observable<{ invitation_code: string }> {
    return this.api.getGroupInvitation(groupId)
  }

  askForJoiningGroup(invitationCode: string) {
    return this.api.askForJoiningGroup({ invitation_code: invitationCode, is_try: false })
  }

  receiveReward() {
    return this.api.receiveReward(this.groupInformation?.group?.groupId!)
  }

  getReport() {
    return this.api.getReport(this.groupInformation?.group?.groupId!)
  }

  enroll(param: { name: string; type: string }) {
    return this.api.enroll(param)
  }

  mountClientFunction() {
    this.client.mountClientFunction(this)
  }

  // todo
  setServices(groupInformation: GroupInformation) {
    this.eventService.events = groupInformation.events ?? []
  }

  /**
   * 在每次 activity 更新后执行
   */
  setupSpecialEventConfigIfNeeded() {
    const { specialEvent, specialEvent520SpecialDate } = this.activityService.activity?.config ?? {}

    if (specialEvent === '520' && specialEvent520SpecialDate) {
      const formattedDate = dayjsWithTZ(specialEvent520SpecialDate).format('YYYY-MM-DD')
      if (formattedDate in this.registeredSpecialSkinDict) {
        return
      }
      this.registeredSpecialSkinDict[formattedDate] = this.specialSkinDict.valentine
    }
  }

  dismissGroup() {
    const groupId = this.groupInformation?.group?.groupId
    if (!groupId) {
      return throwError(() => new Error('没有队伍信息'))
    }
    return this.api.dismissGroup(groupId)
  }
}
