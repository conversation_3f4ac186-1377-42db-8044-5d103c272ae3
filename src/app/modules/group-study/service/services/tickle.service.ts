import { Injectable } from '@angular/core'
import { ABTestingService } from '../../../core/services/abtesting.service'
import { ApiService } from '../../../core/services/api.service'
import { activitiesExcludeTickle, ActivityService } from '../../../group-data/activity/activity.service'
import { GroupStudyService } from '../group-study.service'
import { GroupStudyClientService } from './group-study-client.service'
import { StatusService } from './status.service'

@Injectable({
  providedIn: 'root',
})
export class TickleService {
  constructor(
    public api: ApiService,
    public client: GroupStudyClientService,
    public status: StatusService,
    public groupStudyService: GroupStudyService,
    public activityService: ActivityService,
    public abTest: ABTestingService,
  ) {
  }

  tickle(speakerId: string) {
    const group = this.groupStudyService.groupInformation?.group
    if (!group) {
      return
    }
    const tickleInGroupA = () => {
      return new Promise(r => {
        const groupInformation = this.groupStudyService.groupInformation
        if (!groupInformation) {
          return
        }
        if (this.isCanNotTickle()) {
          return
        }
        const group = groupInformation!.group!
        const groupId = group.groupId
        const targetName = group.members.find(it => it.userId.toString() == speakerId)?.name
        this.client.clientVibrate()
        this.api.track({
          group_id: groupId,
          event_type: 'TICKLE',
          target: speakerId,
        }).subscribe(
          () => {
            this.status.toastContent = `我 拍了拍 **${targetName}**`
            r('success')
          },
          e => {
            if (e.status === 0) {
              this.status.toastContent = '网络错误'
              return
            }
            this.status.toastContent = this.getTickleErrorMessage(e.error.errors[0].code)
          })
      })
    }

    const tickleInGroupB = () => {}
    return this.abTest.tickleInABTest(group.groupId, tickleInGroupA, tickleInGroupB)
  }

  getTickleErrorMessage(code: string): string {
    const errorMap = {
      event_tickle_type_error: '拍一拍类型有误',
      event_tickle_target_1error: '拍一拍目标不存在',
      event_tickle_target_is_oneself: '不能拍自己哦',
      event_tickle_exceeded_limit_times_today: '你今天已达提醒 TA 的次数',
      group_not_learning: '该队伍不处于学习期',
    } as Record<string, string>
    return errorMap[code] || '拍一拍失败'
  }

  isCanNotTickle() {
    return activitiesExcludeTickle.some(activityId => activityId === this.activityService.activityId)
  }
}
