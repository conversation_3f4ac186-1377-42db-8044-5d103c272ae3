import { Injectable } from '@angular/core'
import { ABTestingService } from '../../../core/services/abtesting.service'
import { activitiesExcludeTickle } from '../../../group-data/activity/activity.service'
import { Event } from '../../../group-data/event/event.model'
import { EventService } from '../../../group-data/event/event.service'
import { GroupInformation } from '../../../group-data/group-information.model'
import { dayjsWithTZ } from '../../../shared/helper/time'

@Injectable({
  providedIn: 'root',
})
export class StatusService {
  constructor(
    public eventService: EventService,
    public abTest: ABTestingService,
  ) { }
  isShowReport = false
  isShowRedDot = false
  isShowInteractiveButton = false

  private _toastContent = ''
  get toastContent() {
    return this._toastContent
  }
  set toastContent(value) {
    this._toastContent = value
    setTimeout(() => {
      this._toastContent = ''
    }, 3000)
  }

  setIsShowRedDot() {
    const lastClickEventBoardEventTime = this.eventService.events?.
      filter(it => it.targetId === 'INTERACTIVE_BOARD' || it.eventType === 'LIKE').
      slice(-1)[0]?.createdTime
    if (!lastClickEventBoardEventTime) {
      this.isShowRedDot = true
      return
    }
    const eventsExcludeClickBoard = this.eventService.events?.filter(it => it.targetId !== 'INTERACTIVE_BOARD') as Event[]
    if (eventsExcludeClickBoard?.length === 0) {
      this.isShowRedDot = false
      return
    }
    const finalEventTime = eventsExcludeClickBoard[eventsExcludeClickBoard.length - 1].createdTime
    this.isShowRedDot = dayjsWithTZ(finalEventTime).valueOf() > dayjsWithTZ(lastClickEventBoardEventTime).valueOf()
  }

  setIsShowInteractiveButton(groupInformation: GroupInformation, activityId: string, invitedCode?: string) {
    const group = groupInformation.group
    if (!group) {
      this.isShowInteractiveButton = false
      return
    }
    const groupStatus = group.status
    const allowStatus = ['GROUPING', 'LEARN_SUCCEED', 'GROUP_FAILED', 'TESTING', 'TEST_FAILED', 'LEARNING']
    const banActivityIds = activitiesExcludeTickle
    const currentActivityId = activityId

    this.isShowInteractiveButton =
      this.abTest.isShowInteractiveButtonInABTest(group.groupId) &&
      allowStatus.some(it => it == groupStatus) &&
      !banActivityIds.some(it => it == currentActivityId) &&
      !invitedCode
  }
}
