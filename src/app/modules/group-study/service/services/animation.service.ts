import { Injectable, computed } from '@angular/core'
import { cloneDeep } from 'lodash-es'
import { replaceColor } from 'lottie-colorify'
import lottie, { AnimationConfigWithData, AnimationConfigWithPath, AnimationItem, AnimationSegment } from 'lottie-web'
import { avatarAnimationData } from '../../../../../assets/animations/avatarAnimation'
import { numberAnimation } from '../../../../../assets/animations/numberAnimation'
import { tickAnimationData } from '../../../../../assets/animations/tickAnimation'
import { AppService } from '../../../../app.service'
import { Group } from '../../../group-data/group/group.model'
import { getColorFromColorType } from '../../../shared/helper/color'
import { SpecialSkinConfig } from '../../models/model'
import { GroupStudyService } from '../group-study.service'

type animationType = 'avatar' | 'tick' | 'avatarMove' | 'calendar' | 'shine'

class AnimationDef {
  constructor(type: animationType) {
    this.type = type
  }

  type = ''
  play = (config: any, onStop: () => void) => {
    onStop()
  }
}

export class Animation {
  name = ''
  type = ''
  config: {
    reward_count?: string
    sign_avatar?: number
    is_signed?: boolean
    study_succeeded_time?: string
    element?: Element
    initFrame?: number[]
    day?: number
    user?: number
    animationColor?: number[]
    isSkip?: boolean

    /**
     * 特殊活动的皮肤配置
     */
    specialSkin?: SpecialSkinConfig
  } = {
    day: 0,
  }

  animationRef?: AnimationItem
  play: any
}

@Injectable({
  providedIn: 'root',
})
export class AnimationService {
  playedCount = 0
  group: Group

  private isNationalDayThemeEnable = computed(() => this.groupStudyService.isNationalDayThemeEnable())

  constructor(
    public groupStudyService: GroupStudyService,
    public app: AppService,
  ) {
    const temp = this.groupStudyService.signDays?.filter(it => it.isTodayBefore).length
    this.currentDay = temp ? temp : 0
    // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
    this.group = this.groupStudyService.groupInformation?.group!
  }

  animations: Animation[] = []
  animationDefs: AnimationDef[] = [
    new AnimationDef('avatar'),
    new AnimationDef('tick'),
    new AnimationDef('calendar'),
    new AnimationDef('avatarMove'),
    new AnimationDef('calendar'),
    new AnimationDef('shine'),
  ]

  currentDay = 0

  // 播放动画
  async playAnimationByDay(dayIndex: number, animationSequence?: Animation[][]) {
    if (animationSequence?.length == 0 || !animationSequence) {
      return
    }
    let animationSequenceIndex = 0
    for (const it of animationSequence) {
      let onStop: any
      const stopSignal = new Promise(r => {
        onStop = r
      })
      const arr = Array.isArray(it) ? it : [it]
      if (arr.length == 0) {
        continue
      }
      for (const item of arr) {
        const def = this.animationDefs?.find(x => x.type == item.type)
        def?.play(item, () => {
          item.play(onStop, arr)
        })
      }
      // eslint-disable-next-line no-await-in-loop
      await stopSignal
      animationSequenceIndex++
    }
  }

  getAnimationSequence(dayIndex: number) {
    const currentAnimation = this.animations.filter(it => it.config.day == dayIndex)
    if (currentAnimation.length == 0) {
      return
    }
    currentAnimation.forEach(it => {
      if (it.config.element) {
        this.createLottieAnimation(it)
      }
    })

    const ret = [] as (Animation[])[]
    const unSkippedAnimations = currentAnimation.filter(it => (it.animationRef?.totalFrames && it.animationRef.totalFrames > 1) ?? !it.animationRef)
    const avatarAnimation = unSkippedAnimations.filter(it => it.type == 'avatar')
    const tickAnimation = unSkippedAnimations.filter(it => it.type == 'tick')
    const calendarNumberAnimation = unSkippedAnimations.filter(it => it.type == 'calendar')
    const avatarMoveAnimation = currentAnimation.filter(it => it.type == 'avatarMove')
    const shineAnimation = currentAnimation.filter(it => it.type == 'shine')
    if (this.isSkipPlay(calendarNumberAnimation.find(it => it.animationRef)?.config.initFrame)) {
      avatarMoveAnimation.forEach(it => it.config.isSkip = true)
    }

    this.group.members.forEach(it => {
      ret.push(tickAnimation.filter(item => it.userId == item.config.user))
      ret.push(avatarAnimation.filter(item => it.userId == item.config.user))
    })
    if (avatarMoveAnimation[0] && !avatarMoveAnimation[0]?.config.isSkip) {
      ret.push(avatarMoveAnimation)
      ret.push(calendarNumberAnimation)
      ret.push(shineAnimation)
    }
    return ret
  }

  // 根据帧数来判断是否跳过当前lottie动画
  isSkipPlay(frame?: number[]) {
    if (!frame) {
      return true
    }
    if (frame[0] == frame[1]) {
      return true
    }
    return frame[0] == 0 && frame[1] == 1
  }

  private isDarkMode() {
    return document.documentElement.classList.contains('dark')
  }

  // 创建Lottie动画
  createLottieAnimation(it: Animation) {
    const group = this.groupStudyService.groupInformation?.group
    if (it.type == 'tick') {
      const animationConfig: AnimationConfigWithData = {
        container: it.config.element!,
        renderer: 'svg',
        loop: false,
        autoplay: false,
        name: 'tick',
        animationData: it.config.specialSkin
          ? replaceColor([54, 181, 157], [...it.config.specialSkin.avatarBackgroundColor], tickAnimationData)
          : tickAnimationData,
      }
      if (this.isSkipPlay(it.config.initFrame)) {
        animationConfig.initialSegment = it.config.initFrame as AnimationSegment
      }
      const temp = lottie.loadAnimation(animationConfig)
      it.animationRef = temp
      it.play = (onStop: FunctionConstructor, arr: Animation[]) => {
        temp.play()
        it.animationRef?.addEventListener('complete', () => {
          this.playedCount++
          if (this.playedCount == arr.length) {
            onStop()
            this.playedCount = 0
          }
        })
      }
    }
    if (it.type == 'avatar') {
      const filePath = 'completion-status/'
      if (!it.config.sign_avatar) {
        avatarAnimationData.assets[0].p = filePath + 'question_mark.png'
        avatarAnimationData.assets[1].p = filePath + 'question_mark.png'
      } else {
        const avatarName = it.config.specialSkin?.avatarName ?? it.config.sign_avatar
        const extraInfo = this.isNationalDayThemeEnable() && typeof avatarName === 'number' ? '_national_2024' : ''
        if (it.config.study_succeeded_time) {
          avatarAnimationData.assets[0].p = filePath + `img_${avatarName}${extraInfo}.png`
          avatarAnimationData.assets[1].p = filePath + `img_${avatarName}${extraInfo}_grey.png`
        } else if (it.config.is_signed) {
          avatarAnimationData.assets[0].p = filePath + `img_${avatarName}${extraInfo}_grey.png`
          avatarAnimationData.assets[1].p = filePath + `img_${avatarName}${extraInfo}_grey.png`
        }
      }

      let animationData = replaceColor([130, 194, 171], it.config.animationColor!, avatarAnimationData)

      if (this.isDarkMode()) {
        animationData = replaceColor(
          [229, 229, 229],
          [161, 161, 161],
          animationData,
        )
      }

      const animationConfig = {
        container: it.config.element!,
        renderer: 'svg',
        loop: false,
        autoplay: false,
        name: 'splash',
        animationData: animationData,
      } as AnimationConfigWithPath | AnimationConfigWithData
      if (this.isSkipPlay(it.config.initFrame)) {
        animationConfig.initialSegment = it.config.initFrame as AnimationSegment
        it.config.isSkip = true
      }
      const temp = lottie.loadAnimation(animationConfig)
      it.animationRef = temp
      it.play = (onStop: FunctionConstructor, arr: Animation[]) => {
        const sound = new Audio('assets/audio/audio2.mp3')
        sound.play()
        temp.play()
        it.animationRef?.addEventListener('complete', () => {
          if (it.type == 'avatar') {
            let index = this.group.members.map(item => item.userId).findIndex(item => it.config.user == item)
            index = index ? index : 0
            this.groupStudyService.initialFinishStatus![index] = true
          }
          this.playedCount++
          if (this.playedCount == arr.length) {
            onStop()
            this.playedCount = 0
          }
        })
      }
    }
    if (it.type == 'calendar') {
      const tempChars = cloneDeep(numberAnimation)
      let actualAnimation = cloneDeep(numberAnimation)
      actualAnimation.chars = tempChars.chars.filter((item: any) => item.ch == (it.config.reward_count + ''))
      actualAnimation.layers[0].t!.d.k[0].s.fc = (it.config.animationColor!).map(it => it / 255)
      actualAnimation = cloneDeep(actualAnimation)
      const animationConfig = {
        container: it.config.element,
        renderer: 'svg',
        loop: false,
        autoplay: false,
        name: 'calendar',
        animationData: replaceColor(
          [54, 181, 157],
          (it.config.specialSkin && getColorFromColorType(it.config.specialSkin.calendarColor)) ?? it.config.animationColor!,
          actualAnimation,
        ),
      } as AnimationConfigWithPath | AnimationConfigWithData
      if (this.isSkipPlay(it.config.initFrame)) {
        animationConfig.initialSegment = it.config.initFrame as AnimationSegment
      }
      const temp = lottie.loadAnimation(animationConfig)
      it.animationRef = temp
      it.play = () => {
        it.config.element?.setAttribute('style', 'opacity: 1')
        temp.play()
      }
    }
  }

  clearAnimationByDay(number: number) {
    this.animations.filter(it => it.config.day == number).forEach(it => it.animationRef?.destroy())
    this.animations = []
  }
}
