import { Injectable } from '@angular/core'
import { Dayjs } from 'dayjs'
import { isToday } from '../../../group-data/handler'

@Injectable({
  providedIn: 'root',
})
export class HelperService {
  compensateDoubleDigitalInTime(time: number): string | number {
    if (time < 10) {
      return '0' + time
    }
    return time
  }

  /**
   * 判断一个北京时间减去四小时后时候是业务中定义的今天
   * @param day
   */
  isToday(day: Dayjs) {
    return isToday(day.toISOString())
  }
}
