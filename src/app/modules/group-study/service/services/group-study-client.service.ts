import { Injectable } from '@angular/core'
import { random } from 'lodash-es'
import {
  ClientFrontendCommunicationService,
  ClientSignUpGroupStudy,
  PaymentOptions,
  Product,
} from '../../../core/services/client-frontend-communication.service'
import { Order } from '../../../group-study-v2/services/request.type'
import { GROUP_NAMES_LATEST } from '../../../shared/constants'
import { GroupStudyService } from '../group-study.service'

@Injectable({
  providedIn: 'root',
})
export class GroupStudyClientService {
  constructor(
    public cfc: ClientFrontendCommunicationService,
  ) { }

  clientOpenNotificationPermissionIfNeed(title: string, message: string, callback?: (isPermit: boolean) => void) {
    this.cfc.clientOpenNotificationPermissionIfNeed(title, message, callback)
  }

  clientToast(info: string, type?: string) {
    this.cfc.clientToast(info, type)
  }

  clientUploadStudyRecord() {
    this.cfc.clientUploadStudyRecord()
  }

  clientSignUpGroupStudy(info: ClientSignUpGroupStudy) {
    this.cfc.clientSignUpGroupStudy(info)
  }

  clientAlertJoinFailed(title: string, message: string, type: string) {
    this.cfc.clientAlertJoinFailed(title, message, type)
  }

  clientShareInvitationCode(code: string) {
    this.cfc.clientShareInvitationCode(code)
  }

  clientSetPageTitle(title: string) {
    this.cfc.clientSetPageTitle(title)
  }

  clientStartLoading() {
    this.cfc.clientStartLoading()
  }

  clientEndLoading() {
    this.cfc.clientEndLoading()
  }

  clientVibrate() {
    this.cfc.clientVibrate()
  }

  mountClientFunction(groupService: GroupStudyService) {
    window.Memo.reload = () => {
      const href = `/pages/activity-center/group-study/?token=${groupService.app.token}`
      location.replace(href)
    }
    window.Memo.generateGroupName = () => {
      const defaultGroupName = GROUP_NAMES_LATEST
      let randomIndex = random(random(0, defaultGroupName.length - 1, false))
      while (randomIndex == window.randomIndex) {
        randomIndex = random(random(0, defaultGroupName.length - 1, false))
      }
      window.randomIndex = randomIndex
      return defaultGroupName[window.randomIndex]
    }
  }

  clientShare(linkUrl: string) {
    this.cfc.clientShare(linkUrl)
  }

  share(...opts: Parameters<typeof this.cfc.share>) {
    return this.cfc.share(...opts)
  }

  clientPayForJoiningGroup(price: number, goodsSN: string, activity_id: string, order?: Order) {
    const defaultProduct: Product = {
      goods_catalog: 'product',
      goods_type: '',
      goods_sn: goodsSN,
      goods_ios: 'com.maimemo.ios.momo.product.RC1',
      goods_name: -1,
      goods_desc: '自建组队费用',
      goods_full_desc: '自建组队费用',
      bonus: 0,
      shop_price: price,
      promote_price: price,
      promotion_desc: null,
      amount: 0,
      currency: '¥', // 若字段不存在，默认为 ¥
    }

    return new Promise<Parameters<PaymentOptions['callback']>[0]>((resolve, reject) => {
      let finalProduct = defaultProduct
      const extra: Partial<PaymentOptions> = {
        cache_key: activity_id,
      }

      // 有 order 的话走新逻辑
      if (order) {
        const { product, ...rest } = order
        finalProduct = product
        Object.assign(extra, rest)
      }

      return this.cfc.requestPayment({
        product: JSON.parse(JSON.stringify(finalProduct)),
        method: 'alipay',
        ...extra,
        callback: params => {
          resolve(params)
        },
      })
        .catch(err => reject(err))
    })
  }
}
