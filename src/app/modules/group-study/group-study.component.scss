#group-study * {
  -webkit-touch-callout: none; /*系统默认菜单被禁用*/
  -webkit-user-select: none; /*webkit浏览器*/
  -khtml-user-select: none; /*早期浏览器*/
  -moz-user-select: none; /*火狐*/
  -ms-user-select: none; /*IE10*/
  user-select: none;
}

#group-study {
  overflow: hidden;
}

.activity-enroll {
  position: fixed;
  z-index: 200;
  bottom: 0;
  left: 0;
  width: 100vw;
  margin: 0 auto;
  background-color: var(--bg-color-2);
  padding-top: 10px;
}

.enroll {
  width: 80%;
  height: 42px;
  background-color: #36b59d;
  border-radius: 3px;
  margin: 0 auto 25px;
  font-size: 18px;
  color: rgba(255, 255, 255, 1);
  display: flex;
  justify-content: center;
  align-items: center;

  &:active {
    opacity: var(--activity-opacity);
  }
}

.test {
  position: fixed;
  right: 0;
  bottom: 70px;
  z-index: 10000;
  background: #36b59d;
  height: 30px;
  width: 100px;
  border-radius: 5px;
  text-align: center;
  line-height: 30px;
}

.loading-icon {
  position: absolute;
  top: 30%;
  right: 50%;
  transform: translate(50%);
  text-align: center;
  font-weight: 800;
}

.loading-icon > img {
  width: 50px;
  padding-bottom: 10px;
}

.interactive-button {
  position: absolute;
  border-radius: 4px;
  padding: 4px 7px;
  background-color: var(--bg-color-1);
  color: var(--theme-green);
  top: 10px;
  z-index: 9999;
  display: flex;
  align-items: center;
  font-weight: bold;

  &:active {
    opacity: var(--activity-opacity);
  }

  &.button-dismiss {
    color: var(--color-forget);
  }

  .red-dot {
    --size: 10px;

    box-sizing: border-box;
    position: absolute;
    top: -3px;
    right: -3px;
    display: inline-block;
    width: var(--size);
    height: var(--size);
    border: 2px var(--bg-color-2) solid;
    border-radius: 6px;
    background-color: red;
    background-clip: content-box;
    z-index: 2;
  }
}

:root.dark :host .interactive-button {
  font-weight: normal;
  color: #fff;
  background-color: var(--theme-green);

  &.button-dismiss {
    background-color: var(--color-forget);
  }
}

.opacity {
  opacity: 0;
  position: absolute;
  height: 0;
  overflow: hidden;
}

.toast {
  position: fixed;
  max-width: 80%;
  text-align: center;
  bottom: 50px;
  left: 50%;
  transform: translateX(-50%);
  padding: 12px 15px;
  background-color: var(--title-color);
  border-radius: 6px;
  color: #fff;
  z-index: 9999;
  font-size: 13px;
}
