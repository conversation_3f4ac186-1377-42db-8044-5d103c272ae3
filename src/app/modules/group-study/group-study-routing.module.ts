import { NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { TestComponent } from '../test/components/test/test.component'
import { GroupStudyComponent } from './group-study.component'
import { ActivityStepperComponent } from './pages/activity-stepper/activity-stepper.component'
import { ActivityTerminationComponent } from './pages/activity-termination/activity-termination.component'
import { AdjustPeriodFailedComponent } from './pages/adjust-period-failed/adjust-period-failed.component'
import { AdjustPeriodComponent } from './pages/adjust-period/adjust-period.component'
import { GroupFailedComponent } from './pages/group-failed/group-failed.component'
import { GroupSucceedComponent } from './pages/group-succeed/group-succeed.component'
import { InviteOthersComponent } from './pages/invite-others/invite-others.component'
import { JoinGroupComponent } from './pages/join-group/join-group.component'
import { LearningSucceedComponent } from './pages/learning-succeed/learning-succeed.component'
import { LearningComponent } from './pages/learning/learning.component'
import { StudyReportComponent } from './pages/study-report/study-report.component'

const routes: Routes = [
  {
    path: '',
    component: GroupStudyComponent,
    children: [
      {
        path: 'activityStepper',
        component: ActivityStepperComponent,
        data: {
          name: 'activityStepper',
        },
      },
      {
        path: 'activityTermination',
        component: ActivityTerminationComponent,
        data: {
          name: 'activityTermination',
        },
      },
      {
        path: 'inviteOthers',
        component: InviteOthersComponent,
        data: {
          name: 'inviteOthers',
        },
      },
      {
        path: 'groupSucceed',
        component: GroupSucceedComponent,
        data: {
          name: 'groupSucceed',
        },
      },
      {
        path: 'groupFailed',
        component: GroupFailedComponent,
        data: {
          name: 'groupFailed',
        },
      },
      {
        path: 'adjustPeriod',
        component: AdjustPeriodComponent,
        data: {
          name: 'adjustPeriod',
        },
      },
      {
        path: 'adjustPeriodFailed',
        component: AdjustPeriodFailedComponent,
        data: {
          name: 'adjustPeriodFailed',
        },
      },
      {
        path: 'learning',
        component: LearningComponent,
        data: {
          name: 'adjustPeriodFailed',
        },
      },
      {
        path: 'learnSucceed',
        component: LearningSucceedComponent,
        data: {
          name: 'learnSucceed',
          hideAwardRules: true,
        },
      },
      {
        path: 'joinGroup',
        component: JoinGroupComponent,
        data: {
          name: 'joinGroup',
        },
      },
      {
        path: 'test',
        component: TestComponent,
        data: {
          name: 'test',
        },
      },
      {
        path: 'report',
        component: StudyReportComponent,
        data: {
          hideAwardRules: true,
        },
      },
    ],
  },
]

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class GroupStudyRoutingModule {}
