<div id="group-study">
  <div [class.opacity]="!isShowInteractiveBoard">
    <app-interactive-board #boardRef (closeEmitter)="closeInteractiveBoard()"></app-interactive-board>
  </div>
  @if (groupStudyService.isShowPage) {
    <div [class.opacity]="isShowInteractiveBoard">
      @if (isShowAwardRule) {
        @if (isShowInteractiveButton && nextActiveActivity) {
          <div
            class="interactive-button"
            [style.left]="'5%'"
            appClick
            [debounceTime]="300"
            (debounceClick)="onClickSwitchGroup()"
          >
            <memo-icon name="switch" size="1em" [style.margin-right.em]="0.2"></memo-icon>
            队伍
          </div>
        }
        @if (isShowInteractiveButton && !groupStudyService.status.isShowReport) {
          <div
            appClick
            [debounceTime]="300"
            class="interactive-button"
            [style.right]="'5%'"
            (debounceClick)="openInteractiveBoard()"
          >
            @if (isShowRedDot && isShowTemp) {
              <span class="red-dot"></span>
            }
            互动板
          </div>
        }
        @if (isDismissButtonVisible) {
          <div
            appClick
            [debounceTime]="300"
            class="interactive-button button-dismiss"
            [style]="{
              right: '5%',
              transform: 'translateY(120%)',
            }"
            (debounceClick)="onClickDismissGroup()"
          >
            解散
          </div>
        }
      }
      <router-outlet></router-outlet>
      @if (isShowAwardRule) {
        <app-award-rule [activity]="groupStudyService.activityService.activity"></app-award-rule>
      }
      @if (isShowAwardRule) {
        <app-activity-guide [activity]="groupStudyService.activityService.activity"></app-activity-guide>
      }
      @if (groupStudyService.isShowSignUpButton && !groupStudyService.activityService.isActivityToEnd()) {
        <div style="height: 77px"></div>
      }
      @if (groupStudyService.isShowSignUpButton && !groupStudyService.activityService.isActivityToEnd()) {
        <div class="activity-enroll">
          <div appClick class="enroll" (debounceClick)="signUp()" [debounceTime]="200">创建队伍</div>
        </div>
      }
    </div>
  }
  @if (testService.isShowTestButton(groupStudyService.groupInformation) || !environment.production) {
    <div class="test" (click)="testService.isShowTestComponent = !testService.isShowTestComponent">测试</div>
    @if (testService.isShowTestComponent) {
      <app-test></app-test>
    }
  }
</div>
<div appToast class="toast" [content]="groupStudyService.status.toastContent"></div>

<p-dialog [(visible)]="isSignUpDialogVisible" [modal]="true" [dismissableMask]="true" [closable]="true">
  <ng-template #headless>
    <app-create-group-dialog (dismiss)="isSignUpDialogVisible.set(false)"></app-create-group-dialog>
  </ng-template>
</p-dialog>
