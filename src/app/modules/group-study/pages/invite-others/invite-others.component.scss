.team-creating {
  height: 276px;
  background: var(--bg-color-2);
  border-radius: 10px;
  margin: 0 auto;
  padding-bottom: 20px;
  width: var(--container-width);
}

.portraits {
  display: flex;
  width: 100%;
}

.count-down {
  padding-top: 20px;
  text-align: center;
}

.detailed-time {
  margin-top: 10px;
  font-size: 30px;
  color: var(--font-color-1);
}

.portraits {
  display: flex;
  justify-content: space-evenly;
  margin-top: 20px;
}

.portrait {
  position: relative;
  z-index: 3;
}

.click-feedback {
  animation: click 1s;
}

.click-1 {
  border: 2px dotted var(--border-color);
}

.click-1:active {
  animation: click 1s;
}

@keyframes click {
  from {
    background-color: #36B59D
  }
  to {
    background-color: var(--bg-color-2)
  }
}

.portraits > .user {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.username {
  margin-top: 3px;
  width: 74px;
  text-align: center;
  color: var(--font-color-2);
  font-size: 14px;
}

.portraits > div > div:nth-child(1) {
  width: 54px;
  height: 54px;
  border: 2px dotted var(--border-color);
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.portraits > div > div:nth-child(1)::after,
.portraits > div > div:nth-child(1)::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 2px;
  background: var(--border-color);
  position: absolute;
}

.portraits > div > div:nth-child(1)::before {
  transform: rotate(90deg);
}

.team-people-hint {
  margin-top: 25px;
  text-align: center;
  color: rgba(128, 128, 128, 1);
}

.invite {
  width: 60%;
  height: 42px;
  background: rgba(54, 181, 157, 1);
  border-radius: 4px;
  color: rgba(255, 255, 255, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px auto;

  &:active {
     opacity: var(--activity-opacity)
   }
}
