import { Injectable } from '@angular/core'
import { GroupStudyService } from '../../service/group-study.service'

@Injectable({
  providedIn: 'root',
})
export class InviteOthersService {
  constructor(
    public groupStudyService: GroupStudyService,
  ) {
  }

  inviteOthers(groupId: string): void {
    this.groupStudyService.getGroupInvitation(groupId).subscribe(it => {
      this.groupStudyService.client.clientShareInvitationCode(it.invitation_code)
    })
  }
}
