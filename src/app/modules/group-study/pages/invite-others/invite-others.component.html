<div class="team-creating">
  <app-countdown-three-hours [initialTime]="groupInformation?.group?.createdTime"></app-countdown-three-hours>
  <app-portraits
    [portraitsType]="'invite'"
    [groupInformation]="groupInformation"
    (inviteOther)="inviteOthers($event)"
  ></app-portraits>
  <div class="team-people-hint" style="font-weight: 800">
    还差
    <span class="theme-color">{{ (groupInformation?.group?.type === 'TRIO' ? 3 : 2) - initialNumberOfMember! }}</span>
    人组队成功
  </div>
  <div appClick class="invite" (debounceClick)="inviteOthers({})" [debounceTime]="300">邀请好友组队</div>
</div>
