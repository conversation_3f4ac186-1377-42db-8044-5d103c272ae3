/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import { AfterViewInit, Component, DestroyRef, inject, OnInit } from '@angular/core'
import { map, of, tap, type Observable } from 'rxjs'
import { captureException } from '@sentry/angular'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { AppService } from '../../../../app.service'
import { AppConfig } from '../../../../configs/app.config'
import { GroupInformation } from '../../../group-data/group-information.model'
import { Member } from '../../../group-data/group/member.model'
import { GroupStudyService } from '../../service/group-study.service'
import { MemoModalService } from '../../../ui/modal/modal.service'
import { ShareGroupComponent } from '../../../group-study-v2/components/share-group/share-group.component'
import { GroupStudyClientService } from '../../service/services/group-study-client.service'

@Component({
  selector: 'app-invite-others',
  templateUrl: './invite-others.component.html',
  styleUrls: ['./invite-others.component.scss'],
  standalone: false,
})
export class InviteOthersComponent implements OnInit, AfterViewInit {
  private modal = inject(MemoModalService)
  private client = inject(GroupStudyClientService)
  private app = inject(AppService)
  private groupStudyService = inject(GroupStudyService)
  private destroyRef = inject(DestroyRef)

  offsetInSecond = 0
  groupPeopleNumber = 0

  initialNumberOfMember?: number

  private invitationText = ''

  get groupInformation() {
    return this.groupStudyService.groupInformation
  }

  // TODO: 暂时先注释
  // @HostListener('document:visibilitychange')
  // visibilitychange() {
  //   this.checkHiddenDocument()
  // }
  checkHiddenDocument() {
    if (!document.hidden) {
      location.replace(location.origin + AppConfig.activityBasePath + '?token=' + this.app.token)
    }
  }

  ngOnInit(): void {
    const realMembersCount = this.getReadMembersCount(this.groupInformation)
    this.groupPeopleNumber = realMembersCount
    this.initialNumberOfMember = realMembersCount
    this.setTimerTask()
  }

  inviteOthers(it: Member | Record<string, unknown>) {
    if (Object.keys(it).length != 0) {
      return
    }
    const group = this.groupInformation?.inviteUserGroup ?? this.groupInformation?.group
    if (!group) {
      return
    }

    const action$: Observable<string> = this.invitationText
      ? of(this.invitationText)
      : this.groupStudyService.getGroupInvitation(group.groupId).pipe(
          map(v => v.invitation_code),
          tap(v => {
            this.invitationText = v
          }),
        )

    action$.subscribe({
      next: invitationText => {
        this.modal.create({
          component: ShareGroupComponent,
          componentProps: {
            shareText: invitationText,
          },
          cssClass: 'rounded-modal fit-modal ion-align-items-end',
        })
      },
      error: err => {
        console.error(err)
        captureException(err)
        this.client.clientToast(err?.error?.errors?.[0]?.message || '获取邀请码失败', 'error')
      },
    })
  }

  ngAfterViewInit(): void {
    window.scrollTo(0, 0)
  }

  setTimerTask() {
    let groupInitialTime = this.groupInformation?.group?.updatedTime
    this.groupStudyService.getIntervalGetGroupStatus()
      .pipe(
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(
        it => {
          it = new GroupInformation(it)
          this.groupStudyService.setServices(it)
          // 更新时间产生变化
          if (it.group.updatedTime == groupInitialTime) {
            return
          }
          groupInitialTime = it.group.updatedTime
          this.groupStudyService.groupInformation = it
          this.initialNumberOfMember = this.getReadMembersCount(it)
          if (
            this.groupStudyService.groupTypeAndTeamNumMap.get(this.groupInformation?.group?.type!)
            === this.groupInformation?.group?.members.length
          ) {
            this.groupStudyService.navigate()
          }
        },
      )
  }

  private getReadMembersCount(groupInformation?: GroupInformation): number {
    const members = groupInformation?.group?.members ?? []
    const realMembersCount = members.filter(v => v.userId !== undefined).length
    return realMembersCount
  }
}
