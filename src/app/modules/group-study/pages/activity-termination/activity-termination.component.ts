import { Component } from '@angular/core'
import { GroupStudyService } from '../../service/group-study.service'
import { Activity } from '../../../group-data/activity/activity.model'

@Component({
  selector: 'app-activity-termination',
  templateUrl: './activity-termination.component.html',
  styleUrls: ['./activity-termination.component.css'],
  standalone: false,
})
export class ActivityTerminationComponent {
  activity?: Activity
  constructor(
    private groupStudyService: GroupStudyService,
  ) {
    this.activity = this.groupStudyService.activityService.activity
  }
}
