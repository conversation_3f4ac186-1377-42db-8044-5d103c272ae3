.container {
  margin: 10px auto 10px;
  background: var(--bg-color-2);
  border-radius: 10px;
  padding-top: 32px;
  width: var(--container-width)
}

.figure {
  text-align: center;
}

.text-style-1 {
  padding-top: 13px;
  text-align: center;
  color: var(--title-color);
  font-size: 25px;
}

.text-style-2 {
  padding-top: 10px;
  text-align: center;
  color: var(--second-font-color);
  font-size: 13px;
  padding-bottom: 20px;
}

.activity-guide {
  background: rgba(255, 255, 255, 1);
  border-radius: 10px;
  margin: 0 auto;
  padding-bottom: 15px;
}

.title {
  padding-top: 20px;
  color: var(--title-color);
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}

.circle-1 {
  display: inline-block;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: rgba(196, 196, 196, 1);
  vertical-align: center;
  transform: translate(0.2rem, -0.2rem);
}

.circle-2 {
  display: inline-block;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background-color: rgba(196, 196, 196, 1);
  vertical-align: center;
  transform: translate(-0.6rem, -0.25rem);
}

.hint {
  background: rgba(246.993, 246.993, 246.993, 1);
  border-radius: 6px;
  margin: 20px auto;
  font-size: 13px;
}

.step-chart {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 25px;
}

.step-chart > div {
  position: relative;
}

.step-font {
  position: absolute;
  width: 80px;
  left: -74%;
  color: rgba(170, 170, 170, 1);
  font-size: 13px;
}

.dotted-line {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 22%;
  height: 2px;
  background: repeating-linear-gradient(90deg, #d2d2d2, #d2d2d2 4px,#fff 4px, #fff 8px);
  transform: translateY(-2px);
}

.hint-level-1 {
  width: 90%;
  height: 40px;
  margin: 0 auto;
  padding: 15px;
  color: var(--font-color-1);
  line-height: 20px;
  font-size: 13px;
}

.hint-level-2 {
  width: 90%;
  height: 40px;
  margin: 10px auto;
}

.split-line {
  width: 80%;
  height: 1px;
  background: var(--border-color);
  margin: 0 auto;
}
