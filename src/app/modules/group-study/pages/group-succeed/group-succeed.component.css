.team-success-model-2 {
  background: var(--bg-color-2);
  margin: 0 auto;
  padding-bottom: 5px;
}

.team-status {
  width: 100%;
  color: rgba(54, 181, 157, 1);
  font-size: 22px;
  text-align: center;
  margin-top: 22px;
}

.date-hint {
  color: var(--font-color-1);
  font-size: 14px;
  margin-top: 22px;
}

.adjusting-status {
  background: var(--bg-color-2);
  margin: 10px auto 0;
  overflow: hidden;
  border-radius: 10px;
}

.adjusting-status > div:nth-child(1) {
  margin-top: 20px;
  margin-bottom: 27px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.adjusting-status > div:nth-child(1) > img:nth-child(even) {
  height: 15px;
}

.adjusting-instruction {
  width: 90%;
  margin: 10px auto;
  color: rgba(170, 170, 170, 1);
  font-size: 13px;
  text-align: left;
  background: var(--bg-color-2);
}

.user-status {
  width: 90%;
  margin: 0 auto;
}

.user-status > div {
  display: flex;
  margin: 16px 0;
}

.user-status > div > img {
  transform: translateY(5px);
  display: inline-block
}

.username-2 {
  margin-top: 10px;
  color: var(--font-color-2);
  font-size: 13px;
}

.username-2 > span {
  display: inline-block;
  width: 90px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.user-signup {
  margin-left: 5px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.user-info {
  font-size: 13px;
}

.split {
  display: inline-block;
  width: 1px;
  height: 14px;
  background: var(--border-color);
  margin: 0 10px;
}

.finished {
  margin-top: 6px;
  color: rgba(54, 181, 157, 1);
  font-size: 14px;
}

.no-finished {
  margin-top: 6px;
  color: rgba(220, 102, 62, 1);
  font-size: 14px;
}

.team-failed {
  border-radius: 10px;
  overflow: hidden;
  margin-top: 22px;
  text-align: center;
}

.team-status-failed {
  color: rgba(220, 102, 62, 1);
  font-size: 22px;
  margin-top: 22px;
  font-weight: 600;
}

.failed-info {
  width: 80%;
  color: rgba(220, 102, 62, 1);
  font-size: 14px;
  margin: 10px auto;
}

.count-down-time {
  width: 80%;
  margin: 20px auto;
  color: rgba(170, 170, 170, 1);
  font-size: 26px;
  text-align: center;
}

.team-success {
  background: var(--bg-color-2);
  overflow: hidden;
  margin: 0 auto 10px;
  text-align: center;
  border-radius: 10px;
  padding-bottom: 20px;
}

.medal-waller-container {
  display: grid;
  grid: 1fr / 1fr 1fr 1fr;
}
