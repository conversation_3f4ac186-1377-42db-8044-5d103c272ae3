import { ComponentFixture, TestBed } from '@angular/core/testing'

import { GroupSucceedComponent } from './group-succeed.component'

describe('GroupSucceedComponent', () => {
  let component: GroupSucceedComponent
  let fixture: ComponentFixture<GroupSucceedComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [GroupSucceedComponent],
    })
      .compileComponents()
  })

  beforeEach(() => {
    fixture = TestBed.createComponent(GroupSucceedComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
