import { Component, OnInit } from '@angular/core'
import { GroupStudyService } from '../../service/group-study.service'
import { GroupInformation } from '../../../group-data/group-information.model'

@Component({
  selector: 'app-group-succeed',
  templateUrl: './group-succeed.component.html',
  styleUrls: ['./group-succeed.component.css'],
  standalone: false,
})
export class GroupSucceedComponent implements OnInit {
  groupInformation?: GroupInformation

  constructor(
    public groupStudyService: GroupStudyService,
  ) {
  }

  ngOnInit(): void {
    this.groupInformation = this.groupStudyService.groupInformation
  }
}
