import { ChangeDete<PERSON><PERSON><PERSON>, Component, Inject, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, computed } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { Dayjs } from 'dayjs'
import { cloneDeep } from 'lodash-es'
import { Subscription } from 'rxjs'
import Swiper from 'swiper'
import { SwiperComponent } from 'swiper/angular'
import { AppService } from '../../../../app.service'
import { ROUTE_BASE_HREF } from '../../../entry/group-study.module'
import { Event } from '../../../group-data/event/event.model'
import { EventService } from '../../../group-data/event/event.service'
import { GroupInformation } from '../../../group-data/group-information.model'
import { History } from '../../../group-data/history/history.model'
import { Report } from '../../../group-data/report/report.model'
import { CalenderComponent } from '../../components/calender/calender.component'
import { SignDay } from '../../models/model'
import { GroupStudyService } from '../../service/group-study.service'
import { StatusService } from '../../service/services/status.service'
import { LearningService } from './learning.service'

@Component({
  selector: 'app-learning',
  templateUrl: './learning.component.html',
  styleUrls: ['./learning.component.css'],
  standalone: false,
})
export class LearningComponent implements OnInit, OnDestroy {
  groupInformation: GroupInformation | undefined
  timer?: Subscription
  signDays?: SignDay[]
  lastSignDay?: SignDay
  firstDayRewardHistory?: Dayjs
  actualSignDays?: SignDay[] = []
  slider!: HTMLDivElement
  currentSignDetailIndex = 0
  array = Array
  isShowReport = false
  currentReport?: Report
  @ViewChild('calender') calender!: CalenderComponent
  @ViewChild('swiper', { static: false }) swiper?: SwiperComponent
  private events: Event[] = []

  isNationalDayThemeEnable = computed(() => this.groupStudyService.isNationalDayThemeEnable())

  constructor(
    public groupStudyService: GroupStudyService,
    public app: AppService,
    private router: Router,
    public route: ActivatedRoute,
    public learningService: LearningService,
    public status: StatusService,
    public eventService: EventService,
    private cdr: ChangeDetectorRef,
    @Inject(ROUTE_BASE_HREF) private routeBase: string,
  ) {}

  ngOnInit(): void {
    const activityId = this.groupStudyService.activityService.getActivityId()
    this.events = this.eventService.getEvents()
    this.groupInformation = this.groupStudyService.formatGroupInformation(this.groupStudyService.groupInformation!)
    this.groupStudyService.client.clientUploadStudyRecord()
    this.signDays = this.groupStudyService.getSignDays(this)
    this.actualSignDays = this.signDays?.filter(it => it.isTodayBefore)
    this.groupStudyService.initialFinishStatus = this.groupInformation.group?.members.map(it => {
      return !!this.groupInformation?.todayHistory.find(item => item.memberId == it.userId)?.studySucceededTime
    })
    this.currentSignDetailIndex = this.actualSignDays!.length - 1
    this.groupStudyService.signDays = this.signDays
    this.groupStudyService.lastSignDayInfo = this.signDays[this.signDays.length - 1]
    if (this.groupInformation.group?.status == 'LEARN_SUCCEED') {
      if (!this.isReadLearnSuccessAnimation()) {
        this.groupStudyService.track(
          'VIEW',
          'LEARN_SUCCESS_ANIMATION',
        )
      }
      this.learningService.getReport().subscribe(it => {
        const tempReport = [] as Report[]
        if (it.length > 0) {
          it.forEach(item => tempReport.push(item))
          this.groupStudyService.reports = cloneDeep(tempReport)
          this.currentReport = it.find(it => it.creator == this.groupInformation?.currentUserId)!
        }
        if (this.isReadLearnSuccessAnimation() || this.groupStudyService.activityService.isActivityHasReport()) {
          this.currentSignDetailIndex = this.actualSignDays!.length
        }
      })
      return
    }
    this.groupStudyService.setTimerTask(this)
  }

  informCalendarAnimation() {
    this.calender.playCalenderAnimation()
  }

  getCurrentUserRewardCount(memberId: number, rewards: History[]) {
    return rewards.find(it => it.memberId === memberId)!
  }

  ngOnDestroy(): void {
    this.timer?.unsubscribe()
  }

  readReport() {
    this.groupStudyService.router.navigateByUrl(`${this.routeBase}learnSucceed`, { skipLocationChange: true })
  }

  slideTo(idx: number) {
    this.swiper?.swiperRef.slideTo(idx)
    this.currentSignDetailIndex = idx
  }

  onSlideChange(event: [swiper: Swiper]) {
    let initIndex
    if (this.currentReport?.rewardedTime) {
      initIndex = this.groupStudyService.totalSignDayNumber
    } else {
      initIndex = this.actualSignDays!.length - 1
    }
    this.currentSignDetailIndex = this.swiper?.swiperRef ? this.swiper.swiperRef.activeIndex : initIndex
    this.cdr.detectChanges()
  }

  viewTotalAwardEvent($event: any) {
    if (this.groupInformation?.group?.status == 'LEARN_SUCCEED') {
      this.swiper?.swiperRef.slideTo(this.groupStudyService.totalSignDayNumber)
    }
  }

  getSwiperInitialSlide() {
    if (this.groupStudyService.activityService.isActivityHasReport() || this.isReadLearnSuccessAnimation()) {
      if (this.groupStudyService.groupInformation?.group?.status == 'LEARN_SUCCEED') {
        return this.actualSignDays!.length
      }
    }
    if (this.currentReport?.rewardedTime) {
      return this.groupStudyService.totalSignDayNumber
    }
    return this.actualSignDays!.length - 1
  }

  isClickLastButtonInLearnSuccess() {
    return this.events.some(it => it.targetId == 'CALENDAR_LAST_BUTTON_IN_SUCCESS' && it.creator == this.groupInformation?.currentUserId)
  }

  isReadLearnSuccessAnimation() {
    return this.events.some(it => it.targetId == 'LEARN_SUCCESS_ANIMATION' && it.creator == this.groupInformation?.currentUserId) || this.isClickLastButtonInLearnSuccess()
  }

  viewReport() {
    this.router.navigate([this.routeBase, 'report'], {
      queryParamsHandling: 'preserve',
    })
  }
}
