export const mockReward = {
  reward_vocs: 32,
}
export const mockReport = [
  {
    _id: '123',
    activity_id: '123',                         // 活动 Id
    group_id: '123',                            // 队伍 Id
    creator: 2152799,                               // 创建者 id
    familiar_voc_count: 321,              // 巩固单词数
    // last_read_time: "2022-06-23T08:04:36.671Z",       // 上次阅读报告时间
    rewarded_time: '2022-06-23T08:04:36.671Z',        // 奖励领取时间
    created_time: '2022-06-23T08:04:36.671Z',          // 创建时间
    updated_time: '2022-06-23T08:04:36.671Z',          // 更新时间
  },
  {
    _id: '123',
    activity_id: '123',                         // 活动 Id
    group_id: '123',                            // 队伍 Id
    creator: 123,                               // 创建者 id
    familiar_voc_count: 321,              // 巩固单词数
    last_read_time: '2022-06-23T08:04:36.671Z',       // 上次阅读报告时间
    rewarded_time: '2022-06-23T08:04:36.671Z',        // 奖励领取时间
    created_time: '2022-06-23T08:04:36.671Z',          // 创建时间
    updated_time: '2022-06-23T08:04:36.671Z',          // 更新时间
  },
  {
    _id: '123',
    activity_id: '123',                         // 活动 Id
    group_id: '123',                            // 队伍 Id
    creator: 321,                               // 创建者 id
    familiar_voc_count: 321,              // 巩固单词数
    last_read_time: '2022-06-23T08:04:36.671Z',       // 上次阅读报告时间
    rewarded_time: '2022-06-23T08:04:36.671Z',        // 奖励领取时间
    created_time: '2022-06-23T08:04:36.671Z',          // 创建时间
    updated_time: '2022-06-23T08:04:36.671Z',          // 更新时间
  },
]
