<div *ngIf="!status.isShowReport" style="position: relative">
  <div [style.opacity]="!status.isShowReport ? 1 : 0">
    <!-- <div *ngIf="groupStudyService?.groupInformation?.group?.status === 'LEARN_SUCCEED'" class="read-report" (click)="readReport()"> -->
    <!--     查看组队学习报告 <img [src]="'assets/images/learn_success/enter-symbol.png' | cdn" style="width: 6px;vertical-align: middle"> -->
    <!-- </div> -->
    <ng-container>
      <swiper
        #swiper
        [slidesPerView]="1"
        [spaceBetween]="50"
        [initialSlide]="getSwiperInitialSlide()"
        [centerInsufficientSlides]="true"
        (slideChange)="onSlideChange($event)">
        <ng-container *ngFor="let item of array(this.actualSignDays!.length).fill(0); let i = index">
          <ng-template swiperSlide>
            <app-completion-status
                [actualSignDays]="actualSignDays"
                [firstDayRewardHistory]="firstDayRewardHistory"
                [currentSingDayIndex]="i"
                [groupInformation]="groupInformation"
                [lastSignDayInfo]="lastSignDay"
                [specialSkin]="actualSignDays?.[i]?.specialSkin"
                [nationalDayThemeEnable]="isNationalDayThemeEnable()"
                (calendarAnimationEvent)="informCalendarAnimation()">
            </app-completion-status>
          </ng-template>
        </ng-container>
        <ng-container>
          <ng-template swiperSlide>
            <app-total-award
                [currentReport]="currentReport"
                [reports]="groupStudyService.reports"
                [nationalDayThemeEnable]="isNationalDayThemeEnable()"
                (viewReportEmitter)="viewReport()">
            </app-total-award>
          </ng-template>
        </ng-container>
      </swiper>
    </ng-container>
    <app-calender #calender
                  [type]="'learning'"
                  [currentReport]="currentReport"
                  [currentSignDetailIndex]="currentSignDetailIndex"
                  [initialSignDays]="signDays"
                  [groupInformation]="groupInformation"
                  [actualSignDays]="actualSignDays"
                  (viewTotalAwardEvent)="viewTotalAwardEvent($event)"
                  (sliderMoveEvent)="slideTo($event)">
    </app-calender>
  </div>
</div>

