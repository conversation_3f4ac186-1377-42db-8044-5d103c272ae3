.finish-status-container{
  display: flex;
  flex-direction: row;
  width: 100%;
  overflow: scroll;
}

.finish-status-container::-webkit-scrollbar {
  display: none;
}

.finish-status {
  background: rgba(255, 255, 255, 1);
  border-radius: 10px;
  margin: 0 auto 10px;
  width: 90%;
}

.adjusting-status {
  background: rgba(255, 255, 255, 1);
  margin-top: 10px;
  overflow: hidden;
}

.adjusting-status > div:nth-child(1) {
  margin-top: 20px;
  margin-bottom: 27px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.adjusting-status > div:nth-child(1) > img:nth-child(even) {
  height: 15px;
}

.user-status {
  width: 90%;
  margin: 0 auto;
}

.user-status > div {
  display: flex;
  margin: 16px 0;
}

.user-status > div > img {
  transform: translateY(5px);
  display: inline-block
}

.username-2 {
  margin-top: 5px;
  color: rgba(142, 142, 142, 1);
  font-size: 13px;
}

.username-2 > span {
  display: inline-block;
  width: 90px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.user-signup {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}

.user-info {
  font-size: 13px;
}

.user-info > img {
  transform: translateY(2px);
}

.split {
  display: inline-block;
  width: 1px;
  height: 14px;
  background: rgba(229, 229, 229, 1);
  margin: 0 3px;
}

.calender-container {
  background: rgba(255, 255, 255, 1);
  border-radius: 10px;
  margin: 0 auto;
}

.calender {
  display: grid;
  grid: repeat(5, 20%) / repeat(3, 33%);
  padding-top: 10px;
  padding-bottom: 10px;
}

.calender > div {
  background-color: #f7f7f7;
  height: 70px;
  margin: 3px 10px;
  border-radius: 4px;
}

.calender > .card-style-green {
  background: rgba(130, 194, 171, 0.15);
}

.calender > .card-style-yellow {
  background: #fdf5e9;
}

.calender > .card-style-red {
  background: #fbefeb;
}

.calender > .card-style-opacity {
  opacity: 0.6;
}

.activity-day {
  color: var(--font-color-1);
  font-size: 13px;
  margin: 10px;
}

.user-portraits {
  margin-left: 6px;
}

/*.user-portraits > div {*/
/*  width: 31%;*/
/*  max-width: 25px;*/
/*  height: 31%;*/
/*  max-height: 25px;*/
/*  display: inline-block;*/
/*  position: relative;*/
/*  margin-right: 3px;*/
/*  border: 1px solid #e5e5e5;*/
/*  border-radius: 2px;*/
/*}*/

.user-portraits img {
  max-width: 25px;
  display: inline-block;
}

.finish-status-icon {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: scale(0.57) translate(80%, -75%);
  z-index: 100;
  color: rgba(255, 255, 255, 1);
}

.award-number {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(54, 181, 157, 1);
  transform: scale(0.5) translate(60%, -90%);
  z-index: 100;
  color: rgba(255, 255, 255, 1);
  font-size: 22px;
}

.right-top-loc {
  width: 28px;
  height: 28px;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(0.5) translate(60%, -90%);
  z-index: 100;
}

.study-day-count {
  color: rgba(142, 142, 142, 1);
  font-size: 13px;
  text-align: center;
  padding-top: 18px;
  padding-bottom: 20px;
}

.finish-yellow {
  height: 55px;
  width: 55px;
  border-radius: 50%;
  border: 2.5px solid rgba(235, 158, 39, 1);
  background-color: rgba(235, 158, 39, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.finish-green {
  height: 55px;
  width: 55px;
  border-radius: 50%;
  background: rgba(130, 194, 171, 0.15);
  border: 2.5px solid rgba(130, 194, 171, 1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.finish-status-info {
  text-align: center;
  color: var(--font-color-1);
  font-size: 13px;
  margin-top: 6px;
}

.active-card {
  background: rgba(130, 194, 171, 0.15) !important;
  border: 1px solid #36B59D;
}

.read-report {
  margin: 0 auto;
  color: var(--font-color-1);
  font-size: 12px;
  padding: 9px 0;
  width: var(--container-width);
}
