import { AfterViewInit, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core'
import confetti from 'canvas-confetti'
import { Dayjs } from 'dayjs'
import { Subscription } from 'rxjs'
import { GroupInformation } from '../../../group-data/group-information.model'
import { History } from '../../../group-data/history/history.model'
import { CalenderComponent } from '../../components/calender/calender.component'
import { MemberStatus, SignDay } from '../../models/model'
import { GroupStudyService } from '../../service/group-study.service'

@Component({
  selector: 'app-adjust-period',
  templateUrl: './adjust-period.component.html',
  styleUrls: ['./adjust-period.component.css'],
  standalone: false,
})
export class AdjustPeriodComponent implements OnInit, AfterViewInit, OnDestroy {
  groupInformation: GroupInformation | undefined
  timer?: Subscription
  signDays?: SignDay[]
  currentUserLastReadTime?: string
  memberStatus: MemberStatus[] = []
  firstDayRewardHistory?: Dayjs
  actualSignDays?: SignDay[] = []
  @ViewChild('calender') calender!: CalenderComponent

  constructor(
    public groupStudyService: GroupStudyService,
  ) {
  }

  ngOnInit(): void {
    this.groupInformation = this.groupStudyService.formatGroupInformation(this.groupStudyService.groupInformation!)
    this.groupStudyService.client.clientUploadStudyRecord()
    this.signDays = this.groupStudyService.getSignDays(this)
    this.groupStudyService.signDays = this.signDays
    this.actualSignDays = this.groupStudyService.signDays?.filter(it => it.isTodayBefore)
    this.groupStudyService.initialFinishStatus = this.groupInformation?.group?.members.map(it => {
      return !!this.groupInformation?.todayHistory?.find(item => item.memberId == it.userId)?.studySucceededTime
    })
    const currentUserId = this.groupStudyService.groupInformation?.currentUserId
    this.currentUserLastReadTime = this.groupStudyService.groupInformation?.group?.members.find(it => it.userId === currentUserId)?.lastReadTime
    this.setTimerTask()
  }

  ngAfterViewInit(): void {
    window.scrollTo(0, 0)
    const currentMember = this.groupInformation?.group?.members.find(it => it.isCurrentUser)
    if (!currentMember?.lastReadTime) {
      this.playConfetti()
      this.groupStudyService.track('STUDY_RECORD')
    }
  }

  informCalendarAnimation() {
    this.calender.playCalenderAnimation()
  }

  ngOnDestroy(): void {
    this.timer?.unsubscribe()
  }

  playConfetti() {
    const canvas = document.getElementById('confetti')

    // @ts-ignore
    const customConfetti = confetti.create(canvas, { resize: true })
    customConfetti({
      spread: 70,
      origin: { y: 1.2 },
      colors: ['#EB9E27', '#82C2AB', '#DC663E'],
    })
  }

  getCurrentUserRewardCount(memberId: number, rewards: History[]) {
    return rewards.find(it => it.memberId === memberId)!
  }

  setTimerTask() {
    this.groupStudyService.setTimerTask(this)
  }
}
