<div class="team-model-1">
  <div class="team-success">
    <div class="team-status">组队成功</div>
    <div class="adjusting-instruction">
      组内成员首日全部签到学习视为通过磨合期，如果组内成员在首日未完成签到学习，队伍将自动解散，报名期内可以重新组队。
    </div>
    <canvas id="confetti"></canvas>
  </div>
  <div class="adjusting-status">
    <div>
      <ng-container *ngFor="let it of memberStatus; let i = index">
        <div [id]="'avatar' + it.memberId" class="sign-status"></div>
        <img *ngIf="i !== groupInformation?.group?.members?.length! - 1"
             alt="cross"
             src="/src/assets/images/activity-guide/cross.png">
      </ng-container>
    </div>
    <swiper
        #swiper
        [slidesPerView]="1"
        [spaceBetween]="-30"
        [initialSlide]="0"
        [centerInsufficientSlides]="true">
      <ng-container *ngFor="let item of [0]; let i = index">
        <ng-template swiperSlide>
            <app-completion-status
                [actualSignDays]="actualSignDays"
                [firstDayRewardHistory]="firstDayRewardHistory"
                [currentSingDayIndex]="i"
                [groupInformation]="groupInformation"
                [specialSkin]="actualSignDays?.[i]?.specialSkin"
                (calendarAnimationEvent)="informCalendarAnimation()">
            </app-completion-status>
        </ng-template>
      </ng-container>
    </swiper>
  </div>
</div>

<app-calender #calender
              [type]="'adjusting'"
              [currentSignDetailIndex]="0"
              [initialSignDays]="signDays"
              [groupInformation]="groupInformation">
</app-calender>
