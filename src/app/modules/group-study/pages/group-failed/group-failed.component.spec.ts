import { ComponentFixture, TestBed } from '@angular/core/testing'

import { GroupFailedComponent } from './group-failed.component'

describe('GroupFailedComponent', () => {
  let component: GroupFailedComponent
  let fixture: ComponentFixture<GroupFailedComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [GroupFailedComponent],
    })
      .compileComponents()
  })

  beforeEach(() => {
    fixture = TestBed.createComponent(GroupFailedComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
