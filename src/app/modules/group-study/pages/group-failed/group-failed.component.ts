/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import { Component, OnInit } from '@angular/core'
import { GroupInformation } from '../../../group-data/group-information.model'
import { Member } from '../../../group-data/group/member.model'
import { GroupStudyService } from '../../service/group-study.service'
import { HelperService } from '../../service/services/helper.service'

@Component({
  selector: 'app-group-failed',
  templateUrl: './group-failed.component.html',
  styleUrls: ['./group-failed.component.css'],
  standalone: false,
})
export class GroupFailedComponent implements OnInit {
  groupInformation: GroupInformation | undefined

  constructor(
    public helper: HelperService,
    public groupStudyService: GroupStudyService,
  ) {
  }

  ngOnInit(): void {
    this.groupInformation = JSON.parse(JSON.stringify(this.groupStudyService.groupInformation))
    const initGroupLength = this.groupInformation?.group?.members.length!
    for (let i = 0; i < this.groupStudyService.groupTypeAndTeamNumMap.get(this.groupInformation?.group?.type!)! - initGroupLength; i++) {
      this.groupInformation?.group?.members.push({} as Member)
    }
  }
}
