/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import { Component, EventEmitter, OnInit, Output } from '@angular/core'
import { Dayjs } from 'dayjs'
import { GroupInformation } from '../../../group-data/group-information.model'
import { Member } from '../../../group-data/group/member.model'
import { History } from '../../../group-data/history/history.model'
import { TotalAward } from '../../components/total-award/total-award.component'
import { SignDay } from '../../models/model'
import { GroupStudyService } from '../../service/group-study.service'

@Component({
  selector: 'app-learning-succeed',
  templateUrl: './learning-succeed.component.html',
  styleUrls: ['./learning-succeed.component.scss'],
  standalone: false,
})
export class LearningSucceedComponent implements OnInit {
  totalStudyTime?: number
  totalStudyVoc?: number
  signDays?: SignDay[]
  currentUser?: Member
  numberOfStudiedDays?: number
  totalReward?: number
  groupInformation?: GroupInformation
  learnResult?: 'perfect' | 'nice' | 'keep'
  firstDayRewardHistory?: Dayjs
  @Output() closeReportEmitter = new EventEmitter()
  gridStyle?: string

  constructor(
    public groupStudyService: GroupStudyService,
  ) {
  }

  viceTitle = ''

  readonly viceTitles = {
    perfect: [
      '等风来，不如追风去，期待你的下一次坚持！',
      '优秀的人永远在闪闪发光，墨墨看到了你的闪耀！',
      '路途虽远，脚步未停，一起迈向下一站。',
      '好运藏在努力里，你的坚持万分值得。',
      '让坚持成为一种习惯，你做到了！',
      '相伴的结束，是旅程新的开始。',
    ],
    nice: [
      '差一点点，心有不甘，下次更要努力。',
      '路途虽远，脚步未停，一起迈向下一站。',
      '学习的路很长，庆幸我们能相伴一程。',
      '并肩而行，让下一次不再遗憾！',
      '等风来，不如追风去，期待你的下一次坚持！',
    ],
    keep: [
      '过往皆是序章，未来请继续坚持！',
      '并肩而行，让下一次不再遗憾！',
      '不必抱憾而行，挑战接踵而至。',
    ],
  }

  /**
   * key: Activity['config']['specialEvent']
   */
  readonly specialEventTitles: Record<string, {
    title: string
    viceTitle: string
  }> = {
    520: {
      title: '🕊 Respect!',
      viceTitle: '向世界上一切美好的感情致敬。',
    },
  }

  get activityConfig() {
    return this.groupStudyService.activityService.activity.config
  }

  ngOnInit(): void {
    this.groupInformation = this.groupStudyService.formatGroupInformation(this.groupStudyService.groupInformation!)
    const currentUserId = this.groupInformation?.currentUserId
    this.currentUser = this.groupInformation?.group?.members.find(it => it.userId == currentUserId)
    const currentUserHistory = this.groupInformation?.history?.filter(it => it.memberId == currentUserId)
    this.totalStudyTime = Math.floor(currentUserHistory?.map(it => it.studyTime).reduce((pre, cur) => pre + cur, 0)! / 60)
    this.totalStudyVoc = currentUserHistory?.map(it => it.learnedNewVocCount + it.learnedVocCount).reduce((pre, cur) => pre + cur, 0)
    this.totalReward = currentUserHistory?.map(it => it.rewardCount).reduce((pre, cur) => pre + cur, 0)
    this.numberOfStudiedDays = this.groupInformation?.history?.filter(it => {
      return it.studySucceededTime && it.memberId == this.groupInformation?.currentUserId
    }).length
    this.signDays = this.groupStudyService.getSignDays(this)
    this.learnResult = this.getLearnResult()
    const specialEvent = this.activityConfig?.specialEvent
    this.viceTitle = (specialEvent && this.specialEventTitles[specialEvent]?.viceTitle) ?? this.getRandomViceTitle()
    const numberOfMembers = this.groupInformation?.group?.members.length
    if (numberOfMembers == 3) {
      this.gridStyle = '1fr / 1fr 1fr 1fr'
    } else {
      this.gridStyle = '1fr / 1fr 1fr'
    }
  }

  getLearnResult() {
    let groupFinishCount = this.groupStudyService.signDays?.filter(it => {
      let temp = true
      it.records.forEach(item => {
        temp = !!(temp && item.currentRecord?.studySucceededTime)
      })
      return temp
    }).length
    groupFinishCount = groupFinishCount ? groupFinishCount : 0

    if (groupFinishCount == this.groupStudyService.totalSignDayNumber) {
      return 'perfect'
    } else if (groupFinishCount >= 11 && groupFinishCount < this.groupStudyService.totalSignDayNumber) {
      return 'nice'
    } else {
      return 'keep'
    }
  }

  getCurrentUserRewardCount(memberId: number, rewards: History[]) {
    return rewards?.find(it => it.memberId == memberId)
  }

  getRandomViceTitle() {
    const userId = this.groupStudyService.groupInformation?.group?.members.find(it => it.isCurrentUser)?.userId!
    return this.viceTitles[this.learnResult!][(userId % this.viceTitles[this.learnResult!].length)]
  }

  getBackGroundColor(key: keyof TotalAward, value?: number) {
    const temp = this.groupStudyService.individualTotalAward?.map(it => it[key])
      .sort((a, b) => (a as number) - (b as number))
    return temp?.findIndex(it => it == value)
  }

  closeReport() {
    this.closeReportEmitter.emit()
    this.groupStudyService.isShowAwardRule = true
  }
}
