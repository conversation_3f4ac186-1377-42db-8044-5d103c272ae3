<div class="container">
  <div class="picture-top-title">
    <ng-container *ngIf="specialEventTitles[activityConfig?.specialEvent!] as eventTitles; else defaultTitles">
      <div class="prompt">
        <span class="learn-result-title">{{ eventTitles.title }}</span>
        <br>
        {{ eventTitles.viceTitle }}
      </div>
    </ng-container>
    <ng-template #defaultTitles>
      <div class="prompt" [ngSwitch]="learnResult">
        <ng-container *ngSwitchCase="'keep'">
          <!--         <img [src]="'assets/images/learn_success/keep_it_up.png' | cdn" width="177px"> -->
          <span class="learn-result-title">💪keep it up</span>
          <br>
          {{viceTitle}}
        </ng-container>
        <ng-container *ngSwitchCase="'nice'">
          <!--         <img [src]="'assets/images/learn_success/nice.png' | cdn" width="88px"> -->
          <span class="learn-result-title">👍nice</span>
          <br>
          {{viceTitle}}
        </ng-container>
        <ng-container *ngSwitchCase="'perfect'">
          <!--         <img [src]="'assets/images/learn_success/perfect.png' | cdn" width="144px"> -->
          <span class="learn-result-title">🎉perfect</span>
          <br>
          {{viceTitle}}
        </ng-container>
      </div>
    </ng-template>
    <!--     <img [src]="'assets/images/learn_success/momo.png' | cdn" class="momo"> -->
    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAANPSURBVHic7Z3NkaMwEEY/qL2oyIA4zHnsCBzS2lE4DkcwzNnEQQaUb+weAA9gMMLulpDU7+Q/ZNWrLkk0KnWED6iq6gQAcRx/tR/tP2lvQ+S912cAUErlk79cIFp7QVVVp1bo/p0/dJi8ruufJElOay7SFtyK/bu6Wx5S1/VZV/SiYBH7ksPS0BG/+lLkLvLdzUNzzEawyNXn1ZAxKVjkrmdO8pNgkfs+U5IHgu/3+x7At8E+ecdY8ljwP+M98hCl1MPrYxWxNBsK+vRdPkxL9NLSRXEMSPRy8MjTAINkjUBE5zSSlQMfSqno5a2y8BlVVZ1iAHJTwcgf2x3YCrfbDWVZoixL7HY7ZFn2cZtxHH+JYDRyi6J4vO9ek0hGeE8mBozldpRlSdH8PuhJbk4uQCbY3TG4L+edMfOV3K5NCpyM4Kkx83a7vX39GKpJDnBU8JScV8L6mJQLOCr4XUzLBQISbEMuEIhgW3KBAATblAt4Lti2XMBjwVuQC3gqeCtyAQ8Fb0ku4JngrckFPBO8NbmAZ4LnsCUXCECwTbmA54JtywUczgfrUBSFVpZNbpWZWZtPXoMIbtHNJ69FBDPjpOA0TcnbpHoGN8ZJwcfjkVQy5yQXyb5gXpyMYJdgWwcvJV7WkKYpjscjSVumYYlgSrlAs8vmer2StWcSFsEca0qqrUymkTGYGRbBXGtKF2ERnGWZSG5hW0VkWaa1eKeeELeG1THYd7mARcEhyAUsCQ5FLmBBsKmd5VvBqOAt7lvgxpjgEOUChgSHKhcwIDhkuQCz4NDlAoyCRW6DlXxwKHIBBsEidwipYJH7DJlgSrlTd3Ou3uGRCKaO3HE+2eXIJ9kXcblcZr9zWQ4FJBE8t8smdLlAIzj/tJEpwSIXAJCTPDLqRBZFgTRNkaapyG2J5LxgVnLZF8HLOQLk5FVGDl0E5zZ74Sm5UqoZIuq6/rHdG9/onMoBzUwMDmgGmsPd7XXHL/ou5ZB8BiYPyQckiok49N8MBCdJchLJ71PX9XlcPEpK7RChXWqnQyTrs7pYVIdIXmapeN9iwT6pUjCPTmXExWSPUipXSkUy+f3STmaRTtlJKZqqD2/R1DHt0AEMy0Tsn3/pJDnwm09YK7XPf9aL6QCao3g9AAAAAElFTkSuQmCC" class="shrink" (click)="closeReport()">
  </div>
  <app-medal-waller [gridStyle]="gridStyle"></app-medal-waller>
<!--   <div class="success-report"> -->
<!--     <div class="success-report-level-1"> -->
<!--       <img *ngIf="currentUser?.avatar" class="user-avatar" [src]="currentUser?.avatar"> -->
<!--       {{currentUser?.name}} -->
<!--     </div> -->
<!--     <div class="success-report-level-2">累计学习 <span class="theme-color">{{numberOfStudiedDays}}</span> 天</div> -->
<!--     <div class="success-report-level-2">学习 <span class="theme-color">{{totalStudyTime}}</span> 分钟，<span -->
<!--         class="theme-color">{{totalStudyVoc}}</span> 次单词 -->
<!--     </div> -->
<!--     <div class="success-report-level-2">共获得 <span class="theme-color">{{totalReward}}</span> 个单词上限</div> -->
<!--   </div> -->
  <!--   <app-calender -->
  <!--       [type]="'learningSuccess'" -->
  <!--       [initialSignDays]="signDays" -->
  <!--       [groupInformation]="groupInformation"></app-calender> -->
  <div class="study-status-container">
    <div class="title">学习情况</div>
    <div
        class="study-status"
        [style.grid]="gridStyle">
      <div *ngFor="let it of groupStudyService.individualTotalAward">
        <ng-container
            *ngTemplateOutlet="reportCard; context: {$implicit: '', key: 'finishDays',value: it}"></ng-container>
        <ng-container
            *ngTemplateOutlet="reportCard; context: {$implicit: '', key: 'newLearnWordNumber',value: it}"></ng-container>
        <ng-container
            *ngTemplateOutlet="reportCard; context: {$implicit: '', key: 'familiarVocCount',value: it}"></ng-container>
        <ng-container
            *ngTemplateOutlet="reportCard; context: {$implicit: '', key: 'totalWords',value: it}"></ng-container>
        <ng-container
            *ngTemplateOutlet="reportCard; context: {$implicit: '', key: 'totalTime',value: it}"></ng-container>
      </div>
    </div>
  </div>

  <div class="hardest-day-container">
    <div class="title">最努力</div>
    <div
        class="hardest-day"
        [style.grid]="gridStyle">
      <div *ngFor="let it of groupStudyService.individualTotalAward">
        <div>
          {{it.longestLearningTimeDate | date: 'M 月 d 日' : 'UTC+8'}}
        </div>
      </div>
    </div>
  </div>

  <!-- this is a template for generating study status elements, i dont want to use component, because it is top easy that it don't import a component -->
  <ng-template #reportCard let-key="key" let-it="value">
    <div
        [class.green]="getBackGroundColor(key,it[key]) === (groupStudyService.groupInformation!.group!.members!.length - 3) || 0"
        [class.yellow]="getBackGroundColor(key,it[key]) === (groupStudyService.groupInformation!.group!.members!.length - 2) || 0"
        [class.red]="getBackGroundColor(key,it[key]) === (groupStudyService.groupInformation!.group!.members!.length - 1) || 0">
      <div>
        <ng-container *ngIf="it[key] !== undefined">
          {{it[key]}}
          <ng-container [ngSwitch]="key">
            <ng-container *ngSwitchCase="'finishDays'">天</ng-container>
            <ng-container *ngSwitchCase="'newLearnWordNumber'">个</ng-container>
            <ng-container *ngSwitchCase="'familiarVocCount'">
              <ng-container *ngIf="it[key] !== undefined">
                个
              </ng-container>
            </ng-container>
            <ng-container *ngSwitchCase="'totalWords'">次</ng-container>
            <ng-container *ngSwitchCase="'totalTime'">分</ng-container>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="it[key] === undefined">
          暂无数据
        </ng-container>
      </div>
      <div>
        <ng-container [ngSwitch]="key">
          <ng-container *ngSwitchCase="'finishDays'">完成任务</ng-container>
          <ng-container *ngSwitchCase="'newLearnWordNumber'">新学单词</ng-container>
          <ng-container *ngSwitchCase="'familiarVocCount'">巩固单词</ng-container>
          <ng-container *ngSwitchCase="'totalWords'">复习单词</ng-container>
          <ng-container *ngSwitchCase="'totalTime'">学习时长</ng-container>
        </ng-container>
      </div>
    </div>
  </ng-template>

