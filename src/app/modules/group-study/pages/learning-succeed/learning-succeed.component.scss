.container {
  background: var(--bg-color-2);
  margin: 0 auto 10px;
}

.success-report {
  box-sizing: border-box;
  background: var(--bg-color-1);
  width: 98%;
  margin: 0 auto;
  border-radius: 4px;
  padding: 25px;
  color: var(--title-color)
}

.success-report-level-1 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-bottom: 20px;
  font-size: 14px
}

.success-report-level-1 > img {
  width: 40px;
  display: inline-block;
  margin-right: 14px;
}

.success-report-level-2 {
  margin-bottom: 10px;
}

.picture-top-title {
  display: flex;
  position: relative;
  padding:29px 16px 13px;
  color: var(--second-font-color);
}

.picture-top-title > img {
  display: inline-block;
  vertical-align: middle;
}

.picture-top-title > span {
  display: inline-block;
  transform: translate(-10px, 5px);
}

.momo {
  position: absolute;
  width: 38%;
  transform: translateY(10%);
  bottom: 0;
  right: 10px;
}

.user-avatar {
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.prompt {
  font-weight: 600;
  color: var(--title-color);
}

.title {
  font-size: 11px;
  color: var(--font-color-1);
  font-weight: 600;
  padding-left: 6px;
}

.study-status {
  display: grid;
}

.hardest-day-container {
  margin: 0 10px;
}

.hardest-day {
  display: grid;
  grid: 1fr / 1fr 1fr 1fr;
}

.study-status > div > div {
  border-radius: 4px;
  margin: 6px;
  padding-top: 7px;
  padding-bottom: 6px;
}

.study-status > div > div > div {
  padding-left: 15px;
}

.study-status > div > div > div:first-child {
  font-size: 14px;
  color: var(--title-color);
}

.study-status > div > div > div:nth-child(2) {
  font-size: 12px;
  color: var(--second-font-color);
}

.red {
  background-color: #fbefeb;
}

.yellow {
  background-color: #fdf5e9;
}

.green {
  background-color: #eef5f2;
}

.hardest-day > div {
  background-color: var(--bg-color-1);
  border-radius: 4px;
  margin: 6px;
  padding-top: 7px;
  padding-bottom: 6px;
  text-align: center;
  color: var(--title-color);
  font-weight: 600;
  font-size: 14px;
}

.shrink {
  position: absolute;
  right: 15px;
  top: 15px;
  width: 22px;

  &:active {
     opacity: var(--activity-opacity)
   }
}

.learn-result-title {
  display: inline-block;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 7px;
}

.study-status-container {
  margin: 0 10px;
}
