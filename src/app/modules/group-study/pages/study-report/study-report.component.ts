import { Component, inject } from '@angular/core'
import { DomSanitizer, SafeHtml } from '@angular/platform-browser'
import { AppConfig } from '../../../../configs/app.config'
import { UserService } from '../../../core/services/user.service'
import { Activity } from '../../../group-study-v2/models/activity.model'
import { GroupData } from '../../../group-study-v2/models/group.model'
import { Report } from '../../../group-study-v2/models/report.model'
import { generateStudyReports, getMainStatsList, getSubStatsList } from '../../../group-study-v2/pages/study-report/helper'
import { MemberStudyReport, StatsItem } from '../../../group-study-v2/pages/study-report/type'
import { getColorRGBFromColorType } from '../../../shared/helper/color'
import { dayjsWithTZ, fromSeconds } from '../../../shared/helper/time'
import { GroupStudyService } from '../../service/group-study.service'

@Component({
  selector: 'app-study-report',
  templateUrl: './study-report.component.html',
  styleUrls: ['./study-report.component.scss'],
  standalone: false,
})
export class StudyReportComponent {
  private groupStudyService = inject(GroupStudyService)
  private userService = inject(UserService)
  private domSanitizer = inject(DomSanitizer)

  readonly appConfig = AppConfig

  studyStats?: MemberStudyReport[]

  currentUserStats?: MemberStudyReport
  mainStatsItemsMap: Record<number, StatsItem[]> = {}
  subStatsItemChunksMap: Record<number, StatsItem[][]> = {}

  private statsHTMLCache: Record<string, SafeHtml> = {}

  get userInfo() {
    return this.userService.userInfo
  }

  get activity() {
    return this.groupStudyService.activityService.activity as Activity<'GROUP_STUDY'>
  }

  colorRGBMap = {
    'green-1': getColorRGBFromColorType('green-1'),
    yellow: getColorRGBFromColorType('yellow'),
    red: getColorRGBFromColorType('red'),
  }

  private statsItemHandlers: Partial<Record<StatsItem['name'], (item: StatsItem) => string>> = {
    totalRewarded: item => {
      return `${item.text} ${this.genStatsDataHTML(item.data[0].value)} ${item.data[0].unit ?? ''}`
    },
    totalStudyDays: item => {
      return `${this.genStatsDataHTML(item.data[0].value)} 天`
    },
    totalStudyDuration: item => {
      return `${this.genStatsDataHTML(item.data[0].value)} 分钟`
    },
    hardestDay: item => {
      const [month, day] = item.data
      return `${this.genStatsDataHTML(month.value)} ${month.unit ?? ''} ${this.genStatsDataHTML(day.value)} ${day.unit ?? ''}最努力`
    },
    totalVocCount: item => {
      return `${this.genStatsDataHTML(item.data[0].value)} 次学习单词`
    },
    totalNewVocCount: item => {
      return `${this.genStatsDataHTML(item.data[0].value)} 个新学`
    },
    totalFamiliarVocCount: item => {
      return `${this.genStatsDataHTML(item.data[0].value)} 个顽固`
    },
  }

  groupDateRangeText = this.getGroupDateRangeText()

  constructor() {
    const groupInformation = this.groupStudyService.groupInformation as GroupData
    const activity = this.groupStudyService.activityService.activity as Activity<'GROUP_STUDY'>

    this.groupStudyService.getReport().subscribe(r => {
      const reports = r as Report[]
      const stats = generateStudyReports(groupInformation, activity, reports)
      const otherMembersStats: MemberStudyReport[] = []
      for (const item of stats) {
        if (item.userId === this.userInfo?.userId) {
          this.currentUserStats = item
          continue
        }
        otherMembersStats.push(item)
      }
      this.studyStats = otherMembersStats

      stats.forEach(report => {
        this.mainStatsItemsMap[report.userId] = getMainStatsList(report).slice(0, 1)
        this.subStatsItemChunksMap[report.userId] = getSubStatsList(report)
      })
    })
  }

  getTextFromStatsItem(item: StatsItem, id: string): SafeHtml {
    const cache = this.statsHTMLCache[id]
    if (cache) {
      return cache
    }
    let content = ''
    const handler = this.statsItemHandlers[item.name]

    if (handler) {
      content = handler(item)
    }

    const html = this.domSanitizer.bypassSecurityTrustHtml(content)
    this.statsHTMLCache[id] = html
    return html
  }

  genStatsDataHTML(data: number | string): string {
    return `<span style="color: rgba(var(--theme), 1);">${data}</span>`
  }

  private getGroupDateRangeText() {
    const activity = this.activity
    const group = this.groupStudyService.groupInformation?.group
    if (!group?.studyStartTime || !activity.config) return ''
    const { studyDuration } = activity.config
    const studyDays = fromSeconds(studyDuration, 'day')
    const start = dayjsWithTZ(group.studyStartTime)
    const end = start.add(studyDays, 'day')
    return `${start.format('MM.DD')} - ${end.format('MM.DD')}`
  }
}
