<app-report-page
  [currentUserStats]="currentUserStats"
  [mainStatsItems]="userInfo ? mainStatsItemsMap[userInfo.userId] : []"
  [subStatsItemChunks]="userInfo ? subStatsItemChunksMap[userInfo.userId] : []"
  [userInfo]="userInfo"
  [groupDateRangeText]="groupDateRangeText"
></app-report-page>

<app-card>
  @if(studyStats) {
    <section class="group-section">
      <h3>队友信息</h3>
      <div class="members-container">
        @for (member of studyStats; track member.userId) {
          <div class="member-item">
            <div class="user-info-container">
              <img class="user-avatar" [src]="member.avatar || appConfig.defaultAvatarPath" alt="">
              <span class="truncate">{{member.name}}</span>
            </div>
            <div class="user-study-info">
              @for(item of mainStatsItemsMap[member.userId]; track item.name) {
                <div [style.--theme]="colorRGBMap['green-1']">
                  <p [innerHTML]="getTextFromStatsItem(item, member.userId + '-' + item.name)"></p>
                </div>
              }
              @for(chunk of subStatsItemChunksMap[member.userId]; track $index;) {
                <div [style.--theme]="$index === 0 ? colorRGBMap['yellow'] : colorRGBMap['red']">
                  <p>
                    @for(item of chunk; track item.name) {
                      <span class="with-gap" [innerHTML]="getTextFromStatsItem(item, member.userId + '-' + item.name)"></span>
                    }
                  </p>
                </div>
              }
            </div>
          </div>
        }
      </div>
    </section>
  }
  @else {
    <section class="rank-section">
      <ion-skeleton-text [animated]="true" style="width: 5em;"></ion-skeleton-text>
      <div class="members-container">
        @for(item of 2 | list; track item) {
          <div class="member-item">
            <div class="user-info-container">
              <ion-thumbnail class="user-avatar">
                <ion-skeleton-text [animated]="true"></ion-skeleton-text>
              </ion-thumbnail>
              <ion-skeleton-text [animated]="true" style="width: 3em;"></ion-skeleton-text>
            </div>
            <div class="user-study-info">
              <div>
                @for(v of 3 | list; track v) {
                  <ion-skeleton-text [animated]="true" style="width: 100%; line-height: 1.5;"></ion-skeleton-text>
                }
              </div>
            </div>
          </div>
        }
      </div>
    </section>
  }
</app-card>
