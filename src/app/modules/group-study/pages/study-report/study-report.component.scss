:host {
  --header-bg-height: 150px;
  --gap-base: 5px;

  display: block;
  width: 100%;
  padding-bottom: 40px;
  color: var(--font-color-1);
}

:host,
:host * {
  box-sizing: border-box;
}

:host > app-card {
  margin-top: 7px;
}

.members-container {
  display: flex;
  flex-direction: column;
}

.member-item {
  display: flex;
  padding: calc(var(--gap-base) * 3) 0;

  &:not(:first-child) {
    border-top: 1px dashed var(--border-color);
  }
}

.user-info-container {
  --size: 55px;
  display: flex;
  flex-direction: column;
  margin-right: calc(var(--gap-base) * 2);
  width: var(--size);

  & > span {
    display: inline-block;
    margin-top: var(--gap-base);
    font-size: 12px;
  }
}

.user-avatar {

  width: var(--size);
  height: var(--size);
  border-radius: 4px;
  border: 2px solid var(--border-color);
  object-fit: contain;
}

.user-study-info {
  flex: 1;
  min-width: 0;
  font-size: 12px;
  font-weight: 500;

  & > div {
    text-align: center;
    border-radius: 5px;
    background-color: rgba(var(--theme), 0.15);

    &:not(:first-child) {
      margin-top: var(--gap-base);
    }
  }

  p {
    margin: 0;
    margin-bottom: 1px;
    padding: calc(var(--gap-base) / 2) 0;
    transform: scale(0.85) translateY(-1px);
    transform-origin: center center;
  }
}

.with-gap:not(:first-child) {
  margin-left: 0.5em;
}

.group-section {
  &:not(:first-child) {
    margin-top: calc(var(--gap-base) * 4);
  }

  h3 {
    font-size: 1rem;
    font-weight: bold;
    margin-top: 0;
    margin-bottom: 9px;
  }
}
