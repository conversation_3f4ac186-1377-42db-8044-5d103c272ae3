.activity-guide {
  background: var(--bg-color-2);
  border-radius: 0px 0px 10px 10px;
  margin: 0 auto;
  padding-bottom: 15px;
  width: var(--container-width);
}

.title {
  padding-top: 20px;
  color: var(--title-color);
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}

.circle-1 {
  display: inline-block;
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: rgba(196, 196, 196, 1);
  vertical-align: center;
  transform: translate(0.2rem, -0.2rem);
}

.circle-2 {
  display: inline-block;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background-color: rgba(196, 196, 196, 1);
  vertical-align: center;
  transform: translate(-0.6rem, -0.25rem);
}

.hint {
  background: var(--bg-color-1);
  border-radius: 6px;
  margin: 20px auto;
  font-size: 13px;
}

.step-chart {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 25px 25px;
}

.step-chart > div {
  position: relative;
}

.step-font {
  position: absolute;
  width: 80px;
  left: -74%;
  color: rgba(170, 170, 170, 1);
  font-size: 13px;
}

.dotted-line {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 22%;
  height: 2px;
  background: linear-gradient(to right, #d2d2d2 50%, white 50%);
  background-size: 10px 10px;
  transform: translateY(-2px);
}

.hint-level-1 {
  width: 90%;
  height: 40px;
  margin: 0 auto;
  padding: 10px 0;
  line-height: 20px;
  font-size: 13px;
  color: var(--title-color);
}

@media (min-width: 640px) {
  .hint-level-1 {
    text-align: center;
  }
}

.hint-level-2 {
  width: 90%;
  height: 40px;
  margin: 10px auto;
}

.keyword {
  font-weight: 800;
  color: var(--color-contrast);
}

.container {
  width: var(--container-width);
  margin: 0 auto;
  overflow: hidden;
  background: var(--bg-color-2);
  padding-bottom: 20px;
  border-radius: 10px
}
