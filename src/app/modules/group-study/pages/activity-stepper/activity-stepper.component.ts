import { Component, Inject, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { ROUTE_BASE_HREF } from '../../../entry/group-study.module'
import { GroupStudyService } from '../../service/group-study.service'
import { HelperService } from '../../service/services/helper.service'

@Component({
  selector: 'app-activity-stepper',
  templateUrl: './activity-stepper.component.html',
  styleUrls: ['./activity-stepper.component.css'],
  standalone: false,
})
export class ActivityStepperComponent implements OnInit {
  constructor(
    public groupStudyService: GroupStudyService,
    public helper: HelperService,
    public router: Router,
    @Inject(ROUTE_BASE_HREF) private routeBase: string,
  ) {
  }

  ngOnInit(): void {
    if (this.groupStudyService.activityService.isActivityToEnd()) {
      this.router.navigateByUrl(`${this.routeBase}activityTermination`, { skipLocationChange: true })
    }
  }
}
