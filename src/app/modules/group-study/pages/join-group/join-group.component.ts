import { Component, OnInit, inject } from '@angular/core'
import { UserService } from '../../../core/services/user.service'
import { Member } from '../../../group-data/group/member.model'
import { GroupStudyService } from '../../service/group-study.service'
import { JoinGroupService } from './join-group.service'

@Component({
  selector: 'app-join-group',
  templateUrl: './join-group.component.html',
  styleUrls: ['./join-group.component.scss'],
  standalone: false,
})
export class JoinGroupComponent implements OnInit {
  private userService = inject(UserService)

  invitationUserName?: string
  initialNumberOfMember?: number

  get groupInformation() {
    return this.groupStudyService.groupInformation
  }

  constructor(
    public joinGroupService: JoinGroupService,
    public groupStudyService: GroupStudyService,
  ) {
  }

  joinGroup(it: Member | Record<string, unknown>) {
    if (Object.keys(it).length != 0 || this.userService.checkDebtLearning()) {
      return
    }
    if (!this.groupStudyService.invitedCode) {
      this.groupStudyService.client.clientToast('邀请码不存在', 'FAILURE')
      return
    }
    this.joinGroupService.joinGroup(this.groupStudyService.invitedCode).subscribe(it => {
      this.groupStudyService.groupInformation = it
      this.groupStudyService.client.clientToast('加入队伍成功', 'SUCCESS')
      this.groupStudyService.client.clientOpenNotificationPermissionIfNeed('打开通知权限', '授权开启系统通知权限，才能收到队友拍一拍提醒。')
      this.groupStudyService.client.clientUploadStudyRecord()
      this.groupStudyService.navigate()
    })
  }

  ngOnInit(): void {
    this.initialNumberOfMember = this.groupInformation?.group?.members.length
    // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
    for (let i = 0; i < this.groupStudyService.groupTypeAndTeamNumMap.get(this.groupInformation?.group?.type!)! - this.initialNumberOfMember!; i++) {
      this.groupInformation?.group?.members.push({} as Member)
    }
    this.invitationUserName = this.groupInformation?.group?.members.find(it => it.userId == this.groupInformation?.inviteUserId)?.name
  }
}
