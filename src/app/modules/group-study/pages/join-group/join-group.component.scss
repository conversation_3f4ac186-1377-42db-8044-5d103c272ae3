.team-creating {
  background: var(--bg-color-2);
  border-radius: 10px;
  padding-bottom: 20px;
  width: var(--container-width);
  margin: 0 auto;
}

.portraits {
  display: flex;
  width: 100%;
}

.count-down {
  padding-top: 20px;
  text-align: center;
}

.detailed-time {
  margin-top: 10px;
  font-size: 30px;
  color: var(--font-color-1);
}

.portraits {
  display: flex;
  justify-content: space-evenly;
  margin-top: 20px;
}

.portrait {
  position: relative;
  z-index: 3;
}

.portraits > .user {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.username {
  margin-top: 3px;
  width: 74px;
  text-align: center;
  color: var(--font-color-2);
  font-size: 14px;
}

.portraits > div > div:nth-child(1) {
  width: 54px;
  height: 54px;
  border: 1px dashed var(--border-color);
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.portraits > div > div:nth-child(1)::after,
.portraits > div > div:nth-child(1)::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 2px;
  background: var(--border-color);
  position: absolute;
}

.portraits > div > div:nth-child(1)::before {
  transform: rotate(90deg);
}

.team-people-hint {
  margin-top: 25px;
  text-align: center;
  color: #aaa;
  font-size: 14px;
}

.invite {
  width: 60%;
  height: 42px;
  background: rgba(54, 181, 157, 1);
  border-radius: 4px;
  color: var(--bg-color-2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px auto 0px;
  cursor: pointer;

  &:active {
     opacity: var(--activity-opacity)
   }
}

.inviter-name {
  max-width: 80%;
  color: var(--font-color-1);
  font-size: 17px;
  margin: 25px auto 0px;
  text-align: center;
  font-weight: 800;
}

.limit-name-length {
  display: inline-block;
  max-width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
}
