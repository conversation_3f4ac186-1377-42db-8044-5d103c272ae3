import { Component, Input, OnInit } from '@angular/core'
import { cloneDeep } from 'lodash-es'
import { GroupInformation } from '../../../group-data/group-information.model'
import { GroupStudyService } from '../../service/group-study.service'

@Component({
  selector: 'app-medal-waller',
  templateUrl: './medal-waller.component.html',
  styleUrls: ['./medal-waller.component.css'],
  standalone: false,
})
export class MedalWallerComponent implements OnInit {
  groupInformation: GroupInformation | undefined
  defaultAvatarSrc = 'assets/images/default-avatar.png'
  @Input() gridStyle?: string

  constructor(
    public groupStudyService: GroupStudyService,
  ) { }

  ngOnInit(): void {
    this.groupInformation = cloneDeep(this.groupStudyService.groupInformation)
  }
}
