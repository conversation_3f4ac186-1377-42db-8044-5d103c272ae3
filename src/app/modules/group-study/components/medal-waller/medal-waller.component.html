<div
    class="medal-waller-container"
    [style.grid]="gridStyle">
  <div *ngFor="let it of groupInformation?.group?.members" class="img-container">
    <div style="width: 100%;background: var(--bg-color-1)">
      <img width="54px" class="portrait" [src]="it.avatar.length === 0 ? defaultAvatarSrc : it.avatar"  />
      <div class="username">
        <span>{{it.name}}</span>
      </div>
    </div>
<!--     <div class="medal-waller"> -->
<!--     </div> -->
  </div>
</div>
