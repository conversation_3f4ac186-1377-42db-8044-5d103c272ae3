/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  Renderer2,
  ViewChildren,
} from '@angular/core'
import { Dayjs } from 'dayjs'
import { cloneDeep, flatten } from 'lodash-es'
import { AnimationItem } from 'lottie-web'
import { AppConfig } from '../../../../configs/app.config'
import { GroupInformation } from '../../../group-data/group-information.model'
import { Member } from '../../../group-data/group/member.model'
import { History } from '../../../group-data/history/history.model'
import { Report } from '../../../group-data/report/report.model'
import { getColorFromColorType } from '../../../shared/helper/color'
import { dayjsWithTZ } from '../../../shared/helper/time'
import { CalendarCardIconType, ColorType, SignDay } from '../../models/model'
import { GroupStudyService } from '../../service/group-study.service'
import { Animation, AnimationService } from '../../service/services/animation.service'

@Component({
  selector: 'app-calender',
  templateUrl: './calender.component.html',
  styleUrls: ['./calender.component.scss'],
  standalone: false,
})
export class CalenderComponent implements OnInit, AfterViewInit {
  constructor(
    public groupStudyService: GroupStudyService,
    public animationService: AnimationService,
    public renderer: Renderer2,
  ) {
  }

  isAllFinished = false
  currentUser?: Member
  calenderAnimations: AnimationItem[] = []
  successTime?: (string | undefined)[]
  fixedGroupMember?: number[]
  updateCount = 0
  appConfig = AppConfig
  signDays: SignDay[] = []
  firstDayRewardHistory?: Dayjs
  isTodayBefore = true
  numberOfStudiedDays?: number = 0
  groupInformationTemp?: GroupInformation
  array = Array
  initialFinishStatus?: boolean[] = []
  @ViewChildren('cardWrapper') cardWrappers!: ElementRef[]

  get groupInformation(): GroupInformation | undefined {
    return this.groupInformationTemp
  }

  @Output() sliderMoveEvent = new EventEmitter()
  @Output() viewTotalAwardEvent = new EventEmitter()

  @Input() currentReport?: Report
  @Input() initialSignDays?: SignDay[]
  @Input() type?: string
  @Input() actualSignDays?: SignDay[]

  @Input() set groupInformation(value) {
    this.groupInformationTemp = value
    this.currentUser = this.groupInformation?.group?.members.find(it => it.isCurrentUser)
    this.numberOfStudiedDays = this.groupInformation?.history?.filter(it => {
      return it.studySucceededTime && it.memberId == this.groupInformation?.currentUserId
    }).length
    this.firstDayRewardHistory = dayjsWithTZ(this.groupInformation?.group?.studyStartTime)
    this.isTodayBefore = true
    this.signDays = this.getSignDaysInCalender()
    this.groupStudyService.signDays = this.signDays
    this.setDailyAwardNumber()
    this.setOverallCalendarColor()
    this.setIndividualTotalInfo()
    this.isAllFinished = this.getIsAllFinished()
    if (this.updateCount >= 1) {
      this.animationService.clearAnimationByDay(this.actualSignDays?.length! - 1)
      this.setCalenderAnimation()
    }
    this.updateCount++
  }

  @Input() currentSignDetailIndex?: number

  ngOnInit(): void {
    this.initialFinishStatus = this.signDays.find(it => it.isToday)?.records.map(it => !!it.user.todayHistory?.studySucceededTime)
    this.firstDayRewardHistory = dayjsWithTZ(this.groupInformation?.group?.studyStartTime)
    this.actualSignDays = this.signDays?.filter(it => it.isTodayBefore)
    if (this.groupInformation?.group?.type == 'COUPLE') {
      this.fixedGroupMember = [1, 2]
    } else {
      this.fixedGroupMember = [1, 2, 3]
    }
  }

  ngAfterViewInit(): void {
    if (this.groupInformation?.group?.status == 'LEARN_SUCCEED') {
      const shineAnimation = new Animation()
      shineAnimation.type = 'shine'
      shineAnimation.play = () => {
        this.playCalendarShine()
      }
      shineAnimation.config.day = this.actualSignDays?.length! - 1
      this.animationService.animations.push(shineAnimation)
    }
    this.setCalenderAnimation()
    let animationSequence
    if ((this.actualSignDays?.length)) {
      for (let i = 0; i < (this.actualSignDays?.length!); i++) {
        animationSequence = this.animationService.getAnimationSequence(i)!
      }
      this.animationService.playAnimationByDay(this.actualSignDays?.length - 1, animationSequence)
    }
  }

  getSignDaysInCalender(): SignDay[] {
    const tempSignDays = cloneDeep(this.initialSignDays)
    tempSignDays?.forEach(it => {
      const tempRewards = it.records.map(item => item.currentRecord) as History[]
      it.colorType = this.getDailyCalendarColor(tempRewards)
      it.records.forEach(it => {
        it.calendarCardIconType = this.getCalendarCardIconType(it.currentRecord!)
      })
    })
    this.groupStudyService.lastSignDayInfo = tempSignDays![tempSignDays!.length - 1]
    return tempSignDays!
  }

  getDailyCalendarColor(rewards: History[]): ColorType {
    if (rewards[0]?.rewardStatus == 'CANCELLED') {
      return 'normal'
    }
    const tempRewardsCount = rewards.map(it => it?.rewardCount)
    if (this.groupInformation?.group?.type == 'COUPLE') {
      if (tempRewardsCount.length < 2) {
        return 'normal'
      }
      if (tempRewardsCount[0] == tempRewardsCount[1] && tempRewardsCount[0] == 1) {
        return 'green'
      }
      if (tempRewardsCount[0] == tempRewardsCount[1] && tempRewardsCount[0] == 4) {
        return 'red'
      }
    } else {
      if (tempRewardsCount.length < 3) {
        return 'normal'
      }
      if (tempRewardsCount.includes(0)) {
        return 'normal'
      }
      if (tempRewardsCount[0] == tempRewardsCount[1] && tempRewardsCount[1] == tempRewardsCount[2] && tempRewardsCount[0] == 9) {
        return 'red'
      } else if (tempRewardsCount[0] == tempRewardsCount[1] && tempRewardsCount[1] == tempRewardsCount[2] && tempRewardsCount[0] == 1) {
        return 'green'
      } else {
        return 'yellow'
      }
    }
    return 'normal'
  }

  setDailyAwardNumber() {
    this.signDays.forEach(signDay => {
      signDay.records.forEach(record => {
        if (record?.currentRecord?.rewardStatus == 'CANCELLED') {
          record.currentRecord.rewardCount = 0
        }
      })
    })
  }

  setIndividualTotalInfo(): void {
    const recordOfAllUsers = flatten(this.signDays.map(it => it.records)).filter(it => it.currentRecord)
    this.groupInformation?.group?.members.forEach(it => {
      const totalAwards = recordOfAllUsers.filter(item => it.userId == item.user.userId).map(r => r.currentRecord?.rewardCount)
        .filter(it => it !== undefined)
        ?.reduce((pre, cur) => pre! + cur!, 0)
      let totalStudyTime = recordOfAllUsers.filter(item => it.userId == item.user.userId).map(r => r.currentRecord?.studyTime)
        .filter(it => it !== undefined)
        ?.reduce((pre, cur) => pre! + cur!, 0)
      totalStudyTime = totalStudyTime ? totalStudyTime : 0
      totalStudyTime = Math.floor(totalStudyTime / 60)
      const totalVocCount = recordOfAllUsers.filter(item => it.userId == item.user.userId).map(r => {
        if (!r.currentRecord) {
          return 0
        }
        r.currentRecord.learnedVocCount = r.currentRecord.learnedVocCount || 0
        r.currentRecord.learnedNewVocCount = r.currentRecord.learnedNewVocCount || 0
        return r.currentRecord.learnedVocCount + r.currentRecord.learnedNewVocCount
      })
        .filter(it => it !== undefined)
        ?.reduce((pre, cur) => pre! + cur!, 0)
      it.totalAwards = totalAwards ? totalAwards : 0
      it.totalStudyTime = totalStudyTime
      it.totalVocCount = totalVocCount
    })
  }

  getCalendarCardIconType(history: History): CalendarCardIconType {
    if (!history) {
      return 'cross'
    }
    const isFinish = !!history.studySucceededTime
    if (isFinish) {
      return 'number'
    } else {
      return 'cross'
    }
  }

  setOverallCalendarColor(): void {
    this.signDays.forEach((it, index, arr) => {
      if (it.reward_status == 'CANCELLED') {
        for (let i = 0; i < index; i++) {
          arr[i].colorType = 'normal'
        }
      }
      if (!it.isTodayBefore) {
        it.colorType = 'opacity'
      }
    })
  }

  getIsAllFinished() {
    return this.groupInformation?.todayHistory?.filter(it => it.studySucceededTime).length == this.groupInformation?.group?.members.length
  }

  setCalenderAnimation() {
    const firstFrame = [0, 1]
    const overallFrame = [0, 25]
    const finalFrame = [25, 25]
    let lastSign = this.signDays.filter(it => it.isTodayBefore).slice(-1)[0]
    const groupLastSuccessTime = this.getGroupLastSuccessTime(lastSign.records)
    lastSign = lastSign ? lastSign : this.groupStudyService.lastSignDayInfo!
    const isCurrentUserFinished = this.getIsCurrentUserFinished(lastSign)
    lastSign.records.forEach((it, index) => {
      const tempDay = this.actualSignDays?.length! - 1
      const numberAnimationDom = document.querySelector('#user-number-' + index + '-' + tempDay)!
      let initialSegment = finalFrame
      if (!this.currentUser?.lastReadTime) {
        initialSegment = overallFrame
      } else if (dayjsWithTZ(groupLastSuccessTime).valueOf() > new Date(this.currentUser?.lastReadTime!).getTime()) {
        if (!this.isAllFinished) {
          initialSegment = firstFrame
        } else {
          initialSegment = overallFrame
        }
      } else {
        initialSegment = finalFrame
      }
      if (!this.isAllFinished) {
        initialSegment = firstFrame
      }
      if (!it.currentRecord?.studySucceededTime) {
        initialSegment = [-1, -1]
      }

      const animation = new Animation()
      animation.name = '日历奖励单词数动画'
      animation.config.reward_count = it.user.todayHistory?.rewardCount + ''

      animation.config.animationColor = this.getAnimationColor(lastSign)
      animation.config.element = numberAnimationDom
      animation.config.day = tempDay
      animation.type = 'calendar'
      animation.config.initFrame = initialSegment

      if (lastSign.specialSkin && isCurrentUserFinished) {
        animation.config.specialSkin = lastSign.specialSkin
      }

      this.animationService.animations.push(animation)
    })
  }

  playCalenderAnimation() {
    if (this.updateCount != 0) {
      this.calenderAnimations.forEach(it => it.destroy())
      this.calenderAnimations = []
      this.setCalenderAnimation()
    }
    this.calenderAnimations.forEach(it => {
      it.play()
    })
  }

  getGroupLastSuccessTime(records: { user: Member; currentRecord?: History; calendarCardIconType?: string | undefined }[]) {
    const temp = records.map(it => {
      return it.currentRecord?.studySucceededTime
    })
    let ret = temp[0]
    temp.forEach(it => {
      if (!it) {
        return
      }
      if (dayjsWithTZ(ret).valueOf() < dayjsWithTZ(it).valueOf()) {
        ret = it
      }
    })
    return ret
  }

  viewTotalAward() {
    this.viewTotalAwardEvent.emit()
    this.sliderMoveEvent.emit(this.actualSignDays?.length)
  }

  playCalendarShine() {
    new Promise(r => {
      this.cardWrappers.forEach((it, index, arr) => {
        setTimeout(() => {
          this.renderer.setAttribute(it.nativeElement, 'class', 'wrapper wrapper-animation')
          if (index == (arr.length - 1)) {
            r('finish')
          }
        }, 200 * index)
      })
    }).then(r => {
      this.viewTotalAward()
    })
  }

  getActiveCardBorderColor(signDay: SignDay | undefined, i: number) {
    if (i == this.actualSignDays?.length && this.actualSignDays.length == this.currentSignDetailIndex) {
      if (this.groupStudyService.activityService.isActivityHasReport()) {
        return '#AAA'
      }
      if (!this.currentReport?.rewardedTime) {
        return 'var(--bg-color-2)'
      }
    }

    // 非选中
    if (this.currentSignDetailIndex !== i) {
      return 'var(--bg-color-2)'
    }

    // 选中时的颜色
    // 有特殊配置则优先使用
    const defaultColor = '#AAA'
    const targetColorType = this.getColorTypeFromSignDay(signDay)

    return (targetColorType && getColorFromColorType(targetColorType)) ?? defaultColor
  }

  isShowUnderMask() {
    if (this.groupStudyService.activityService.isActivityHasReport()) {
      return false
    } else if (this.groupInformation?.group?.status == 'LEARN_SUCCEED') {
      const currentUserTotalAwards = this.groupInformation?.group.members.find(it => it.isCurrentUser)?.totalAwards
      if (currentUserTotalAwards === 0) {
        return false
      }
      return !this.currentReport?.rewardedTime
    } else {
      return false
    }
  }

  getTotalBorderColor() {
    if (this.groupInformation?.group?.status == 'LEARN_SUCCEED') {
      if (
        (this.currentReport?.rewardedTime || this.groupStudyService.activityService.isActivityHasReport())
        && this.currentSignDetailIndex == this.actualSignDays?.length) {
        return '#AAA'
      }
      return 'var(--bg-color-2)'
    }
    if (this.currentSignDetailIndex == this.actualSignDays?.length) {
      return '#AAA'
    }
    return 'var(--bg-color-2)'
  }

  showCurrentStudyStatus(i: number) {
    if (i < this.actualSignDays!.length) {
      this.sliderMoveEvent.emit(i)
    }
  }

  getAnimationColor(signDay: SignDay | undefined) {
    const red = [220, 102, 62]
    const green = [54, 181, 157]
    const yellow = [235, 158, 39]
    const rewardReason = signDay?.records[0].currentRecord?.rewardReason
    if (!rewardReason || rewardReason == 123 || rewardReason == 12) {
      return green
    }
    if (rewardReason == 112 || rewardReason == 211) {
      return yellow
    }
    if (rewardReason == 111 || rewardReason == 11) {
      return red
    }
    return green
  }

  getColorTypeFromSignDay(signDay: SignDay | undefined): ColorType | undefined {
    if (signDay?.specialSkin && this.getIsCurrentUserFinished(signDay)) {
      return signDay.specialSkin.calendarColor
    }

    return signDay?.colorType
  }

  private getIsCurrentUserFinished(signDay: SignDay | undefined): boolean {
    if (!signDay) {
      return false
    }
    const currentUserRecord = signDay.records.find(v => v.user.isCurrentUser)
    const isCurrentUserFinished = !!currentUserRecord?.currentRecord?.studySucceededTime

    return isCurrentUserFinished
  }
}
