<div class="calender-container" [style.width]="type === 'learningSuccess' ? '100%' : '95%'">
  <div class="calender">
    @for (it of array(14).fill(0); let i = $index; track `day-${i}`) {
      <div #cardWrapper class="wrapper">
        <div
          [class]="'card-style-' + getColorTypeFromSignDay(signDays[i])"
          [class.today-card]="signDays[i].isToday"
          [style.border-color]="getActiveCardBorderColor(signDays[i], i)"
          (click)="showCurrentStudyStatus(i)"
        >
          <div
            class="activity-day"
            [class.opacity]="!signDays[i].isTodayBefore && getColorTypeFromSignDay(signDays[i]) === 'opacity'"
          >
            第 {{ i + 1 }} 天
          </div>
          <div class="user-portraits">
            <div class="avatars-container">
              @for (item of fixedGroupMember; let recordIndex = $index; track `member-${item}`) {
                <div style="position: relative" class="img-container">
                  <img
                    [src]="
                      signDays[i].records[recordIndex]?.user?.avatar
                        ? signDays[i].records[recordIndex]?.user?.avatar
                        : appConfig.defaultAvatarPath
                    "
                  />
                  @if (signDays[i].isTodayBefore) {
                    @if (!signDays[i].isToday) {
                      @if (signDays[i].records[recordIndex]?.calendarCardIconType !== 'number') {
                        <span class="status incorrect">
                          <span class="cross">
                            <img
                              alt="cross"
                              src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAC2SURBVEiJ7VTBDcQgDHOYrB0iYpwr4yCGKJPV92qFeqEF3Un3KH6hyNg4iQAGnosY45JSmq44KaUpxrhccaQmLiIvABCRWVWzJU5yBQCSwXtvGjmz6NwhSHI9JynFz/ymBJbInqRW7zawTEiGvXUt4rcGlkmPOFCZQQlVzSRDWSMZWsSBfyewZlCcP7ary8DaFu/9IiJzj4nZortV7FlVM8G2bcerrMuqmsskJb8Zv/qLBga+xxvFlJ76B8K2BQAAAABJRU5ErkJggg=="
                            />
                          </span>
                        </span>
                      } @else {
                        <span class="award-number">
                          {{ signDays[i].records[recordIndex].currentRecord?.rewardCount }}
                        </span>
                      }
                    }
                    <span
                      [id]="'user-number-' + recordIndex + '-' + i"
                      class="animation-number"
                      [style.opacity]="signDays[i].isToday ? 1 : 0"
                    ></span>
                  }
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    }
    <div class="wrapper">
      <div
        style="position: relative; border: 1px solid #000000"
        [class.success-total]="groupInformation?.group?.status === 'LEARN_SUCCEED'"
        [style.border-color]="getTotalBorderColor()"
        (click)="viewTotalAward()"
      >
        <div class="accumulated-awards">
          <div class="activity-day">累计获得</div>
          <div class="user-portraits">
            <div class="avatars-container">
              @for (it of groupInformation?.group?.members; track it.userId) {
                <div style="position: relative">
                  <img [src]="it.avatar ? it.avatar : appConfig.defaultAvatarPath" />
                  <span
                    class="award-number"
                    [class.award-number-digital-2]="it.totalAwards! >= 10"
                    [class.award-number-digital-3]="it.totalAwards! >= 100"
                  >
                    {{ it.totalAwards ? it.totalAwards : 0 }}
                  </span>
                </div>
              }
            </div>
          </div>
        </div>
        @if (isShowUnderMask()) {
          <div class="under"></div>
        }
      </div>
    </div>
  </div>
</div>
