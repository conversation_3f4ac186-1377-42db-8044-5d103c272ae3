@use 'sass:map';
$color-map: (
  yellow: var(--yellow-bgColor),
  green: var(--theme-green),
  red: var(--red-bgColor),
  normal: var(--normal-bgColor1),
);

:host {
  --item-normal-color: var(--bg-color-1);
}

:root.dark :host {
  --item-normal-color: var(--gray-level-100);
}

.calender-container {
  background: var(--bg-color-2);
  border-radius: 10px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.calender {
  position: relative;
  display: grid;
  grid: repeat(5, 20%) / 1fr 1fr 1fr;
  padding-top: 5px;
  width: 98%;
  margin: 0 auto;
  grid-row-gap: 4px;
  grid-column-gap: 3px;
  padding-bottom: 16px;
}

.wrapper {
  position: relative;
}

@keyframes skew {
  0% {
    transform: skewX(-45deg) translateX(-200%);
  }
  100% {
    transform: skewX(-45deg) translateX(10rem);
  }
}

.wrapper > div:first-child {
  position: relative;
  overflow: hidden;
  // ios 圆角处理
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
}

.wrapper > div:first-child::before {
  content: '';
  display: block;
  background: linear-gradient(
    to left,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 1) 40% 60%,
    rgba(255, 255, 255, 0.1)
  );
  height: 100%;
  width: 4em;
  position: absolute;
  top: 0;
  left: -20px;
  transform: skewX(-45deg) translateX(-200%);
  transform-origin: center center;
}

.wrapper-animation > div:first-child::before {
  animation: skew 1s forwards linear;
}

.wrapper > div {
  --bg-opacity: 0.1;

  background-color: var(--item-normal-color);
  height: 70px;
  border-radius: 4px;
  border: 1px solid #ffffff;
}

@mixin award-number($color) {
  color: map.get($color-map, $color);
  border: 1px solid map.get($color-map, $color);
  background-color: #fff;
}

.wrapper {
  .card-style-green {
    background: rgba(var(--green-blue), var(--bg-opacity)) !important;
    border-color: var(--theme-green);
    .img-container > img {
      border-color: var(--theme-green);
    }
    .award-number {
      @include award-number(green);
    }
  }
  .card-style-yellow {
    background: rgba(var(--orange-normal), var(--bg-opacity)) !important;
    border-color: var(--yellow-bgColor);
    img {
      border-color: var(--yellow-bgColor);
    }
    .award-number {
      @include award-number(yellow);
    }
  }
  .card-style-red {
    background: rgb(var(--red-normal), var(--bg-opacity)) !important;
    border-color: var(--red-bgColor);
    img {
      border-color: var(--red-bgColor);
    }
    .award-number {
      @include award-number(red);
    }
  }
  .card-style-normal {
    img {
      border-color: var(--normal-bgColor1);
    }
    .status {
      background-color: #fff;
    }
    .award-number {
      @include award-number(normal);
    }
  }
}

.card-style-opacity > .activity-day {
  color: var(--font-color-1) !important;
}

.wrapper > .card-style-opacity img {
  opacity: 0.3;
}

.wrapper > .card-style-normal {
  background-color: var(--item-normal-color);
  border-color: var(--normal-bgColor1);
}

.opacity {
  opacity: 0.3;
}

.activity-day {
  color: var(--font-color-1);
  font-size: 12px;
  padding: 8px 8px 3px 8px;
}

.user-portraits {
  margin-left: 6px;
}

.user-portraits img:nth-child(1) {
  display: inline-block;
  max-width: 25px;
  width: 25px;
  height: 25px;
  border-radius: 2px;
  opacity: 0.6;
}

.finish-status-icon {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  position: absolute;
  top: -15%;
  right: -25%;
  display: flex;
  justify-content: center;
  align-items: center;
  transform-origin: center bottom;
  z-index: 100;
  color: rgba(255, 255, 255, 1);
}

.award-number {
  min-width: 14px;
  height: 14px;
  border-radius: 14px;
  position: absolute;
  top: -17%;
  right: -27%;
  line-height: 14px;
  text-align: center;
  background: var(--theme-green);
  z-index: 100;
  color: #ffffff;
  font-size: 12px;
  font-family:
    -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Helvetica Neue', STHeiti, 'Microsoft Yahei', Tahoma, Simsun,
    sans-serif !important;
  font-weight: 450;
  @include award-number(green);
}

.avatar-mask {
  position: absolute;
  left: 0;
  top: 0;
  max-width: 27px;
  max-height: 27px;
  width: 100%;
  height: 100%;
}

.mask-green {
  background-color: var(--theme-green);
  opacity: 10%;
}

.mask-red {
  background-color: var(--red-bgColor);
  opacity: 10%;
}

.mask-yellow {
  background-color: var(--yellow-bgColor);
  opacity: 10%;
}

.mask-normal {
  background-color: var(--item-normal-color);
  opacity: 10%;
}

.award-number-digital-2 {
  padding: 0 2px;
}

.award-number-digital-3 {
  width: 50px;
  transform: scale(0.53);
}

.right-top-loc {
  width: 28px;
  height: 28px;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(0.5) translate(60%, -90%);
  z-index: 100;
}

.study-day-count {
  color: rgba(142, 142, 142, 1);
  font-size: 13px;
  text-align: center;
  padding: 10px 8px 20px 8px;
}

.finish-yellow {
  height: 55px;
  width: 55px;
  border-radius: 50%;
  border: 2.5px solid rgba(235, 158, 39, 1);
  background-color: rgba(235, 158, 39, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.finish-green {
  height: 55px;
  width: 55px;
  border-radius: 50%;
  background: rgba(130, 194, 171, 0.15);
  border: 2.5px solid rgba(130, 194, 171, 1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.finish-status-info {
  text-align: center;
  color: var(--font-color-1);
  font-size: 13px;
  margin-top: 6px;
}

@media (max-width: 600px) {
  .avatars-container > div {
    display: inline-block;
    margin-right: 4px;
  }
}

@media (min-width: 600px) {
  .avatars-container {
    width: 50%;
    display: flex;
    justify-content: space-between;
  }
}

.animation-number {
  position: absolute;
  z-index: 200;
  width: 17px;
  height: 17px;
  top: -15%;
  right: -25%;
}

.summary {
  text-align: left;
  margin: 0 auto;
  background: var(--item-normal-color);
  border-radius: 4px;
  padding-bottom: 10px;
}

.summary > div:first-child {
  padding-top: 6px;
  color: var(--theme-green);
  font-weight: 800;
  margin-bottom: 10px;
}

.summary > div {
  padding-left: 6px;
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.summary > div > img {
  display: inline-block;
  max-width: 25px;
  width: 25px;
  height: 25px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.summary > div > div {
  width: 80%;
  margin-left: 8px;
  color: var(--font-color-1);
}

.summary > div > div > span {
  color: var(--font-color-2);
}

.summary .keyword {
  color: var(--theme-green);
  font-weight: 600;
}

.success-total {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  height: calc(100% - 5px);
  border: solid 1px var(--border-color);
  background-color: var(--item-normal-color);
}

.success-total > div:first-child {
  position: relative;
  z-index: 1;
  background-color: var(--item-normal-color) !important;
  border-radius: 4px;
  height: calc(100% - 4px);
  overflow: hidden;
  margin: 2px;
}

.under {
  position: absolute;
  width: 0;
  height: 0;
  border-radius: 4px;
  border-top: 200px solid #ffd26c;
  border-right: 200px solid #ffa23a;
  border-bottom: 200px solid #ffd26c;
  border-left: 200px solid #ffa23a;
  left: calc(-200px + 50%);
  top: calc(-200px + 50%);
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  to {
    transform: rotate(1turn);
  }
}

.status {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  position: absolute;
  top: -15%;
  right: -25%;
  display: flex;
  justify-content: center;
  align-items: center;
  transform-origin: center bottom;
  z-index: 100;
  color: rgba(255, 255, 255, 1);
}

.status > img {
  border: unset !important;
}

.cross {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--normal-bgColor1);
  border-radius: 50%;
  background-color: #fff;
  width: 15px;
  height: 15px;
  z-index: 200;
  & > img {
    transform: scale(0.5);
    transform-origin: center center;
  }
}

.accumulated-awards {
  background-color: var(--item-normal-color);
  height: 70px;
  border-radius: 4px;
}

@media screen and (max-width: 390px) {
  .activity-day {
    color: var(--font-color-1);
    font-size: 12px;
    padding: 4px 4px 3px;
    margin: 2px 8px 2px 8px;
  }

  .wrapper > div {
    background-color: var(--item-normal-color);
    height: 57px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
  }

  .user-portraits img:nth-child(1) {
    width: 22px;
    height: 22px;
  }

  .user-portraits {
    padding-left: 6px;
  }

  .accumulated-awards {
    height: 57px;
  }

  .avatar-mask {
    position: absolute;
    left: 0;
    top: 0;
    max-width: 27px;
    max-height: 27px;
    width: 24px;
    height: 24px;
  }
}

:root.dark :host .wrapper > div {
  --bg-opacity: 0.25;
}
