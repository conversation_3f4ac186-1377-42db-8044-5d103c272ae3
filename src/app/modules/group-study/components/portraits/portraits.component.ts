import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core'
import { GroupInformation } from '../../../group-data/group-information.model'
import { Member } from '../../../group-data/group/member.model'
import { History } from '../../../group-data/history/history.model'
import { GroupStudyService } from '../../service/group-study.service'

const portraitsTypeTemp = ['invite', 'joinGroup', 'adjustFailed', 'groupSucceed', 'normal', 'groupFailed'] as const
type PortraitsType = typeof portraitsTypeTemp[number]

@Component({
  selector: 'app-portraits',
  templateUrl: './portraits.component.html',
  styleUrls: ['./portraits.component.scss'],
  standalone: false,
})
export class PortraitsComponent implements OnInit {
  @Input() groupInformation: GroupInformation | undefined
  @Input() portraitsType: PortraitsType = 'normal'
  @Output() inviteOther = new EventEmitter<Member | Record<string, unknown>>()
  @Output() joinGroup = new EventEmitter<Member | Record<string, unknown>>()
  failedHistory: { currentId: number; firstDayHistory: History | undefined }[] | undefined = []

  get isDisplayOtherInfo() {
    return this.portraitsType == 'adjustFailed'
      || this.portraitsType == 'groupSucceed'
      || this.portraitsType == 'groupFailed'
  }

  defaultAvatarSrc = 'assets/images/default-avatar.png'

  get members() {
    return this.groupInformation?.group?.members || []
  }

  get groupSize() {
    return this.groupInformation?.group?.type === 'COUPLE' ? 2 : 3
  }

  constructor(
    public groupStudyService: GroupStudyService,
  ) { }

  clickAvatar(it: Member | Record<string, unknown>) {
    switch (this.portraitsType) {
      case 'normal':
        break
      case 'invite':
        this.inviteOther.emit(it)
        break
      case 'joinGroup':
        this.joinGroup.emit(it)
        break
      default:
        break
    }
  }

  ngOnInit(): void {
    this.failedHistory = this.groupStudyService.groupInformation?.group?.members.map(it => {
      const firstDayHistory = this.groupStudyService.groupInformation?.history!.filter(item => it.userId === item.memberId)?.sort((a, b) => new Date(a.createdTime).getTime() - new Date(b.createdTime).getTime())[0]
      return {
        currentId: it.userId,
        firstDayHistory: firstDayHistory,
      }
    })
  }
}
