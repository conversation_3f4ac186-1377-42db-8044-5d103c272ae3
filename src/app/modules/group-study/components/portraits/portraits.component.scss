.portraits {
  display: flex;
  justify-content: space-evenly;
  margin-top: 20px;
}

.portrait {
  position: relative;
  z-index: 3;
  border: 2px solid #e5e5e5;
  border-radius: 6px;

  &:active {
    opacity: var(--activity-opacity)
   }
}

.portraits > div > div:nth-child(1) {
  width: 54px;
  height: 54px;
  border: 2px dotted #e5e5e5;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.username {
  margin-top: 10px;
  color: rgba(142, 142, 142, 1);
  font-size: 13px;
}

.username > span {
  display: inline-block;
  max-width: 90px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  line-height: 17px;
}

.finished {
  color: rgba(54, 181, 157, 1);
  font-size: 14px;
}

.no-finished {
  color: rgba(220, 102, 62, 1);
  font-size: 14px;
}
