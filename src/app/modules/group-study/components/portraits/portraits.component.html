<div class="portraits">
  <div *ngFor="let item of groupSize | list;let i = index;">
    <ng-container *ngIf="members[i] as it; else emptyUser">
      <div appClick [class.click-1]="!it.avatar" (debounceClick)="clickAvatar(it)" [debounceTime]="300">
        <img *ngIf="it.avatar || it.avatar?.length === 0" width="54px" class="portrait" [src]="it.avatar.length === 0 ? defaultAvatarSrc : it.avatar" />
      </div>
    </ng-container>
    <ng-template #emptyUser>
      <div appClick class="click-1" (debounceClick)="clickAvatar({})" [debounceTime]="300">
      </div>
    </ng-template>
    <ng-container *ngIf="isDisplayOtherInfo">
      <div *ngIf="portraitsType" class="username">
        <span>{{members[i]?.name || ''}}</span>
      </div>
      <ng-container *ngIf="portraitsType === 'adjustFailed'">
        <div *ngIf="failedHistory![i]?.firstDayHistory?.studySucceededTime" class="finished">已完成</div>
        <div *ngIf="!failedHistory![i]?.firstDayHistory?.studySucceededTime" class="no-finished">未完成</div>
      </ng-container>
    </ng-container>
  </div>
</div>
