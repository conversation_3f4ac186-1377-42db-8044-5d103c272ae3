import { AfterViewInit, Component } from '@angular/core'
import confetti from 'canvas-confetti'
import { GroupStudyService } from '../../service/group-study.service'

@Component({
  selector: 'app-team-success-confetti',
  templateUrl: './team-success-confetti.component.html',
  styleUrls: ['./team-success-confetti.component.scss'],
  standalone: false,
})
export class TeamSuccessConfettiComponent implements AfterViewInit {
  constructor(
    public groupStudyService?: GroupStudyService,
  ) {
  }

  ngAfterViewInit(): void {
    window.scrollTo(0, 0)
    const currentMember = this.groupStudyService?.groupInformation?.group?.members.filter(it => it.isCurrentUser)[0]
    if (!currentMember?.lastReadTime) {
      this.playConfetti()
      this.groupStudyService?.track('STUDY_RECORD')
    }
  }

  playConfetti() {
    const canvas = document.getElementById('confetti')

    // @ts-ignore
    const customConfetti = confetti.create(canvas, { resize: true })
    customConfetti({
      spread: 70,
      origin: { y: 1.2 },
      colors: ['#EB9E27', '#82C2AB', '#DC663E'],
    })
  }
}
