<div #danmu class="danmu-container">
  @for (track of barrageChunks(); track $index) {
    <memo-marquee class="track" [repeat]="2" [style.--duration]="track.duration + 's'">
      <ng-template #content>
        @for (item of track.items; track $index + '-' + item.text) {
          <span
            class="brarage-item"
            [style]="{
              '--color': item.color,
              '--bg-color': getBgColorByIndex($index)
            }" >{{item.text}}</span>
        }
      </ng-template>
    </memo-marquee>
  }

</div>
