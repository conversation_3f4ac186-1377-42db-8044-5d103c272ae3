import { After<PERSON>iewInit, Component, <PERSON>ement<PERSON>ef, <PERSON><PERSON><PERSON>roy, Renderer2, ViewChild, signal } from '@angular/core'
import { DomSanitizer } from '@angular/platform-browser'
import { clone, random, shuffle } from 'lodash-es'
import { Commander } from '../barrage.commander'
import { defaultGroupName, presetGroupNames } from '../constants'
import { DanmuService } from '../danmu.service'
import { BarrageObject, CommanderConfig, TrackConfig } from '../types'

@Component({
  selector: 'app-danmu',
  templateUrl: './danmu.component.html',
  styleUrls: ['./danmu.component.scss'],
  standalone: false,
})
export class DanmuComponent implements AfterViewInit, OnDestroy {
  private rollingCommander?: Commander

  barrageChunks = signal<{ items: BarrageObject[]; duration: number }[]>([])

  constructor(
    public danmuService: DanmuService,
    public render2: Renderer2,
    public domSanitizer: <PERSON>Sanitizer,
  ) {
  }

  @ViewChild('danmu') danmu!: ElementRef

  async ngAfterViewInit() {
    const presetBarrages = this.danmuService.toBarrages(presetGroupNames)
    const barragesFromServer = (await this.danmuService.getBarrageObjects()) || []
    if (barragesFromServer.length < 50) {
      barragesFromServer.push(...this.danmuService.toBarrages(defaultGroupName))
    }
    const barrages = [...presetBarrages, ...barragesFromServer]
    this.barrageChunks.set([
      this.rollBarrages(barrages),
      this.rollBarrages(barrages),
      this.rollBarrages(barrages),
    ])
  }

  private rollBarrages(barrages: BarrageObject[]) {
    const shuffled = shuffle(barrages)
    return {
      items: shuffled,
      duration: shuffled.length * (2 + random(-0.6, 0.6, true)),
    }
  }

  ngOnDestroy(): void {
    this.rollingCommander && this.rollingCommander.destroy()
  }

  getTrackConfigs(trackNum: number): TrackConfig[] {
    const trackConfig = {
      trackWidth: this.danmu.nativeElement.clientWidth,
      trackHeight: this.danmu.nativeElement.clientHeight / trackNum,
      poolSize: 100,
    } as TrackConfig
    const trackConfigs: TrackConfig[] = []
    for (let i = 1; i <= trackNum; ++i) {
      const temp = clone(trackConfig)
      temp.speed = i * 10
      trackConfigs.push(temp)
    }
    trackConfigs[0].speed = 1
    trackConfigs[1].speed = 0.8
    trackConfigs[2].speed = 1.2
    return trackConfigs
  }

  getCommanderConfig(trackConfigs: TrackConfig[]): CommanderConfig {
    return {
      trackConfigs: trackConfigs,
      container: this.danmu.nativeElement,
      render: this.render2,
      danmuService: this.danmuService,
      domSanitizer: this.domSanitizer,
    } as CommanderConfig
  }

  getRandomOffsetByIndex(index: number) {
    return Math.floor(Math.random() * 10 + index / 2)
  }

  getBgColorByIndex(index: number) {
    return this.danmuService.getBarrageColor(index)
  }
}
