import { Injectable } from '@angular/core'
import { Observable, firstValueFrom } from 'rxjs'
import { ApiService } from '../../../core/services/api.service'
import { GroupStudyService } from '../../service/group-study.service'
import { BarrageObject } from './types'

@Injectable({
  providedIn: 'root',
})
export class DanmuService {
  private bgColors = ['var(--color-card-green-blue)', 'var(--color-card-orange)', 'var(--color-card-red)']

  constructor(
    public api: ApiService,
    public groupStudyService: GroupStudyService,
  ) { }

  async getBarrageObjects() {
    const barrageObjects: BarrageObject[] = []
    const texts = (await firstValueFrom(this.getTextOfBarrages(100))) || []

    barrageObjects.push(...this.toBarrages(texts))

    return barrageObjects
  }

  toBarrages(contents: string[] = []): BarrageObject[] {
    return contents.map((text, i) => ({
      text: text,
      color: 'var(--font-color-1)',
      width: 30,
      offset: 0,
      bgColor: '#fff',
    } as BarrageObject))
  }

  getTextOfBarrages(number?: number): Observable<string[]> {
    return this.api.getdanmu(number ?? 200)
  }

  getBarrageColor(index: number) {
    return this.bgColors[index % 3]
  }
}
