import { Renderer2 } from '@angular/core'
import { DomSanitizer } from '@angular/platform-browser'
import { BarrageTrack } from './barrage.track'
import { DanmuService } from './danmu.service'

export interface BarrageObject {
  text: string
  color: string
  size: number
  width: number
  offset: number
  freeze?: boolean
  bgColor: string
}

export interface BarrageConfig {
  engine: 'canvas' | 'css3'
  zoom: number
  proxyObject: HTMLElement | undefined
  usePointerEvents: boolean
  maxTrack: number
  fontSize: number
  fontColor: string
  duration: number
  trackHeight: number
  wrapper: HTMLElement | undefined
  interactive: boolean
  poolSize: number
}

export type TrackForEachHandler<T extends BarrageObject> = (track: T, index: number, array: T[]) => void

export interface TrackConfig {
  trackWidth: number
  trackHeight: number
  speed: number
  poolSize: number
}

export interface barrageObject {
  text: string
  color: string
  width: number
  offset: number
  bgColor: string
}

export interface CommanderConfig {
  trackConfigs: TrackConfig[]
  container: HTMLDivElement
  render: Renderer2
  danmuService: DanmuService
  domSanitizer: DomSanitizer
}

export type CommanderForEachHandler<T extends BarrageObject> = (track: BarrageTrack<T>, index: number, array: BarrageTrack<T>[]) => void

export abstract class BaseCommander<T extends BarrageObject> {
  protected tracks: BarrageTrack<T>[] = []
  waitingQueue: T[] = []

  constructor(config: CommanderConfig) {
    for (let i = 0; i < config.trackConfigs.length; ++i) {
      this.tracks[i] = new BarrageTrack(config.trackConfigs[i])
    }
  }

  forEach(handler: CommanderForEachHandler<T>) {
    for (let i = 0; i < this.tracks.length; ++i) {
      handler(this.tracks[i], i, this.tracks)
    }
  }

  // 添加弹幕到等待队列
  abstract add(barrage: T): boolean

  // 寻找合适的轨道
  abstract findTrack(): number

  // 从等待队列中抽取弹幕并放入弹幕
  abstract extractBarrage(): void

  // 渲染函数
  abstract render(): void

  // 清空
  abstract reset(): void
}
