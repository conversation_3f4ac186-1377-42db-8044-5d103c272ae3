import { Renderer2 } from '@angular/core'
import { DomSanitizer } from '@angular/platform-browser'
import { shuffle } from 'lodash-es'
import { BarrageTrack } from './barrage.track'
import { DanmuService } from './danmu.service'
import { BarrageObject, BaseCommander, CommanderConfig } from './types'

export class Commander extends BaseCommander<BarrageObject> {
  private _barragePool: BarrageObject[] = []

  constructor(config: CommanderConfig) {
    super(config)
    this.container = config.container
    this.render2 = config.render
    this.danmuService = config.danmuService
    this.domSanitizer = config.domSanitizer
  }

  domSanitizer: DomSanitizer
  danmuService: DanmuService
  render2: Renderer2
  container: HTMLDivElement

  /**
   * add a barrage to a certain track
   * @param barrage
   */
  add(barrage: BarrageObject): boolean {
    const trackId = this.findTrack()
    if (trackId === -1) {
      this.waitingQueue.push(barrage)
      return false
    }
    const track = this.tracks[trackId]
    track.push(barrage)
    track.offset -= barrage.text.length * 8
    return true
  }

  /**
   * should find a track which can receive a barrage
   */
  findTrack(): number {
    let idx = -1
    this.forEach((track, index) => {
      if (track.offset > 0) {
        if (idx == -1) {
          idx = index
        }
        return
      } else {
        idx = -1
      }
    })
    return idx
  }

  extractBarrage() {
    return this.waitingQueue.shift()!
  }

  /**
   * init track
   */
  initTracks() {
    this.tracks.forEach(it => {
      const div = this.render2.createElement('div')
      this.render2.addClass(div, 'track')
      this.render2.setStyle(div, 'line-height', it.height + 'px')
      this.container.appendChild(div)
      it.container = div
      it.barrages.forEach(barrage => {
        const span = this.initBarrage(barrage)
        this.render2.appendChild(div, span)
      })
    })
  }

  initBarrage(barrageObject: BarrageObject) {
    const span: HTMLElement = this.render2.createElement('span')
    span.innerHTML = this.domSanitizer.sanitize(1, barrageObject.text) ?? ''
    this.render2.setStyle(span, 'background', barrageObject.bgColor)
    this.render2.setStyle(span, 'color', barrageObject.color)
    return span
  }

  /**
   * render函数负责弹幕移动的渲染
   * commander维护一个等待队列，render会消费等待队列，当等待队列数值小于limitWaitingQueue,则会请求后端获取队名
   */
  render(): void {
    const limitWaitingQueue = 10
    let progress = 0
    let lastProgress = 0
    const transform = () => {
      if (this.tracks.length === 0) {
        return
      }

      progress += 1
      this.tracks.forEach((track, index) => {
        track.offset += track.speed
        this.translateTrack(track, progress * track.speed)
        if (track.offset > 0) {
          if (this.waitingQueue.length > limitWaitingQueue) {
            const barrageObject = this.extractBarrage()
            track.barrages.push(barrageObject)
            barrageObject.bgColor = this.getBarrageColor(track.barrages.length)
            const barrage = this.initBarrage(barrageObject)
            this.render2.appendChild(track.container, barrage)
            let textLength = barrageObject?.text.length
            if (textLength > 10) {
              textLength = 10
            }

            // 14px 为font-size, 1.2 为整体宽度
            track.offset -= textLength * 14 * 1.2
          } else {
            if (progress - lastProgress > 100) {
              this.addBarragesToTrack(shuffle(this._barragePool.slice()))
              lastProgress = progress
            }
          }
        }
      })
      requestAnimationFrame(transform)
    }
    requestAnimationFrame(transform)
  }

  reset(): void {
    this.forEach(track => {
      track.barrages = []
      track.offset = track.width
    })
  }

  /**
   * 暂定只执行一次
   * @param barrages
   */
  registerBarrages(barrages: BarrageObject[]) {
    this._barragePool.length = 0
    this._barragePool.push(...barrages)
  }

  addBarragesToTrack(barrages: BarrageObject[]) {
    barrages.forEach(barrage => this.add(barrage))
    this.tracks.forEach(it => {
      it.barrages.forEach((item, index) => {
        item.bgColor = this.getBarrageColor(index)
      })
    })
  }

  getBarrageColor(index: number) {
    const bgColors = ['var(--color-card-green-blue)', 'var(--color-card-orange)', 'var(--color-card-red)']
    return bgColors[index % 3]
  }

  translateTrack(track: BarrageTrack<BarrageObject>, length: number) {
    this.render2.setStyle(track.container, 'transform', `translateX(${-length}px)`)
  }

  destroy() {
    this.tracks.length = 0
    this._barragePool.length = 0
  }
}
