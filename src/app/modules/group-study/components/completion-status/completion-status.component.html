<div class="finish-status">
  <div class="sign-day-prompt">
    <div>第 {{ currentSingDayIndex! + 1 < 10 ? '0' : '' }}{{ currentSingDayIndex! + 1 }} 天</div>
    <div>
      @if (currentSingDayIndex === 0) {
        首日磨合期
      }
      {{ clone(firstDayRewardHistory)?.add(currentSingDayIndex!, 'days')?.format() | date: 'yyyy.MM.dd' : 'UTC+8' }}
      @if (
        currentSingDayIndex! === this.actualSignDays!.length - 1 && groupInformation?.group?.status !== 'LEARN_SUCCEED'
      ) {
        「今天」
      }
    </div>
  </div>
  <div class="adjusting-status">
    <div class="adjusting-status-container">
      @for (it of fixedPortraitArray; let i = $index; track it) {
        <div>
          <div [id]="'avatar' + i + currentSingDayIndex" class="sign-status"></div>
          @if (memberStatus![i]?.todayHistory?.studySucceededTime) {
            <div class="finished">已完成</div>
          } @else {
            <div class="no-finished">未完成</div>
          }
        </div>
        @if (i !== groupStudyService.groupInformation?.group?.members?.length! - 1) {
          <img [src]="'assets/images/activity-guide/cross.png' | cdn" class="cross" />
        }
      }
    </div>
    <div class="user-status">
      @for (it of fixedPortraitArray; let i = $index; track it) {
        <div>
          <img
            appDbclick
            class="user-avatar"
            (doubleClick)="tickle(memberStatus[i].memberId + '')"
            [src]="memberStatus[i].avatar ? memberStatus[i].avatar : 'assets/images/default-avatar.png'"
          />

          <div class="user-signup">
            <div class="username-status">
              {{ memberStatus[i].name }}
              @if (nationalDayThemeEnable) {
                <img class="flag-icon" [src]="CHINA_IMG_SRC" alt="" />
              }
              @if (memberStatus[i]?.todayHistory?.studySucceededTime) {
                <span style="color: #aaa; margin-left: 2px">
                  · {{ memberStatus[i]?.todayHistory?.actualStudySucceededTime | date: 'HH:mm' : 'UTC+8' }}
                </span>
              } @else {
                <span style="color: #aaa"> · 未完成 </span>
              }
            </div>
            <div class="user-info">
              <div class="animation-container">
                <span [class]="'user-' + i + currentSingDayIndex"></span>
              </div>
              <span style="padding-right: 7px"> {{ memberStatus[i].isSign ? '已' : '未' }}签到</span>
              <div class="animation-container">
                <span [class]="'user-' + i + currentSingDayIndex" [id]="'user-' + i + 'study-time-symbol'"></span>
              </div>
              <span style="padding-right: 7px">
                {{ math.floor((memberStatus![i].studyTimeToday ? memberStatus![i].studyTimeToday : 0)! / 60) }}/{{
                  activity?.config?.dailyStudyDuration! / 60
                }}分钟
              </span>
              <div class="animation-container">
                <span [class]="'user-' + i + currentSingDayIndex" [id]="'user-' + i + 'study-voc-symbol'"></span>
              </div>
              {{ memberStatus[i].learnedVocCountToday ? memberStatus[i].learnedVocCountToday : 0 }}/{{
                activity?.config?.dailyStudyVoc!
              }}单词
            </div>
          </div>
        </div>
      }
    </div>
  </div>
</div>
