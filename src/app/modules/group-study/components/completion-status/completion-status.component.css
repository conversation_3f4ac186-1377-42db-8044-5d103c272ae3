.username-status {
  margin-top: 3px;
  text-align: left;
  color: var(--font-color-2);
  font-size: 12px;
  padding-left: 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 16px;
  line-height: 16px;
}

.adjusting-status > div:nth-child(1) {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

.finished {
  text-align: center;
  transform: translateY(-10px);
  color: var(--font-color-1);
  font-size: 12px;
}

@media screen and (max-width: 375px) {
  .finished {
    font-size: 12px;
  }
  .no-finished {
    font-size: 12px;
  }
}

.no-finished {
  color: var(--disabled-color);
  text-align: center;
  transform: translateY(-10px);
  font-size: 12px;
}

.user-avatar {
  width: 38px;
  height: 38px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
}

.cross {
  transform: translateY(-10px);
  height: 10px;
}

.adjusting-status-container > div:nth-child(1) {
  padding-left: 20px;
}

.adjusting-status-container > div:last-child {
  padding-right: 20px;
}

@media (max-width: 425px) {
  .user-info {
    white-space: nowrap;
    transform: scale(.8);
    transform-origin: left;
    font-size: 15px;
  }
}

.user-status > div {
  display: flex;
  margin: 10px 0 16px 20px;
}

.user-status > div > img {
  transform: translateY(5px);
  display: inline-block
}

.user-signup {
  display: flex;
  margin-left: 5px;
  flex-direction: column;
  justify-content: space-evenly;
  max-width: 80%;
  transform: translateY(2px);
}

.animation-container {
  max-width: 40px;
  max-height: 40px;
  width: 20px;
  height: 20px;
  display: inline-block;
  position: relative;
}

.animation-container > span {
  display: inline-block;
  position: absolute;
  width: 53px;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-40%);
}

.sign-day-prompt {
  padding-top: 15px;
  text-align: center;
}

.sign-day-prompt > div:first-child {
  color: var(--title-color);
  font-size: 18px;
  font-weight: 800;
  padding-bottom: 2px;
}

.sign-day-prompt > div:nth-child(2) {
  color: var(--default-font-color);
  font-size: 11px;
}

.finish-status {
  background: var(--bg-color-2);
  border-radius: 10px;
  margin: 0 auto 10px;
  width: var(--container-width);
}
