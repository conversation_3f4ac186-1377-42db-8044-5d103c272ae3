import { Component, Input, OnInit } from '@angular/core'
import { Activity } from '../../../group-data/activity/activity.model'
import { Group } from '../../../group-data/group/group.model'
import { GroupStudyService } from '../../service/group-study.service'

@Component({
  selector: 'app-award-rule',
  templateUrl: './award-rule.component.html',
  styleUrls: ['./award-rule.component.css'],
  standalone: false,
})
export class AwardRuleComponent implements OnInit {
  currentRule?: Group['type'] = 'COUPLE'
  touchStartX = 0
  screenWidth!: number
  touch = 0
  @Input() activity!: Activity

  // 当滑动距离超过屏幕的滑动的5%，滑动真实生效
  get slideDistance(): number {
    return this.screenWidth * 0.15
  }

  constructor(
    public groupStudyService: GroupStudyService,
  ) {
  }

  ngOnInit(): void {
    this.screenWidth = screen.width
    this.currentRule = this.groupStudyService.groupInformation?.group?.type
    this.currentRule = this.currentRule ? this.currentRule : 'COUPLE'
  }
}
