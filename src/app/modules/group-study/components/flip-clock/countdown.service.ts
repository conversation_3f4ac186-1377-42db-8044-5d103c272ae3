import { Injectable } from '@angular/core'
import { Observable } from 'rxjs'
import { GroupStudyService } from '../../service/group-study.service'
import { GroupInformation } from '../../../group-data/group-information.model'

@Injectable({
  providedIn: 'root',
})
export class CountdownService {
  constructor(
    public groupStudyService: GroupStudyService,
  ) { }

  getGroupStatus(invitedCode?: string): Observable<GroupInformation> {
    return this.groupStudyService.getGroupStatus(this.groupStudyService.activityService.activityId, invitedCode)
  }
}
