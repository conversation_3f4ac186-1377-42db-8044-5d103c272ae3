import { AfterViewInit, Component, ElementRef, Input, OnChanges, SimpleChanges, ViewChild } from '@angular/core'

@Component({
  selector: 'app-countdown',
  templateUrl: './countdown.component.html',
  styleUrls: ['./countdown.component.css'],
  standalone: false,
})
export class CountdownComponent implements AfterViewInit, OnChanges {
  @ViewChild('leftTick') leftTickElement!: ElementRef
  @ViewChild('rightTick') rightTickElement!: ElementRef

  // 无法找到对应的类型
  leftTick: any
  rightTick: any
  @Input() inputNumber!: number | string

  ngAfterViewInit(): void {
    this.leftTick = window.Tick.DOM.create(this.leftTickElement.nativeElement, {
      value: Math.floor(Number(this.inputNumber) / 10),
    })
    this.rightTick = window.Tick.DOM.create(this.rightTickElement.nativeElement, {
      value: Number(this.inputNumber) % 10,
    })
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!this.leftTick) {
      return
    }
    this.leftTick.value = Math.floor(Number(changes.inputNumber.currentValue) / 10)
    this.rightTick.value = Math.floor(Number(changes.inputNumber.currentValue) % 10)
  }
}
