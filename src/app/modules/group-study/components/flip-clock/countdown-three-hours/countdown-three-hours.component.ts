import { Component, Input, OnInit } from '@angular/core'
import { clone } from 'lodash-es'
import { getTimeDifference } from '../../../../group-data/handler'

@Component({
  selector: 'app-countdown-three-hours',
  templateUrl: './countdown-three-hours.component.html',
  styleUrls: ['./countdown-three-hours.component.css'],
  standalone: false,
})
export class CountdownThreeHoursComponent implements OnInit {
  remainingTimeInSeconds = '00'
  remainingTimeInMinus = '00'
  remainingTimeInHours = '00'

  // 队伍创建时间，减去了四小时
  @Input() initialTime?: string
  @Input() countdownHour = 3

  ngOnInit(): void {
    let restSecond = this.getRestSecond()
    if (restSecond <= 0) {
      return
    }

    this.setRemainingTime(restSecond)
    const interval = setInterval(() => {
      this.setRemainingTime(restSecond)
      if (--restSecond <= 0) {
        clearInterval(interval)
        return
      }
    }, 1000)
  }

  compensateDoubleDigitalInTime(time: number): string {
    if (time < 10) {
      return '0' + time
    }
    return time + ''
  }

  getRestSecond() {
    const SECOND_IN_HOURS = 3600
    return (SECOND_IN_HOURS * this.countdownHour) - (getTimeDifference(this.initialTime!)) / 1000
  }

  setRemainingTime(restSecond: number) {
    this.remainingTimeInSeconds = this.compensateDoubleDigitalInTime(Math.floor(restSecond % 60))
    this.remainingTimeInMinus = this.compensateDoubleDigitalInTime(Math.floor(restSecond / 60 % 60))
    this.remainingTimeInHours = clone(this.compensateDoubleDigitalInTime(Math.floor(restSecond / 3600)))
  }
}
