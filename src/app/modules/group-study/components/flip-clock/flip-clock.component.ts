import { Component, OnInit } from '@angular/core'
import { dayjsWithTZ } from '../../../shared/helper/time'
import { GroupStudyService } from '../../service/group-study.service'
import { HelperService } from '../../service/services/helper.service'

@Component({
  selector: 'app-flip-clock',
  templateUrl: './flip-clock.component.html',
  styleUrls: ['./flip-clock.component.css'],
  standalone: false,
})
export class FlipClockComponent implements OnInit {
  offsetInSecond = 0

  constructor(
    public helper: HelperService,
    public groupStudyService: GroupStudyService,
  ) {
  }

  get getCountDownColor() {
    return this.remainingTimeDays > 0 ? '#36b59d' : '#dc663e'
  }

  get remainingTimeInSeconds() {
    const tempSeconds = Math.floor(this.offsetInSecond % 60)
    return this.helper.compensateDoubleDigitalInTime(tempSeconds)
  }

  get remainingTimeInMinus() {
    const tempMinus = Math.floor(this.offsetInSecond / 60 % 60)
    return this.helper.compensateDoubleDigitalInTime(tempMinus)
  }

  get remainingTimeInHours() {
    return this.helper.compensateDoubleDigitalInTime(Math.floor(this.offsetInSecond / 3600 % 24))
  }

  get remainingTimeDays() {
    return Math.floor(this.offsetInSecond / 3600 / 24)
  }

  getOffsetTime(): number {
    const temp = dayjsWithTZ(this.groupStudyService.activityService.activity?.config?.registrationEndTime).valueOf() - dayjsWithTZ().valueOf()
    return (temp >= 0 ? temp : 0) / 1000
  }

  ngOnInit(): void {
    this.offsetInSecond = this.getOffsetTime()
    const interval = setInterval(() => {
      if (this.offsetInSecond === 0) {
        clearInterval(interval)
      } else {
        this.offsetInSecond--
        if (this.offsetInSecond < 0) {
          this.offsetInSecond = 0
        }
      }
    }, 1000)
  }
}
