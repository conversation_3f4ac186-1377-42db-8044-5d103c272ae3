<div class="finish-status">
  <div class="sign-day-prompt">
    <div>累计获得</div>
    <div>
      {{groupStudyService.groupInformation?.group?.studyStartTime | date: 'yyyy.MM.dd' : 'UTC+8'  }}
      -
      {{getActivityEndTime() | date: 'yyyy.MM.dd' : 'UTC+8'}}
    </div>
  </div>
  <div style="margin-bottom: 20px;position: relative">
    <canvas id="confetti-receive-reward"></canvas>
    <div *ngIf="groupStudyService.groupInformation?.group?.status !== 'LEARN_SUCCEED'" class="receive-reward"
         style="background-color: #AAAAAA">
      领取奖励
    </div>
    <div
      *ngIf="groupStudyService.groupInformation?.group?.status === 'LEARN_SUCCEED'"
      class="receive-reward"
      #receiveRewardElement>
      <ng-container *ngIf="currentReport">
        <ng-container *ngIf="isShowReportButton() || isShowReport">
        <div class="reward-report-button" style="position: relative;background: #36B59D" (click)="viewReport()">
          查看详细报告
        </div>
      </ng-container>
        <ng-container *ngIf="!isShowReportButton() && !isShowReport">
        <div class="reward-report-button" style="background: #eb9e27" (click)="receiveReward()">
          领取奖励
        </div>
      </ng-container>
      </ng-container>
      <div #rewardNumberElement class="reward-number">+{{rewardVocNumber}}</div>
    </div>
  </div>
  <div class="adjusting-status">
    <div class="user-status">
      <div *ngFor="let it of individualTotalAward; let i = index ">
        <img width="38px" height="38px" class="user-avatar" [src]="it.userAvatar">
        <div class="user-signup">
          <div class="username-status">
            {{it.userName}}
            <img *ngIf="nationalDayThemeEnable" class="flag-icon" [src]="CHINA_IMG_SRC" alt="">
          </div>
          <div class="user-info">
            <span>
              学习
              <span class="theme-color">{{ it.totalWords }}</span>
              次单词
            </span>
            |
            <span>
              <span class="theme-color">
              {{ it.totalTime }}
              </span>
              分钟
          </span>
            |
            <span>
             获得
              <span class="theme-color">
              {{ it.rewardCount }}
              </span>
              上限
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
