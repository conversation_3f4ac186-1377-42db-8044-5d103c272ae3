/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, Renderer2, ViewChild } from '@angular/core'
import confetti from 'canvas-confetti'
import { Report } from '../../../group-data/report/report.model'
import { dayjsWithTZ } from '../../../shared/helper/time'
import { GroupStudyService } from '../../service/group-study.service'

export interface TotalAward {
  familiarVocCount?: number
  userId: number
  totalWords: number
  newLearnWordNumber: number
  reviewWordNumber: number
  totalTime: number
  rewardCount: number
  userName: string
  userAvatar: string
  finishDays?: number
  longestLearningTimeDate?: string
}

@Component({
  selector: 'app-total-award',
  templateUrl: './total-award.component.html',
  styleUrls: ['./total-award.component.scss'],
  standalone: false,
})
export class TotalAwardComponent implements OnInit {
  individualTotalAward?: TotalAward[] = []
  isShowReport = false
  _currentReport?: Report
  rewardVocNumber?: number
  @ViewChild('rewardNumberElement') rewardNumberElement!: ElementRef
  @ViewChild('receiveRewardElement') receiveRewardElement!: ElementRef
  @Output() viewReportEmitter = new EventEmitter()
  currentUserTotalReward?: number = 0
  @Input() currentReport?: Report
  _report: Report[] = []
  @Input() set reports(value: Report[]) {
    this._report = value
    this.individualTotalAward = this.calculateTotalAward()

    this.currentUserTotalReward = this.individualTotalAward.find(it => it.userId == this.groupStudyService.groupInformation?.currentUserId)?.rewardCount
    this.individualTotalAward?.forEach(it => it.familiarVocCount = value.find(item => item.creator == it.userId)?.familiarVocCount)
    this.groupStudyService.individualTotalAward = this.individualTotalAward
  }

  get reports() {
    return this._report
  }

  @Input() nationalDayThemeEnable = false
  readonly CHINA_IMG_SRC = 'assets/images/china.png'

  constructor(
    public groupStudyService: GroupStudyService,
    public renderer: Renderer2,
    private cdr: ChangeDetectorRef,
  ) {
  }

  ngOnInit(): void {
    this.individualTotalAward = this.calculateTotalAward()
  }

  calculateTotalAward() {
    const ret = this.groupStudyService.groupInformation?.group?.members
      .map(it => {
        return {
          userId: it.userId,
          userName: it.name,
          userAvatar: it.avatar || 'assets/images/default-avatar.png',
          totalWords: 0,
          totalTime: 0,
          rewardCount: 0,
        }
      }) as TotalAward[]
    ret?.forEach(it => {
      const temp = this.groupStudyService.groupInformation?.history!.filter(item => it.userId == item.memberId)
      it.finishDays = temp?.filter(it => it.studySucceededTime).length
      it.longestLearningTimeDate = temp?.reduce((a, b) => {
        if (a.studyTime > b.studyTime) {
          return a
        } else {
          return b
        }
      }).createdTime
      it.newLearnWordNumber = temp?.map(it => it.learnedNewVocCount)?.reduce((a, b) => a + b, 0) ?? 0
      it.reviewWordNumber = temp?.map(it => it.learnedVocCount)?.reduce((a, b) => a + b, 0) ?? 0
      it.totalWords = it.newLearnWordNumber + it.reviewWordNumber
      it.totalTime = Math.floor((temp?.map(it => it.studyTime).reduce(
        (previousValue, currentValue) => previousValue + currentValue, 0)! / 60)) || 0
      it.rewardCount = temp?.map(it => it.rewardCount).reduce(
        (previousValue, currentValue) => previousValue + currentValue, 0) ?? 0
    })
    return ret
  }

  receiveReward() {
    this.groupStudyService.receiveReward().subscribe(
      it => {
        this.cdr.detectChanges()
        this.renderer.setAttribute(this.receiveRewardElement.nativeElement, 'class', 'view-report receive-reward')
        this.renderer.setAttribute(this.rewardNumberElement.nativeElement, 'class', 'receive-reward-animation reward-number')
        this.playConfetti()
        this.isShowReport = true
        this.rewardVocNumber = it.reward_count
      },
    )
  }

  playConfetti() {
    const canvas = document.getElementById('confetti-receive-reward')

    // @ts-ignore
    const customConfetti = confetti.create(canvas, { resize: true })
    customConfetti({
      spread: 100,
      origin: { y: 1.2 },
      colors: ['#EB9E27', '#82C2AB', '#DC663E'],
    })
  }

  viewReport() {
    this.groupStudyService.track('REPORT')
    this.viewReportEmitter.emit()
    this.groupStudyService.isShowAwardRule = false
  }

  getActivityEndTime() {
    const temp = this.groupStudyService.groupInformation?.group?.studyStartTime
    return dayjsWithTZ(temp).add(this.groupStudyService.totalSignDayNumber - 1, 'days').format()
  }

  isShowReportButton(): boolean {
    return !!((this.currentReport?.rewardedTime ?? this.currentUserTotalReward == 0) || this.groupStudyService.activityService.isActivityHasReport())
  }
}
