import { ComponentFixture, TestBed } from '@angular/core/testing'

import { TotalAwardComponent } from './total-award.component'

describe('TotalAwardComponent', () => {
  let component: TotalAwardComponent
  let fixture: ComponentFixture<TotalAwardComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TotalAwardComponent],
    })
      .compileComponents()
  })

  beforeEach(() => {
    fixture = TestBed.createComponent(TotalAwardComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
