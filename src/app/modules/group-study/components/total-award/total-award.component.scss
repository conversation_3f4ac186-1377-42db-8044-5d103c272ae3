.sign-day-prompt {
  padding-top: 15px;
  text-align: center;
}

.sign-day-prompt > div:first-child {
  color: var(--title-color);
  font-size: 18px;
  font-weight: 800;
  padding-bottom: 2px;
}

.sign-day-prompt > div:nth-child(2) {
  color: var(--default-font-color);
  font-size: 11px;
}

.finish-status {
  background: var(--bg-color-2);
  border-radius: 10px;
  margin: 0 auto 10px;
  width: var(--container-width);
}

#confetti-receive-reward {
  display: block;
  height: 60px;
  width: 90%;
  margin: 0 auto;
}

.receive-reward {
  width: 70%;
  margin: 0 auto;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  border-radius: 4px;
}

.view-report {
  background-color: #36b59d;
  position: relative;
}

.view-report-bg {
  background-color: #36b59d;
}

.view-report > div:first-child {
  position: relative;
  z-index: 1;
  background-color: #36B59D;
  border-radius: 4px;
}

.view-report::after {
  content: " ";
  display: block;
  height: 120%;
  width: 120%;
  position: absolute;
  top: 50%;
  left: 50%;
  background-color: #d7f0eb;
  border-radius: 4px;
  transform: translate(-50%, -50%);
  transform-origin: center center;
  animation: narrowAnimation 1s linear forwards;
}

@keyframes narrowAnimation {
  0% {
    height: calc(100% + 20px);
    width: calc(100% + 34px);
  }
  100% {
    height: 100%;
    width: 100%;
  }
}

.user-status > div {
  display: flex;
  margin: 10px 0 16px 20px;
}

.username-status {
  margin-top: 3px;
  text-align: left;
  color: rgba(128, 128, 128, 1);
  font-size: 12px;
  padding-left: 3px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 16px;
  line-height: 16px;
}

.user-info {
  padding-left: 3px;
  color: #ededed;
}

.user-info > span {
  color: var(--title-color)
}

@media (max-width: 425px) {
  .user-info {
    white-space: nowrap;
    transform: scale(.8);
    transform-origin: left;
    font-size: 15px;
  }
}

.reward-number {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #dc663e;
  font-weight: 600;
  font-size: 25px;
  opacity: 0;
}

.receive-reward-animation {
  opacity: 1;
  animation: rise forwards 1s linear;
}

@keyframes rise {
  0% {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -100%);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -200%);
    opacity:0;
  }
}

.user-avatar {
  border-radius: 6px;
  border: 1px solid #e5e5e5;
}

.user-signup {
  transform: translateY(-2px);
}

.reward-report-button {
  border-radius: 4px;

  &:active {
     opacity: var(--activity-opacity)
   }
}
