<header>
  <h3>队伍名称</h3>
</header>
<main>
  <div class="input-container">
    <input #inputEl type="text" [formControl]="groupName" />
    <memo-icon
      [style.--throttle.ms]="throttleTime"
      [class.spinning]="iconSpinning()"
      (animationend)="iconSpinning.set(false)"
      [style.color]="'var(--theme-green)'"
      [style.margin-right.em]="0.2"
      [style.margin-left.em]="0.2"
      size="0.9em"
      name="refresh"
      (click)="onClickRefresh()"
    ></memo-icon>
  </div>
  <p [class.red-text]="errorMessage()">{{ errorMessage() || '创建队伍成功后名称不可修改' }}</p>
  <button memo-button skin="light" (click)="onClickCreateGroup('COUPLE')">创建 2 人队伍</button>
  <button memo-button skin="light" (click)="onClickCreateGroup('TRIO')">创建 3 人队伍</button>
</main>
