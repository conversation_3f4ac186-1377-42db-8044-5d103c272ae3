:host {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 70vw;
  max-width: 400px;
  color: var(--title-color);
}

header {
  width: 100%;
  text-align: center;
  padding: 0.3rem 0;
  border-bottom: 1px solid var(--border-color);
}

header h3 {
  margin: 0 auto;
  font-size: 1rem;
  font-weight: normal;
}

main {
  width: 100%;
  padding: 15px;
}

main p {
  margin: 4px 4px 6px;
  font-size: 12px;
  color: var(--font-color-2);
  transform: scale(0.8);
  transform-origin: left top;
}

main button {
  font-size: 14px;
  width: 100%;
}

main button:last-of-type {
  margin-top: 0.8rem;
}

.input-container {
  border: 1px solid var(--color-familiar);
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0.3em;
  border-radius: 4px;
}

.input-container input {
  border: none;
  margin-right: 0.5rem;
  flex: 1;
  min-width: 0;
  background-color: transparent;
}

.spinning {
  --throttle: 500ms;
  animation: spin var(--throttle) ease-in-out forwards;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
