import { ChangeDetectionStrategy, Component, effect, ElementRef, inject, output, signal, viewChild } from '@angular/core'
import { delay, finalize, Subject, tap, throttleTime } from 'rxjs'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { FormControl, ReactiveFormsModule } from '@angular/forms'
import { throttle } from 'lodash-es'
import { MemoButtonComponent } from '../../../ui/button/button.component'
import { MemoIconComponent } from '../../../ui/icon/icon.component'
import { GroupNameGenerator } from '../../../shared/helper/group'
import { GroupStudyService } from '../../service/group-study.service'
import { GroupType } from '../../../group-study-v2/models/group.model'
import { MemoLoadingService } from '../../../ui/loading/loading.service'

@Component({
  selector: 'app-create-group-dialog',
  templateUrl: './create-group-dialog.component.html',
  styleUrls: ['./create-group-dialog.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    MemoButtonComponent,
    MemoIconComponent,
    ReactiveFormsModule,
  ],
})
export class CreateGroupDialogComponent {
  private groupStudyService = inject(GroupStudyService)
  private loading = inject(MemoLoadingService)

  readonly inputElRef = viewChild<ElementRef<HTMLInputElement>, ElementRef<HTMLInputElement>>('inputEl', {
    read: ElementRef,
  })

  dismiss = output()

  private _groupNameGenerator = new GroupNameGenerator('GROUP_STUDY')
  readonly throttleTime = 500
  readonly maxGroupNameLength = 20

  groupName = new FormControl(this._groupNameGenerator.getRandomName())

  errorMessage = signal('')
  iconSpinning = signal(false)
  private isCreating = signal(false)
  clickToChangeGroupName$ = new Subject<void>()

  constructor() {
    this.clickToChangeGroupName$
      .pipe(
        throttleTime(this.throttleTime),
        tap(() => {
          this.iconSpinning.set(true)
        }),
        delay(this.throttleTime / 2),
        tap(() => {
          this.groupName.setValue(this._groupNameGenerator.getRandomName())
        }),
        takeUntilDestroyed(),
      )
      .subscribe()

    effect(c => {
      const el = this.inputElRef()
      if (el) {
        setTimeout(() => {
          el?.nativeElement.focus()
        }, 200)
      }
    })
  }

  onClickRefresh() {
    this.clickToChangeGroupName$.next()
  }

  onClickCreateGroup = throttle((groupType: GroupType) => {
    const groupName = this.groupName.value?.trim()
    if (this.isCreating()) {
      return
    }
    if (!groupName) {
      this.errorMessage.set('队伍名称不能为空')
      return
    }
    if (groupName.length > this.maxGroupNameLength) {
      this.errorMessage.set(`队伍名称不能超过${this.maxGroupNameLength}个字符`)
      return
    }
    this.errorMessage.set('')

    this.createGroup(groupName, groupType)
  }, 500)

  private async createGroup(groupName: string, groupType: GroupType) {
    if (this.isCreating()) {
      return
    }
    const loadingRef = await this.loading.show()
    this.isCreating.set(true)
    this.groupStudyService.enroll({ name: groupName, type: groupType })
      .pipe(
        finalize(() => {
          loadingRef.dismiss()
          this.isCreating.set(false)
        }),
      )
      .subscribe({
        next: it => {
          this.groupStudyService.groupInformation = it
          this.dismiss.emit()
          this.groupStudyService.isShowSignUpButton = false
          this.groupStudyService.client.clientToast('创建队伍成功', 'SUCCESS')
          this.groupStudyService.client.clientUploadStudyRecord()
          this.groupStudyService.client.clientOpenNotificationPermissionIfNeed('打开通知权限', '授权开启系统通知权限，才能收到队友拍一拍提醒。')
          this.groupStudyService.navigate()
        },
        error: error => {
          const errorCode = error.error.errors[0].code
          if (errorCode === 'common_sensitive_word') {
            this.errorMessage.set('队名不可包含敏感词')
          } else if (errorCode === 'common_invalid_param') {
            this.errorMessage.set('队名限制20字以内')
          } else if (errorCode === 'group_user_in_other_group') {
            // 设置groupInformation为undefined， 在navigate()方法中，如果检测到groupInformation为undefined，重新进行请求
            this.groupStudyService.groupInformation = undefined
            this.groupStudyService.navigate()
            this.groupStudyService.client.clientAlertJoinFailed('提示', '你已在队伍中，不能创建队伍', 'group_user_in_other_group')
          } else if (errorCode === 'group_join_and_create_limit_exceeded') {
            this.groupStudyService.client.clientToast('可创建队伍数量已达上限')
          } else {
            this.groupStudyService.client.clientToast('创建队伍失败')
          }
        },
      })
  }
}
