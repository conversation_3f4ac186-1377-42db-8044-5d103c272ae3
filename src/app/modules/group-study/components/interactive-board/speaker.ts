import { environment } from '../../../../../environments/environment'

type SpeakerType = 'USER' | 'ROBOT'
const tickleTypes = ['GROUP_EVENT_ALL_DONE', 'GROUP_EVENT_MULTIPLE_UNDONE', 'GROUP_EVENT_RECEIVER_UNDONE', 'GROUP_EVENT_RECEIVER_DONE', 'GROUP_EVENT_SENDER_UNDONE'] as const
export type TickleType = typeof tickleTypes[number]
export type SpeakerConfig = {
  speakerType: SpeakerType
  speakerId: string
  speakerAvatar?: string
}

export class Speaker {
  speakerType: SpeakerType
  speakerAvatar?: string
  speakerId: string
  speakerTickleCallback?: () => void
  constructor(
    speakConfig: SpeakerConfig,
  ) {
    this.speakerType = speakConfig.speakerType
    this.speakerId = speakConfig.speakerId
    this.speakerAvatar = speakConfig.speakerAvatar
    if (this.speakerType == 'ROBOT') {
      this.speakerAvatar = `${environment.deployUrl}assets/images/interactive/robot.png`
    }
  }
}
