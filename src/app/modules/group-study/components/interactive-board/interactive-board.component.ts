import { ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core'
import { cloneDeep } from 'lodash-es'
import { ActivityService } from '../../../group-data/activity/activity.service'
import { Event } from '../../../group-data/event/event.model'
import { EventService } from '../../../group-data/event/event.service'
import { Group } from '../../../group-data/group/group.model'
import { Member } from '../../../group-data/group/member.model'
import { isToday } from '../../../group-data/handler'
import { dayjsWithTZ } from '../../../shared/helper/time'
import { GroupStudyService } from '../../service/group-study.service'
import { DATE_FORMAT, InteractiveBoardService } from './interactive-board.service'
import { Message } from './message/message'

@Component({
  selector: 'app-interactive-board',
  templateUrl: './interactive-board.component.html',
  styleUrls: ['./interactive-board.component.scss'],
  standalone: false,
})
export class InteractiveBoardComponent {
  @Output() closeEmitter = new EventEmitter()
  eventsOrderByDate: Message[][] = []
  days: string[] = []
  events: Array<Event> = []
  members: Member[] = []
  group!: Group

  constructor(
    public interactiveBoardService: InteractiveBoardService,
    public cdr: ChangeDetectorRef,
    public groupService: GroupStudyService,
    public eventService: EventService,
    public activityService: ActivityService,
  ) {
  }

  setContext() {
    if (!this.activityService.activityId) {
      return
    }
    this.events = this.eventService.events
    // eslint-disable-next-line @typescript-eslint/no-non-null-asserted-optional-chain
    this.group = this.interactiveBoardService.groupService.groupInformation?.group!
    this.eventsOrderByDate = cloneDeep(this.interactiveBoardService.getMessageOrderedByDateFromEvents('desc'))
    this.days = cloneDeep(this.interactiveBoardService.getDays('desc')).map(it => {
      if (isToday(it)) {
        return '今天'
      } else {
        return dayjsWithTZ(it).format(DATE_FORMAT)
      }
    },
    )
    this.cdr.detectChanges()
  }

  closeInteractiveBoard() {
    this.closeEmitter.emit()
  }

  handleMessageEvent(messageEvent: MessageEvent) {
    this.groupService.track(messageEvent.event_type, messageEvent.target)
  }

  refresh() {
    this.eventService.updateEvents(this.activityService.activityId).subscribe(() => {
      this.setContext()
    })
  }
}

export type MessageEvent = {
  event_type: string
  target: string
}
