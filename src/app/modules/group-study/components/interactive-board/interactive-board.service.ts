import { Injectable } from '@angular/core'
import { cloneDeep } from 'lodash-es'
import { Event } from '../../../group-data/event/event.model'
import { EventService } from '../../../group-data/event/event.service'
import { dayjsWithTZ } from '../../../shared/helper/time'
import { GroupStudyService } from '../../service/group-study.service'
import { Message } from './message/message'
import { Speaker, SpeakerConfig } from './speaker'

export const DATE_FORMAT = 'YYYY.MM.DD'

@Injectable({
  providedIn: 'root',
})
export class InteractiveBoardService {
  eventMap: Record<string, Event[]> = {}
  eventsOrderByDate: Message[][] = []
  events: Event[] = []

  constructor(
    public groupService: GroupStudyService,
    public eventService: EventService,
  ) {
  }

  getEventMap() {
    this.events = this.eventService.events
    const events = cloneDeep(this.events.filter(it => it.eventType !== 'LIKE' && it.eventType !== 'VIEW' && it.targetId !== 'INTERACTIVE_BOARD'))
    return this.eventService.getEventMap(events)
  }

  getMessageOrderedByDateFromEvents(order?: 'asc' | 'desc') {
    const speakers = this.generateSpeakers()
    this.eventMap = this.getEventMap()
    const messagesOrderedByDate = this.getDays().sort((a, b) => {
      return this.sort(dayjsWithTZ(a).valueOf(), dayjsWithTZ(b).valueOf(), order)
    }).map(it => {
      return this.eventMap[it].map((item, index, events) => {
        let finishOrder
        if (item.creator !== 0) {
          finishOrder = events.filter(event => event.eventType == 'LEARN_SUCCEED_TODAY').findIndex(event => event.creator == item.creator)
        }
        return {
          messageTime: dayjsWithTZ(item.createdTime).add(4, 'hours').toISOString(),
          messageId: item.id,
          speaker: speakers.find(speaker => speaker.speakerId == item.creator.toString()),
          messageTitle: this.getMessageTitle(item),
          messageContent: this.getMessageContent(item, finishOrder),
          messageType: item.eventType,
        } as Message
      },
      )
    })
    messagesOrderedByDate.forEach(it => {
      it.sort((a, b) => {
        return this.sort(dayjsWithTZ(a.messageTime).valueOf(), dayjsWithTZ(b.messageTime).valueOf(), order)
      })
    })
    this.eventsOrderByDate = messagesOrderedByDate
    return messagesOrderedByDate
  }

  generateSpeakers(): Speaker[] {
    const members = this.groupService.groupInformation?.group?.members!.filter(it => !!it.userId)
    const finishStatus = members?.map(it => {
      return {
        userId: it.userId,
        isCurrentUser: it.isCurrentUser,
        avatar: it.avatar,
      }
    })
    const ret = [{
      'speakerType': 'ROBOT',
      'speakerId': '0',
    } as SpeakerConfig]
    finishStatus?.forEach(it => {
      ret.push({
        speakerType: 'USER',
        speakerId: it.userId.toString(),
        speakerAvatar: it.avatar,
      } as SpeakerConfig)
    })
    return ret?.map(it => {
      return new Speaker(it)
    })
  }

  groupByDate(event: Event) {
    const temp = dayjsWithTZ(event.createdTime).subtract(4, 'hours')
    return temp.month() + 1 + '.' + temp.date()
  }

  getMessageContent(event: Event, finishOrder?: number) {
    const eventType = event.eventType
    const contentAboutStatus = {
      CREATE_GROUP: '🤘快来加入我的队伍吧！',
      JOIN_GROUP: '我来了🙌，未来大家一起努力呀！',
      GROUP_SUCCEEDED: '🥳恭喜组队成功，赶紧去学习吧！',
      TEST_SUCCEEDED: '🎉恭喜你们的队伍通过磨合期！未来的日子，要一起坚持学习收获奖励哦！',
      LEARN_SUCCEED: '👋🏻本次组队结束啦，墨墨将开展下一轮组队，期待你的参与啦~',
    } as Record<string, string>

    const getStrongTag = (text: string) => '<strong>' + text + '</strong>'
    switch (eventType) {
      case 'GROUP_FAILED':
        const joinedMembers = this.groupService.groupInformation?.group?.members.map(it => '@' + getStrongTag(it.name) + ' ')
        return `${joinedMembers!.join(' ')} 倒计时内队伍没有组队成功哦，💪别泄气，报名期内你可以重新组队~`
      case 'TEST_FAILED':
        const unFinishedMembers = this.groupService.groupInformation?.group?.members!.filter(it => {
          return !Object.values(this.eventMap)[0].some(item => item.eventType == 'LEARN_SUCCEED_TODAY' && item.creator == it.userId)
        }).map(it => '@' +  getStrongTag(it.name) + ' ')
        return `${unFinishedMembers!.join('')} 没有达成首日磨合期要求哦，💪别泄气，报名期内你可以重新组队~`
      case 'LEARN_SUCCEED_TODAY':
        let ret
        if (finishOrder == 0) {
          ret = '今天我最早完成耶😌！'
        } else if (finishOrder == 1) {
          ret = '我也完成学习啦😚~'
        } else {
          ret = '久等了，我 done 了😎！'
        }
        return ret
      case 'TICKLE':
        return event.comment
      default:
        return contentAboutStatus[eventType]
    }
  }

  getMessageTitle(event: Event) {
    const eventType = event.eventType
    const titleAboutStatus = {
      JOIN_GROUP: '加入队伍',
      GROUP_FAILED: '组队期失败',
      GROUP_SUCCEEDED: '组队成功',
      TEST_SUCCEEDED: '磨合期成功',
      TEST_FAILED: '磨合期失败',
      LEARN_SUCCEED_TODAY: '完成今日任务',
      LEARN_SUCCEED: '完成 14 天学习任务',
    } as Record<string, string>

    switch (eventType) {
      case 'CREATE_GROUP':
        const group = this.groupService.groupInformation?.group?.type
        return [`创建 ${group == 'COUPLE' ? 2 : 3} 人队伍`]
      case 'TICKLE':
        const targetMember = this.getMemberName((event.targetId || '').toString())
        return ['拍了拍', targetMember]
      default:
        return [titleAboutStatus[eventType]]
    }
  }

  getMemberName(userId: string) {
    return this.groupService.groupInformation?.group?.members!.find(it => it.userId.toString() == userId)?.name
  }

  getMessages() {
    return this.getDailyMessage()
  }

  getDailyMessage() {
    return this.getMessageOrderedByDateFromEvents()
  }

  getDays(order?: 'asc' | 'desc') {
    return [...(new Set(Object.keys(this.getEventMap())
      .sort((a, b) => {
        return this.sort(dayjsWithTZ(a).valueOf(), dayjsWithTZ(b).valueOf(), order)
      }),
    )),
    ]
  }

  sort(a: number, b: number, order?: 'asc' | 'desc') {
    if (order === 'asc') {
      return a - b
    } else {
      return b - a
    }
  }
}
