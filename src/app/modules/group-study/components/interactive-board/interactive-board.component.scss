.container {
  margin: 25px 20px;
  background: var(--bg-color-2);
}

.container:first-child {
  margin-top: 0;
}

.container:last-child {
  margin-bottom: 0;
  padding-bottom: 5vh;
}
.title {
  margin:0 20px;
  padding: 15px 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--title-color);
  .shrink {
    float: right;

    &:active {
      opacity: var(--activity-opacity)
    }
  }
}

.deadline {
  color: #aaa;
  font-size: 12px;
  text-align: center;
  padding-bottom: 30px
}


.btn-close {
  --size: 22px;

  width: var(--size);
  height: var(--size);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--second-font-color);
  background-color: var(--bg-color-1);
  border-radius: 5px;
  border: none;
  padding: 0;
}

:root.dark :host .btn-close {
  background-color: var(--gray-level-100);
}
