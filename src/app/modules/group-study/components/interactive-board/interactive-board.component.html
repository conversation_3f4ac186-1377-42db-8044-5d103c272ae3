<div style="background: var(--bg-color-2);min-height: 100vh">
  <div class="title">
    <span>最新动态</span>
    <button class="btn-close aspect-square shrink" (click)="closeInteractiveBoard()">
      <memo-icon name="shrink" size="14px"></memo-icon>
    </button>
  </div>

  <div>
    <div *ngFor="let day of days; let i = index" class="container">
  <span style="color: var(--font-color-2);font-size: 13px;font-weight: 500">
    {{ day }}
  </span>
      <div *ngFor="let message of eventsOrderByDate[i]">
        <app-message [message]="message" [events]="events"
                     [group]="group"
                     (messageEvent)="handleMessageEvent($event)"
                     (tickleEvent)="refresh()">
        </app-message>
      </div>
    </div>
  </div>
  <div class="deadline">- THE END -</div>
</div>
