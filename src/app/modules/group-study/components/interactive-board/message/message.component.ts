import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core'
import { cloneDeep, uniqBy } from 'lodash-es'
import { Event } from '../../../../group-data/event/event.model'
import { Group } from '../../../../group-data/group/group.model'
import { TickleService } from '../../../service/services/tickle.service'
import { MessageEvent } from '../interactive-board.component'
import { Message } from './message'

@Component({
  selector: 'app-message',
  templateUrl: './message.component.html',
  styleUrls: ['./message.component.scss'],
  standalone: false,
})
export class MessageComponent implements OnInit {
  @Input() message!: Message
  @Input() group!: Group
  @Input() events: Event[] = []
  @Output() messageEvent = new EventEmitter<MessageEvent>()
  @Output() tickleEvent = new EventEmitter<void>()

  _message!: Message
  emotionConfig!: EmotionConfig
  likeMembers: LikeMember[] = []
  currentUserId!: string

  constructor(
    public tickleService: TickleService,
  ) {
  }

  ngOnInit(): void {
    this.likeMembers = this.events.filter(it => it.eventType == 'LIKE' && it.targetId == this.message.messageId).map(it => {
      return {
        userId: it.creator.toString(),
        username: this.group.members.find(item => item.userId == it.creator)?.name,
        userAvatar: this.group.members.find(item => item.userId == it.creator)?.avatar,
      } as LikeMember
    }) || []
    this.currentUserId = this.group.members.find(member => member.isCurrentUser)!.userId.toString()
    this.setEmotionConfig()
  }

  tickle(speakerId: string): void {
    this.tickleService.tickle(speakerId)?.then(() => {
      this.tickleEvent.emit()
    })
  }

  like(): void {
    // 自己已经点过了就不可以再点了
    if (this.likeMembers.find(it => it.userId == this.currentUserId.toString())) {
      return
    }
    const groupId = this.group.groupId
    if (!groupId) {
      return
    }
    const targetId = this.message.messageId
    this.addLikePerson()
    this.setEmotionConfig()
    this.messageEvent.emit({
      event_type: 'LIKE',
      target: targetId,
    })
  }

  addLikePerson(): void {
    if (this.likeMembers.some(it => it.userId == this.currentUserId.toString())) {
      return
    }
    const currentUser = this.group.members.find(it => it.isCurrentUser)!
    this.likeMembers.push({
      userId: currentUser.userId.toString(),
      username: currentUser.name,
      userAvatar: currentUser.avatar,
    })
    this.likeMembers = cloneDeep(this.likeMembers)
  }

  setEmotionConfig(): void {
    const likeMember = uniqBy(this.likeMembers, 'userId')
    this.emotionConfig = new EmotionConfig(likeMember, EmotionType.LIKE, this.currentUserId)
  }
}

export interface LikeMember {
  userId: string
  username: string
  userAvatar: string
}
export enum EmotionType {
  LIKE = 'LIKE',
}
export enum EmotionStatus {
  isPlayed = 'isPlayed',
  unPlayed = 'unPlayed',
}

export class EmotionConfig {
  constructor(
    emotionSpeakers: LikeMember[],
    emotionType: EmotionType,
    currentUserId: string,
  ) {
    this.emotionSpeakers = emotionSpeakers
    this.emotionType = emotionType
    if (this.emotionType === 'LIKE') {
      if (this.emotionSpeakers.some(it => it.userId == currentUserId)) {
        this.emotionStatus = EmotionStatus.isPlayed
      } else {
        this.emotionStatus = EmotionStatus.unPlayed
      }
    } else {
      this.emotionStatus = EmotionStatus.isPlayed
    }
  }

  emotionType: EmotionType
  emotionStatus: EmotionStatus
  emotionSpeakers: LikeMember[]
}
