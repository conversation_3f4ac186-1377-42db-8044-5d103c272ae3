<div class="message">
  <img *ngIf="message?.speaker?.speakerType !== 'ROBOT'"
       class="avatar"
       appDbclick
       [src]="message?.speaker?.speakerAvatar || 'assets/images/default-avatar.png'"
       (doubleClick)="tickle(message.speaker.speakerId)"
       alt="avatar">
  <img *ngIf="message?.speaker?.speakerType === 'ROBOT'"
       class="avatar"
       src="assets/images/maimemo_bot.png"
       alt="robot">
  <div class="message-main-content">
    <div class="message-title">
      <ng-container *ngIf="message.messageType !== 'TICKLE'">
        <span class="tickle-title">
          {{message.messageTitle.join('')}}
        </span>
      </ng-container>
      <ng-container *ngIf="message.messageType === 'TICKLE'">
        <span *ngFor="let it of message.messageTitle" >
          {{it}}
        </span>
      </ng-container>
    </div>
    <div class="message-content-container">
      <div class="message-content">
        <div style="margin-bottom: 4px" [innerHTML]="message?.messageContent"></div>
        <ng-container *ngIf="message?.speaker?.speakerType !== 'ROBOT'">
          <app-emotion [emotionConfig]="emotionConfig" (emotionEmitter)="like()"></app-emotion>
        </ng-container>
      </div>
    </div>
  </div>
  <span class="message-time">{{message?.messageTime | date:'HH:mm'}}</span>
</div>
