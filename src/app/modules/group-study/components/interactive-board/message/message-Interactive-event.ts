type MessageInteractiveEventConfig = {
  messageId: string
  eventType: 'LIKE'
  callback: () => void
  isCanTrigger: boolean
  eventId?: string
}

export class MessageInteractiveEvent {
  isCanTrigger: boolean
  eventConfig: 'LIKE'
  callBack: () => void
  messageId: string
  eventId: string | undefined

  constructor(config: MessageInteractiveEventConfig) {
    this.eventConfig = config.eventType
    this.isCanTrigger = config.isCanTrigger
    this.messageId = config.messageId
    this.eventId = config.eventId
    this.callBack = () => {
      if (this.isCanTrigger) {
        config.callback()
      }
    }
  }


}

