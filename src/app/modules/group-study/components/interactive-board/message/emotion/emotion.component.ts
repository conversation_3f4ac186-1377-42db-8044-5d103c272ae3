import { AfterViewInit, Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core'
import lottie, { AnimationItem } from 'lottie-web'
import { likeAnimation } from '../../../../../../../assets/animations/likeAnimation'
import { EmotionConfig, EmotionStatus, EmotionType } from '../message.component'

@Component({
  selector: 'app-emotion',
  templateUrl: './emotion.component.html',
  styleUrls: ['./emotion.component.scss'],
  standalone: false,
})
export class EmotionComponent implements AfterViewInit {
  @Input()
  set emotionConfig(emotionConfig: EmotionConfig) {
    this._emotionConfig = emotionConfig
    this.likeMembersAvatars = emotionConfig.emotionSpeakers.map(it => it.userAvatar)
  }

  get emotionConfig(): EmotionConfig {
    return this._emotionConfig
  }

  _emotionConfig!: EmotionConfig
  likeMembersAvatars: string[] = []
  likeAnimation?: AnimationItem
  @ViewChild('likeEle') likeDom!: ElementRef
  @Output() emotionEmitter: EventEmitter<EmotionType.LIKE> = new EventEmitter()

  ngAfterViewInit(): void {
    this.setEmotion(this.emotionConfig)
  }

  setEmotion(emotionConfig: EmotionConfig) {
    if (emotionConfig.emotionType === 'LIKE') {
      this.likeAnimation = createAnimation(this.likeDom, likeAnimation, emotionConfig.emotionStatus)
    }

    function createAnimation(dom: ElementRef, animationJSON: any, animationStatus: EmotionStatus): AnimationItem {
      lottie.setQuality('high')
      const animation = lottie.loadAnimation({
        container: dom.nativeElement,
        renderer: 'svg',
        loop: false,
        autoplay: false,
        animationData: animationJSON,
      })
      const finalFrame = animation.totalFrames
      if (animationStatus === EmotionStatus.isPlayed) {
        animation.goToAndPlay(finalFrame, true)
      }
      return animation
    }
  }

  like(): void {
    if (this.emotionConfig.emotionStatus === EmotionStatus.isPlayed || !this.likeAnimation) {
      return
    }
    this.likeAnimation.play()
    this.emotionEmitter.emit(EmotionType.LIKE)
  }
}
