<div class="emotion-container">
  <div id="emotion-like" class="like-icon" #likeEle (click)="like()"></div>
  <ng-container *ngIf="likeMembersAvatars.length > 0">| </ng-container>
  <span class="avatar-gallery">
      <ng-container *ngFor="let avatar of likeMembersAvatars; let i = index">
    <img class="user-avatar" [src]="avatar || 'assets/images/default-avatar.png'" [alt]="'avatar' + i">
  </ng-container>
  </span>
</div>
