import { EventType } from '../../../../group-data/event/event.model'
import { Speaker } from '../speaker'
import { MessageInteractiveEvent } from './message-Interactive-event'

export type Message = {
  speaker: Speaker
  messageTitle: string[]
  messageContent: string
  messageId: string
  messageTime: string
  messageInteractiveEvents?: MessageInteractiveEvent
  messageType: EventType
}

// export class Message {
//   public messageInteractiveEvents?: MessageInteractiveEvent[]
//
//   constructor(
//     public speaker: Speaker,
//     public messageTitle: string,
//     public messageContent: string,
//     public messageId: string,
//     public messageTime: string,
//   ) {
//
//   }
// }
