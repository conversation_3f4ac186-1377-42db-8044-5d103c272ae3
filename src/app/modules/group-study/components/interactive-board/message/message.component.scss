.message {
  display: flex;
  flex-direction: row;
  margin-top: 10px;

  & > span:last-child {
    margin-left: auto;
  }

  .avatar {
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-right: 12px;
    object-fit:cover;
  }

  .message-title {
    display: inline-flex;
    color: var(--font-color-1);
    font-size: 15px;
    width: 95%;
    align-items: center;

    & > span:first-child {
      font-weight: 800;
      margin-right: 10px;
    }
    & > span:nth-child(2) {
      max-width: calc(100% - 4em);
      display: inline-block;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      vertical-align: bottom;
    }

    .tickle-title {
      display: inline-block;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      padding-right: 10px;
    }
  }

  .message-main-content {
    max-width: 70%;
    min-width: 60%;

    .message-content-container {
      margin-top: 4px;

      .message-content {
        display: inline-block;
        width: 90%;
        background: var(--normal-bgColor);
        border-radius: 6px;
        color: var(--font-color-1);
        font-size: 14px;
        padding: 6px 8px;
      }
    }
  }

  .message-time {
    font-size: 13px;
    font-weight: 400;
    color: var(--font-color-2);
  }
}
