import { dayjsWithTZ } from '../../../shared/helper/time'

export function getEasyRandomTodayTime(time?: string) {
  const randomHourNum = Math.ceil(Math.random() * 24)
  const randomMinuteNum = Math.ceil(Math.random() * 60)
  return dayjsWithTZ(time).hour(randomHourNum).minute(randomMinuteNum).add(4, 'hours').toString()
}

export function beautify(value: any) {
  //eslint-disable-next-line
  console.log(JSON.stringify(value, null, 2))
}

export function getRandomOffsetDayTime(offset: number, time?: string) {
  const randomHourNum = Math.ceil(Math.random() * 24)
  const randomMinuteNum = Math.ceil(Math.random() * 60)
  return dayjsWithTZ(time).subtract(offset, 'days').hour(randomHourNum).minute(randomMinuteNum).add(4, 'hours').toISOString()
}

/**
 * 获取某一个时间点之前的随机时间
 */
export function getCertainTimeBeforeTime(time?: string) {
  const currentHour = dayjsWithTZ().hour()
  const randomHourNum = Math.ceil(Math.random() * currentHour)
  return dayjsWithTZ(time).hour(randomHourNum).toString()
}

export function getTodayLastMoment(time?: string) {
  return dayjsWithTZ().endOf('day').add(4, 'hours').toString()
}

export function getTodayFirstMoment(time?: string) {
  return dayjsWithTZ().startOf('day').add(4, 'hours').toString()
}
