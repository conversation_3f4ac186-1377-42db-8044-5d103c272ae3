import { MockDataMaker, MockHistoryRule } from './mock.maker'
import { MockGroupRule } from './mock.group'
import { beautify } from './helper'

describe('mockDataMaker test', () => {
  it('test generate certain day history', () => {
    const mockGroupRule: MockGroupRule<'SIGN_UP'> = {
      status: 'LEARNING',
      numberOfGroup: 2,
    }
    const mockHistoryRule: MockHistoryRule = {
      numOfLearningDays: 7,
      currentDayPattern: 11,
    }
    const mockDataMaker = new MockDataMaker(mockGroupRule, mockHistoryRule, [], false)
    beautify(mockDataMaker.getMockData())
  })

  it('test generate grouping group', () => {
    const mockGroupRule: MockGroupRule<'SIGN_UP'> = {
      status: 'GROUPING',
      numberOfGroup: 2,
    }
    const mockHistoryRule: MockHistoryRule = {
      numOfLearningDays: 0,
      currentDayPattern: '00',
    }
    const mockDataMaker = new MockDataMaker(mockGroupRule, mockHistoryRule, [], false)
    beautify(mockDataMaker.getMockData())
  })

  it('test generate testing group', () => {
    const mockGroupRule: MockGroupRule<'SIGN_UP'> = {
      status: 'TESTING',
      numberOfGroup: 2,
    }
    const mockHistoryRule: MockHistoryRule = {
      numOfLearningDays: 0,
      currentDayPattern: '00',
    }
    const mockDataMaker = new MockDataMaker(mockGroupRule, mockHistoryRule, [], false)
    beautify(mockDataMaker.getMockData())
  })
})
