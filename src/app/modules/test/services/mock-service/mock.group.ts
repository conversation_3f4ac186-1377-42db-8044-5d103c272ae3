import { Group } from '../../../group-data/group/group.model'
import { MemberRes } from '../../../group-data/group/member.model'
import { GroupStatus } from '../../../group-study/models/model'
import { dayjsWithTZ } from '../../../shared/helper/time'
import { noMatterString } from './constant'
import { getEasyRandomTodayTime, getRandomOffsetDayTime, getTodayFirstMoment, getTodayLastMoment } from './helper'


export type MockGroupRule<T> = {
  status: GroupStatus | T
  numberOfGroup: 2 | 3
}

export default class MockGroup {
  group: Group
  constructor(
    public mockGroupRule: MockGroupRule<'SIGN_UP'>,
    public numOfLearningDays?: number,
    public createdTime: string = dayjsWithTZ().toString(),
    public isPlayAnimation = true,
  ) {
    this.group = this.getGroup() as any
  }

  type = ['COUPLE', 'TRIO']

  getMembers() {
    const defaultMembers =  [
      {
        user_id: 2152799,
        type: 'PARTICIPANT',
        joined_time: getRandomOffsetDayTime(this.numOfLearningDays || 0, this.createdTime),
        last_read_time: getEasyRandomTodayTime(this.createdTime),
        avatar: 'http://cdn2.maimemo.com/avatar/6a/9c/6a9c8554c15802e1f5e0d926ef0aea34.jpg?1645773327870',
        name: 'lukkun',
        is_current_user: true,
      },
      {
        user_id: 17026534,
        type: 'CREATOR',
        joined_time: getRandomOffsetDayTime(this.numOfLearningDays || 0, this.createdTime),
        last_read_time: getEasyRandomTodayTime(this.createdTime),
        avatar: 'http://cdn2.maimemo.com/wechat/60/bb/60bb71a7b358ae1719181b961609adee',
        name: '圆圆圆园圆',
        is_current_user: false,
      },
      {
        user_id: 7691942,
        type: 'PARTICIPANT',
        joined_time: getRandomOffsetDayTime(this.numOfLearningDays || 0, this.createdTime),
        last_read_time: getEasyRandomTodayTime(this.createdTime),
        avatar: 'http://cdn2.maimemo.com/avatar/fa/92/fa9290fa3c9acf24924d2a582ccfd76c.cn/mmopen/vi_32/Sd4fue86C0ECHgR3ypHYCTJUX4WmibkkEZ0OYqSEicp4fQiaosiajd6fe5Fp0B43X3bCSbRIPkhPYCdYJkw3BaibUFQ/0?1611375122023',
        name: '墨墨学员_7691942',
        is_current_user: false,
      },
    ]
    if (this.isPlayAnimation) {
      defaultMembers.find(it => it.is_current_user)!.last_read_time = getTodayFirstMoment()
    } else {
      defaultMembers.find(it => it.is_current_user)!.last_read_time = getTodayLastMoment()
    }
    if (['GROUP_FAILED', 'GROUPING'].find(it => it == this.mockGroupRule.status)) {
      return defaultMembers.slice(0, 1) as MemberRes[]
    }

    return defaultMembers.slice(0, this.mockGroupRule.numberOfGroup) as MemberRes[]
  }

  getGroup() {
    return {
      id: '123',
      group_id: '123',
      activity_id: '321',
      name: noMatterString,
      type: this.type[this.mockGroupRule.numberOfGroup % 2],
      group_succeeded_time: getRandomOffsetDayTime(this.numOfLearningDays || 0, this.createdTime),
      study_start_time: getRandomOffsetDayTime(this.numOfLearningDays || 0, this.createdTime),
      status: this.mockGroupRule.status,
      created_time: this.createdTime,
      updated_time: this.createdTime,
      members: this.getMembers(),
    }
  }
}
