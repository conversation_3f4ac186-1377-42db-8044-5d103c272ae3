import { MemberRes } from '../../../../group-data/group/member.model'
import { HistoryRes } from '../../../../group-data/history/history.model'
import { noMatterNumber, noMatterString } from '../constant'
import { getEasyRandomTodayTime } from '../helper'
import MockBaseHistory from './mock.base.history'

const patterns =  ['11', '12', '111', '112', '211', '123'] as const
type Pattern = typeof patterns[number]

export type FinishedHistoryRule = {
  pattern: Pattern
}

export class MockFinishedHistory extends MockBaseHistory {
  dailyHistoryRule: FinishedHistoryRule

  constructor(
    members: MemberRes[],
    createdTime: string,
    dailyHistoryRule: FinishedHistoryRule,
  ) {
    super(members, createdTime)
    this.dailyHistoryRule = dailyHistoryRule
    this.histories = this.getDailyHistories()

  }


  override getDailyHistories() {
    const histories =  this.members.map((member, memberIndex) => {
      return {
        member_id: member.user_id,
        device_id: noMatterString,
        is_signed: true,
        sign_avatar: noMatterNumber,
        sign_date: getEasyRandomTodayTime(this.createdTime),
        study_voc_succeeded_time: getEasyRandomTodayTime(this.createdTime),
        study_succeeded_time: getEasyRandomTodayTime(this.createdTime),
        study_time_succeeded_time: getEasyRandomTodayTime(this.createdTime),
        study_time: 100000,
        learned_voc_count: 100,
        learned_new_voc_count: 100,
        reward_type: this.dailyHistoryRule.pattern + '',
        reward_reason: noMatterNumber,
        reward_status: noMatterString,
        reward_cancelled_reason: noMatterString,
        created_time: this.createdTime,
        updated_time: this.createdTime,
        reward_count: this.getRewardCount(memberIndex),
      }
    })

    this.setSignAvatar(histories)

    return histories as HistoryRes[]
  }

  getRewardCount(memberIndex: number): number {
    const pattern = this.dailyHistoryRule.pattern as any
    const patternArr = String(pattern).split('')
    if (pattern == '111') {
      return 9
    }
    if (pattern == '112' || pattern == '211') {
      if (patternArr[memberIndex] == '1') {
        return 3
      }
      if (patternArr[memberIndex] == '2') {
        return 1
      }
    }
    if (pattern == '123') {
      return 1
    }
    if (pattern == 11) {
      return 4
    }
    if (pattern == '12') {
      return 1
    }
    return 0
  }

  setSignAvatar(histories: HistoryRes[]) {
    const randomSets = new Set()
    randomSets.add(Math.ceil(Math.random() * 5))
    const pattern = this.dailyHistoryRule.pattern as any
    const patternArr = String(pattern).split('')
    histories.forEach((history, historyIndex) => {
      if (pattern == 11 || pattern == '111') {
        history.sign_avatar = Array.from(randomSets)[0] as number
      }
      if (pattern == '112' || pattern == '211' || pattern == 12) {
        while (randomSets.size == 2) {
          randomSets.add(Math.floor(Math.random() * 6))
        }
        if (patternArr[historyIndex] == '1') {
          history.sign_avatar = Array.from(randomSets)[0] as number
        }
        if (patternArr[historyIndex] == '2') {
          history.sign_avatar = Array.from(randomSets)[1] as number
        }
      }
      if (pattern == '123') {
        while (randomSets.size == 3) {
          randomSets.add(Math.floor(Math.random() * 6))
        }
        history.sign_avatar = Array.from(randomSets)[historyIndex] as number
      }
    })
    this.histories = histories as HistoryRes[]
  }

}

