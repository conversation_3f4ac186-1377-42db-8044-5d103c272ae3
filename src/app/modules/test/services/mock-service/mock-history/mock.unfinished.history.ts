import { MemberRes } from '../../../../group-data/group/member.model'
import { HistoryRes } from '../../../../group-data/history/history.model'
import { noMatterNumber, noMatterString } from '../constant'
import { getEasyRandomTodayTime } from '../helper'
import MockBaseHistory from './mock.base.history'

export type UnFinishedHistoryRule = {
  pattern: '00' | '01' | '011' | '001' | '000'
}

export class MockUnfinishedHistory extends MockBaseHistory {
  unFinishedHistoryRule: UnFinishedHistoryRule
  constructor(
    members: MemberRes[],
    createdTime: string,
    unFinishedHistoryRule: UnFinishedHistoryRule,
  ) {
    super(members, createdTime)
    this.unFinishedHistoryRule = unFinishedHistoryRule
    this.histories = this.getDailyHistories()
  }

  override getDailyHistories(): HistoryRes[] {
    const pattern = this.unFinishedHistoryRule.pattern
    if (pattern == '00') {
      return []
    }
    if (pattern == '01' || pattern == '001') {
      const randomOne = Math.floor(Math.random() * this.members.length)
      return [
        {
          member_id: this.members[randomOne].user_id,
          device_id: noMatterString,
          is_signed: true,
          sign_date: getEasyRandomTodayTime(this.createdTime),
          study_voc_succeeded_time: getEasyRandomTodayTime(this.createdTime),
          study_succeeded_time: getEasyRandomTodayTime(this.createdTime),
          study_time: 100,
          learned_voc_count: 100,
          learned_new_voc_count: 100,
          reward_type: '',
          reward_reason: noMatterNumber,
          reward_status: noMatterString,
          reward_cancelled_reason: noMatterString,
          created_time: this.createdTime,
          updated_time: this.createdTime,
          reward_count: 0,
          sign_avatar: Math.ceil(Math.random() * 5),
        },
      ]
    }
    if (pattern == '011') {
      return [
        {
          member_id: this.members[0].user_id,
          device_id: noMatterString,
          is_signed: true,
          sign_date: getEasyRandomTodayTime(this.createdTime),
          study_voc_succeeded_time: getEasyRandomTodayTime(this.createdTime),
          study_succeeded_time: getEasyRandomTodayTime(this.createdTime),
          study_time: 100,
          learned_voc_count: 100,
          learned_new_voc_count: 100,
          reward_type: '',
          reward_reason: noMatterNumber,
          reward_status: noMatterString,
          reward_cancelled_reason: noMatterString,
          created_time: this.createdTime,
          updated_time: this.createdTime,
          reward_count: 0,
          sign_avatar: Math.ceil(Math.random() * 6),
        },
        {
          member_id: this.members[1].user_id,
          device_id: noMatterString,
          is_signed: true,
          sign_date: getEasyRandomTodayTime(this.createdTime),
          study_voc_succeeded_time: getEasyRandomTodayTime(this.createdTime),
          study_succeeded_time: getEasyRandomTodayTime(this.createdTime),
          study_time: 100,
          learned_voc_count: 100,
          learned_new_voc_count: 100,
          reward_type: '',
          reward_reason: noMatterNumber,
          reward_status: noMatterString,
          reward_cancelled_reason: noMatterString,
          created_time: this.createdTime,
          updated_time: this.createdTime,
          reward_count: 0,
          sign_avatar: Math.ceil(Math.random() * 6),
        },
      ]
    }
    return []
  }
}
