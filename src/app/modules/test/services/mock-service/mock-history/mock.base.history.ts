import { MemberRes } from '../../../../group-data/group/member.model'
import { HistoryRes } from '../../../../group-data/history/history.model'

export default abstract class MockBaseHistory {
  protected constructor(
    public members: MemberRes[],
    public createdTime: string,
  ) {
    this.members = members
  }
  histories: HistoryRes[] = []

  getDailyHistories() {
    return this.histories
  }
}
