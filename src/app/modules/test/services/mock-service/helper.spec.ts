import { dayjsWithTZ } from '../../../shared/helper/time'
import {
  beautify,
  getCertainTimeBeforeTime,
  getRandomOffsetDayTime,
  getTodayFirstMoment,
  getTodayLastMoment,
} from './helper'

describe('helper test', () => {
  it('test getRandomOffsetDayTime', () => {
    beautify(
      getRandomOffsetDayTime(-2, dayjsWithTZ().toString()),
    )
  })
  it('test getCertainTimeBeforeTime', () => {
    beautify(
      getCertainTimeBeforeTime(dayjsWithTZ().toString()),
    )
  })
  it('test getTodayLastMoment', () => {
    beautify(
      getTodayLastMoment(),
    )
  })
  it('test getTodayFirstMoment', () => {
    beautify(getTodayFirstMoment())
  })
})
