import { EventRes } from '../../../group-data/event/event.model'
import { GroupRes } from '../../../group-data/group/group.model'
import { MemberRes } from '../../../group-data/group/member.model'
import { HistoryRes } from '../../../group-data/history/history.model'
import { RewardReasons as Pattern } from '../../../group-study/models/model'
import { dayjsWithTZ } from '../../../shared/helper/time'
import { getRandomOffsetDayTime } from './helper'
import { MockFinishedHistory } from './mock-history/mock.finished.history'
import { MockUnfinishedHistory } from './mock-history/mock.unfinished.history'
import MockGroup, { MockGroupRule } from './mock.group'

export type MockHistoryRule = {
  numOfLearningDays?: number
  currentDayPattern: '00' | '01' | '011' | '001' | Pattern
}

export class MockDataMaker {
  group!: GroupRes
  constructor(
    public mockGroupRule: MockGroupRule<'SIGN_UP'>,
    public mockHistoryRule: MockHistoryRule,
    public eventTypes: string[],
    public isPlayAnimation: boolean = true,
  ) {
    this.getMockGroup()
  }

  getMockGroup() {
    this.group = new MockGroup(
      this.mockGroupRule,
      this.mockHistoryRule.numOfLearningDays,
      new Date().toString(),
      this.isPlayAnimation,
    ).getGroup() as GroupRes
    return this.group
  }

  getMockHistory() {
    const members = this.group.members
    const histories = [] as HistoryRes[]
    for (let i = 1; i <= this.mockHistoryRule.numOfLearningDays!; i++) {
      const pattern = this.getRandomPattern() as any
      const createdTime = getRandomOffsetDayTime(i, dayjsWithTZ().toString())
      this.getHistoryPerDay(
        members,
        createdTime,
        pattern,
      ).histories.forEach(it => {
        histories.push(it)
      })
    }
    const currentDayHistories = this.getHistoryPerDay(
      members,
      new Date().toString(),
      this.mockHistoryRule.currentDayPattern,
    ).histories

    currentDayHistories.forEach(it => {
      histories.push(it)
    })
    return histories
  }

  getMockData() {
    const group =  this.getMockGroup()
    return {
      group: this.getMockGroup(),
      history: this.getMockHistory(),
      current_user_id: this.getMockGroup().members.find(it => it.is_current_user)?.user_id,
      events: this.getEvents(),
    }
  }

  getHistoryPerDay(
    members: MemberRes[],
    createdTime: string,
    pattern: '00' | '01' | '011' | '001' | Pattern,
  ) {
    const currentDayPattern = pattern + ''
    if (currentDayPattern.includes('0')) {
      return new MockUnfinishedHistory(
        members,
        createdTime,
        {
          pattern: pattern as any,
        },
      )
    } else {
      return new MockFinishedHistory(
        this.group.members,
        createdTime,
        {
          pattern: pattern as any,
        },
      )
    }
  }

  getRandomPattern() {
    const patternTwoMemberGroup = ['00', '01', '11', '12']
    const patternThreeMemberGroup = ['011', '001', '111', '112', '211', '123']
    if (this.group.members.length == 2) {
      const random = Math.floor(Math.random() * patternTwoMemberGroup.length)
      return patternTwoMemberGroup[random]
    } else {
      const random = Math.floor(Math.random() * patternThreeMemberGroup.length)
      return patternThreeMemberGroup[random]
    }
  }

  getEvents() {
    const events = [
      {
        '_id': '62e799e575b86f6dd0c5e2b7',
        'activity_id': '62de120c7ff2135a3e3a590e',
        'group_id': '62e799e575b86f6dd0c5e29b',
        'creator': 5661172,
        'event_type': 'CREATE_GROUP',
        'created_time': '2022-08-01T09:16:21.346Z',
      },
      {
        '_id': '62e79a8b331d1682452e7d91',
        'activity_id': '62de120c7ff2135a3e3a590e',
        'group_id': '62e799e575b86f6dd0c5e29b',
        'creator': 3496381,
        'event_type': 'JOIN_GROUP',
        'created_time': '2022-08-01T09:19:07.434Z',
      },
    ] as any
    return events as EventRes
  }
}


