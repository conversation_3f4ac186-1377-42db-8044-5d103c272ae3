import { dayjsWithTZ } from '../../../shared/helper/time'

type Config = {
  isReceiveReward: boolean
  isOpenReport: boolean
  groupMemberNum: number
}
export function getReports(config: Config) {
  const reports = [
    {
      '_id': '62be9edd76548df130a92e2c',
      'activity_id': '62ac4eaa22fb194321c1b201',
      'group_id': '62add1aad16a840b15259309',
      'creator': 2152799,
      'familiar_voc_count': 2738,
      'created_time': '2022-07-01T07:14:37.382Z',
      'updated_time': '2022-07-01T07:20:18.890Z',
      // "rewarded_time": "2022-07-01T07:16:16.458Z",
      // "first_read_time": "2022-07-01T07:20:18.890Z"
    },
    {
      '_id': '62be9edd76548df130a92e2a',
      'activity_id': '62ac4eaa22fb194321c1b201',
      'group_id': '62add1aad16a840b15259309',
      'creator': 22791736,
      'familiar_voc_count': 205,
      'created_time': '2022-07-01T07:14:37.381Z',
      'updated_time': '2022-07-01T11:01:42.301Z',
      // "rewarded_time": "2022-07-01T08:39:26.973Z",
      // "first_read_time": "2022-07-01T11:01:42.301Z"
    },
    {
      '_id': '62be9edd76548df130a92e2b',
      'activity_id': '62ac4eaa22fb194321c1b201',
      'group_id': '62add1aad16a840b15259309',
      'creator': 6967647,
      'familiar_voc_count': 221,
      'created_time': '2022-07-01T07:14:37.381Z',
      'updated_time': '2022-07-01T08:37:55.759Z',
      // "rewarded_time": "2022-07-01T08:35:28.027Z",
      // "first_read_time": "2022-07-01T08:37:55.759Z"
    },
  ]
  const tempReports = reports.slice(0, config.groupMemberNum)
  tempReports.forEach(report => {
    report.created_time = dayjsWithTZ().toISOString()
    report.updated_time = dayjsWithTZ().toISOString()
    if (config.isReceiveReward) {
      dayjsWithTZ().subtract(1, 'hours').toISOString()
    }
    if (config.isOpenReport) {
      dayjsWithTZ().subtract(1, 'hours').toISOString()
    }
  })
}
