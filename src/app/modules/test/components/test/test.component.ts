import { Component, Inject, Injectable, Injector, OnInit } from '@angular/core'
import { Router } from '@angular/router'
import { AppService } from '../../../../app.service'
import { ROUTE_BASE_HREF } from '../../../entry/group-study.module'
import { GroupInformation } from '../../../group-data/group-information.model'
import { SignDay } from '../../../group-study/models/model'
import { GroupStudyService } from '../../../group-study/service/group-study.service'

@Injectable({
  providedIn: 'root',
})
export class TestService {
  allowUserIds = [
    2152799,
    17026534,
    5661172,
    1924528,
    7691942,
    16204550,
    3496381,
    11846614,
    5068488,
    3637464,
    246428,
    181,
    884,
    5491115,
    8139762,
  ]

  isShowTestComponent = false
  constructor(
    public groupStudyService: GroupStudyService,
    public injector: Injector,
  ) {
  }

  isShowTestButton(groupInformation?: GroupInformation) {
    if (this.allowUserIds.find(item => item == groupInformation?.currentUserId)) {
      return true
    }
    return false
  }

  fullScreen() {
    location.replace('https://www.maimemo.com?' + '__open_mode=3')
  }
}

@Component({
  selector: 'app-test',
  templateUrl: './test.component.html',
  styleUrls: ['./test.component.css'],
  standalone: false,
})
export class TestComponent implements OnInit {
  json = JSON
  memberStatus: SignDay | undefined

  initAnimationPlay: () => void = () => {}

  constructor(
    public router: Router,
    public testService: TestService,
    public app: AppService,
    @Inject(ROUTE_BASE_HREF) private routeBase: string,
  ) {
  }

  get viewGroupInformation() {
    return JSON.stringify(this.testService.groupStudyService?.groupInformation, null, 2)
  }

  set viewGroupInformation(value) {
    this.testService.groupStudyService.groupInformation = this.testService.groupStudyService.formatGroupInformation(JSON.parse(value))
  }

  currentGroupInformation?: string
  groupInformation?: string
  activityName = '/group-study/'
  signDays?: Array<SignDay>
  isShowTodayData = false
  specificDay = ''

  ngOnInit(): void {
    this.currentGroupInformation = JSON.stringify(this.testService.groupStudyService.groupInformation?.group, null, 2)
    this.signDays = this.testService.groupStudyService.signDays
    this.memberStatus = this.signDays?.find(it => it.isToday)
    const dayIndex = (this.signDays?.filter(it => it.isTodayBefore).length || 0) - 1
    // this.initAnimationPlay = () => {
    //   this.testService.animationService.clearAnimationByDay(dayIndex)
    //   this.testService.animationService.getAnimationSequence(dayIndex)
    //   this.testService.animationService.playAnimationByDay(dayIndex)
    // }
  }

  goTo(destination?: string) {
    this.testService.groupStudyService.isTestStatus = true
    if (!destination) {
      this.router.navigateByUrl(`${this.routeBase}activityStepper`, { skipLocationChange: true })
      return
    }
    switch (destination) {
      case 'GROUPING':
        this.router.navigateByUrl(`${this.routeBase}inviteOthers`, { skipLocationChange: true })
        break
      case 'GROUP_SUCCEEDED':
        this.router.navigateByUrl(`${this.routeBase}groupSucceed`, { skipLocationChange: true })
        break
      case 'GROUP_FAILED':
        this.router.navigateByUrl(`${this.routeBase}groupFailed`, { skipLocationChange: true })
        break
      case 'TESTING':
        this.router.navigateByUrl(`${this.routeBase}adjustPeriod`, { skipLocationChange: true })
        break
      case 'TEST_FAILED':
        this.router.navigateByUrl(`${this.routeBase}adjustPeriodFailed`, { skipLocationChange: true })
        break
      case 'LEARNING':
        this.router.navigateByUrl(`${this.routeBase}learning`, { skipLocationChange: true })
        break
      case 'LEARN_SUCCEED':
        this.router.navigateByUrl(`${this.routeBase}learnSucceed`, { skipLocationChange: true })
        break
      case 'joinGroup':
        this.router.navigateByUrl(`${this.routeBase}joinGroup`, { skipLocationChange: true })
        break
    }
  }

  viewSpecificDay() {
    this.memberStatus = this.signDays![Number(this.specificDay)]
    this.isShowTodayData = !this.isShowTodayData
  }

  replayAnimation() {
    // const animationService = this.testService.injector.get(AnimationService)
    this.initAnimationPlay()
  }

  setMockEnv() {
    this.app.isMockEnvironment = true
  }
}
