<div
    class="test-container"
    [class.test-active]="testService.isShowTestComponent">
  <button (click)="testService.fullScreen()">Full Screen</button>
  <button (click)="testService.groupStudyService.client.clientShare('http://www.maimemo.com')">Share</button>
  <button (click)="testService.groupStudyService.client.clientOpenNotificationPermissionIfNeed('打开通知权限', '授权开启系统通知权限，才能收到队友拍一拍提醒。')">授权通知</button>
  <button (click)="testService.groupStudyService.client.clientSetPageTitle('demo')">setTitle</button>
  <button (click)="testService.groupStudyService.client.clientShareInvitationCode('123')">shareInvitationCode</button>
  <button (click)="testService.groupStudyService.client.clientToast('123','ERROR')">toast</button>
  <button
      (click)="testService.groupStudyService.client.clientAlertJoinFailed('提示', '你已在队伍中，不能创建队伍', 'group_user_in_other_group')">
    alertJoinFailed
  </button>
  <button (click)="testService.groupStudyService.client.clientAlertJoinFailed('提示', '暂无参与权限', 'group_already_joined')">
    alertJoinFailed
  </button>
  <button (click)="testService.groupStudyService.client.clientVibrate()">
    vibrate
  </button>
  <button
      (click)="testService.groupStudyService.client.clientAlertJoinFailed('提示', '来晚了，你所加入的队伍已满员，报名期内你可以自己创建队伍', 'group_max_members_exceeded')">
    alertJoinFailed
  </button>
  <button (click)="testService.groupStudyService.client.clientStartLoading()">startLoading</button>
  <button (click)="testService.groupStudyService.client.clientEndLoading()">endLoading</button>
  <button (click)="testService.groupStudyService.client.clientSignUpGroupStudy({
      'name': 'dddd'
    })">signUpGroupStudy
  </button>
  <button (click)="testService.groupStudyService.client.clientSignUpGroupStudy({
                'error': '队名不可包含敏感词'
              })">signUpGroupStudy
  </button>
  <button (click)="testService.groupStudyService.client.clientSignUpGroupStudy({
              'dismiss': true})">signUpGroupStudy
  </button>
  <button (click)="testService.groupStudyService.client.clientUploadStudyRecord()">uploadStudyRecord</button>

  <button (click)="setMockEnv()">开启mock环境</button>
  <button (click)="isShowTodayData = !isShowTodayData">查看今日数据</button>
  <div style="display: flex"><input [(ngModel)]="specificDay" placeholder="1">
    <button (click)="viewSpecificDay()">点击查看某天数据</button>
  </div>
  <pre *ngIf="isShowTodayData">
      {{json.stringify(memberStatus, null, 2)}}
    </pre>
  <button (click)="replayAnimation()">重新播放动画</button>
  <button (click)="goTo()">活动首页</button>
  <button (click)="goTo('GROUPING')">邀请他人组队</button>
  <button (click)="goTo('GROUP_SUCCEEDED')">组队成功</button>
  <button (click)="goTo('GROUP_FAILED')">组队失败</button>
  <button (click)="goTo('TESTING')">磨合期</button>
  <button (click)="goTo('TEST_FAILED')">磨合期失败</button>
  <button (click)="goTo('LEARNING')">学习中</button>
  <button (click)="goTo('LEARN_SUCCEED')">学习成功</button>
  <button (click)="goTo('joinGroup')">加入他的队伍</button>
  <h1>当前队伍状态信息</h1>
  <pre>
  {{this.currentGroupInformation}}
</pre>
  <h1>今日打卡详细数据</h1>
  <pre>
  {{json.stringify(memberStatus, null, 2)}}
</pre>
  <div>
    <ng-container *ngFor="let item of this.signDays; let i = index">
      <ng-container *ngIf="item.isTodayBefore">
        <h1>第 {{i}} 天</h1>
        <pre>
        {{json.stringify(item.records, null, 2)}}
      </pre>
      </ng-container>
    </ng-container>
  </div>
  <h1>待修改的队伍状态</h1>
  <textarea style="width: 80vw;height:700px" [(ngModel)]="viewGroupInformation"></textarea>
</div>

