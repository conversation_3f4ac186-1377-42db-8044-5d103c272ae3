import { CommonModule } from '@angular/common'
import { InjectionToken, NgModule } from '@angular/core'
import { RouterModule, Routes } from '@angular/router'
import { ActivityResolver } from '../group-study-v2/utils/activity.resolver'
import { MemoButtonComponent } from '../ui/button/button.component'
import { IndexComponent } from './index/index.component'
import { NotFoundComponent } from './not-found/not-found.component'

export const ROUTE_BASE_HREF = new InjectionToken<string>('Route base href')

const routes: Routes = [
  {
    path: 'v1',
    loadChildren: () => import('../group-study/legacy-group-study.module').then(v => v.LegacyGroupStudyModule),
  },
  {
    path: 'v2',
    loadChildren: () => import('../group-study-v2/group-study-v2.module').then(v => v.GroupStudyModule),
  },
  {
    path: '404',
    component: NotFoundComponent,
  },
  {
    path: '',
    pathMatch: 'full',
    component: IndexComponent,
    resolve: {
      groupMeta: ActivityResolver,
    },
  },
]

@NgModule({
  declarations: [
    IndexComponent,
    NotFoundComponent,
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    MemoButtonComponent,
  ],
})
export class GroupStudyModule {}
