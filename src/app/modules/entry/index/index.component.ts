import { Component, inject } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { EMPTY, switchMap } from 'rxjs'
import { DestroyService } from '../../core/services/destroy.service'
import { Group, GroupMeta } from '../../group-study-v2/models/group.model'
import { GroupStudyStorageService } from '../../group-study-v2/services/group-study-storage.service'

@Component({
  selector: 'app-index',
  template: '',
  providers: [DestroyService],
  standalone: false,
})
export class IndexComponent {
  private route = inject(ActivatedRoute)
  private router = inject(Router)
  private gss = inject(GroupStudyStorageService)

  private groupMeta: GroupMeta = this.route.snapshot.data.groupMeta

  constructor() {
    const {
      activity_id = '',
      invitation_code = '',
      group_id = '',
    } = this.groupMeta || {}

    if (activity_id !== '' && activity_id !== null) {
      this.gss.queryGroupData({ activity_id }, true)
        .subscribe(groupData => {
          this.navigateByGroup(groupData.group)
        })
      return
    }

    if (group_id !== '' && group_id !== null) {
      this.gss.queryGroupById(group_id)
        .subscribe(groupData => {
          this.navigateByGroup(groupData.group)
        })
      return
    }

    if (invitation_code !== '' && invitation_code !== null) {
      this.gss.queryGroupData({ invitation_code }, true)
        .subscribe(groupData => {
          const { inviteUserGroup } = groupData

          // 邀请码队伍已过期（失败）
          if (!inviteUserGroup) {
            this.router.navigate(['404'], {
              relativeTo: this.route,
              replaceUrl: true,
              queryParamsHandling: 'merge',
            })
            return
          }

          this.navigateByGroup(inviteUserGroup)
        })
      return
    }

    this.gss.queryActivities({
      activeOnly: true,
      lastViewFirst: true,
      withOpeningOfficials: true,
    })
      .pipe(
        switchMap(activities => {
          if (activities.length === 0) {
            this.gss.redirectToGroupListPage()
            return EMPTY
          }
          const targetActivity = activities[0]
          return this.gss.queryGroupData({ activity_id: targetActivity.id }, true)
        }),
      )
      .subscribe(groupData => {
        this.navigateByGroup(groupData.group)
      })
  }

  private async navigateByGroup(group: Group | null) {
    // 有 activity_id ，但是没有 group
    // 说明是官方活动，但还没创建队伍
    if (!group) {
      this.router.navigate(['v1'], {
        relativeTo: this.route,
        queryParamsHandling: 'merge',
        replaceUrl: true,
      })
      return
    }

    const queryParams: GroupMeta = {}
    if (this.groupMeta.invitation_code) {
      queryParams.invitation_code = this.groupMeta.invitation_code
    } else {
      queryParams.activity_id = group.activityId
    }

    if (group.type === 'CUSTOM') {
      if (await this.gss.checkShouldHidePaymentInfo()) {
        this.gss.redirectToGroupListPage()
        return
      }
      this.gss.navigateByGroupStatus(group, { meta: queryParams })
    } else {
      this.router.navigate(['v1'], {
        relativeTo: this.route,
        queryParams,
        queryParamsHandling: 'merge',
        replaceUrl: true,
      })
    }
  }
}
