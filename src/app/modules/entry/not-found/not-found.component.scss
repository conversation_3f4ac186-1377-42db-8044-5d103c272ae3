:host {
  --gap: 10px;

  height: calc(100vh - calc(var(--gap) * 2));
  display: flex;
  align-items: center;
  flex-direction: column;
  background-color: var(--bg-color-2);
  border-radius: 10px;
  margin: var(--gap);
  font-family: var(--memo-font-family);
  color: var(--title-color);
}

img {
  margin-top: 68px;
}

h2 {
  font-size: 30px;
  font-weight: bold;
  margin-top: 32px;
  margin-bottom: 60px;
}

footer {
  width: 85%;
  display: flex;
  align-items: center;
  justify-content: space-around;

}

[memo-button] {
  flex: 1;
  min-width: 0;
  border: none;

  &:not(:first-child) {
    margin-left: 15px;
  }

}
