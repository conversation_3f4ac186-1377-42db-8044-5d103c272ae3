import { HttpErrorResponse, HttpEvent, Http<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { Observable, catchError, throwError } from 'rxjs'
import { captureException } from '@sentry/angular'
import { ClientFrontendCommunicationService } from '../services/client-frontend-communication.service'

@Injectable({
  providedIn: 'root',
})
export class ErrorInterceptor implements HttpInterceptor {
  constructor(
    private cfc: ClientFrontendCommunicationService,
  ) {}

  intercept(req: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
    return next.handle(req).pipe(
      catchError(error => this.handleError(error)),
    )
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    console.error(error)
    captureException(error, {
      tags: {
        scene: 'interceptor',
      },
    })
    if (error.status.toString().startsWith('5')) {
      this.cfc.clientToast('网络异常，请重试')
    }

    return throwError(() => error)
  }
}
