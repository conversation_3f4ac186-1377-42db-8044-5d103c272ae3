import { Injectable, On<PERSON><PERSON>roy } from '@angular/core'
import { Observable, Subject } from 'rxjs'

/**
 * @example 示例
 * ```typescript
 * @Component({
 *  ...
 *  providers: [DestroyService],
 * })
 * export class AppComponent implements OnDestroy {
 *  constructor(private destroy$: DestroyService) {
 *    this.clicks$.pipe(
 *      takeUntil(this.destroy$)
 *    )
 *    .subscribe()
 *  }
 * }
 * ```
 *
 */
@Injectable()
export class DestroyService extends Observable<void> implements OnDestroy {

  private _destroyed = new Subject<void>()

  constructor() {
    super(
      subscriber => this._destroyed.subscribe(subscriber),
    )
  }

  ngOnDestroy(): void {
    this._destroyed.next()
    this._destroyed.complete()
  }

}
