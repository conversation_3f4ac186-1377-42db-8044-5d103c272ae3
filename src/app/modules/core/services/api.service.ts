import { HttpClient } from '@angular/common/http'
import { Injectable } from '@angular/core'
import { catchError, map, of } from 'rxjs'
import { ActivityRes } from '../../group-data/activity/activity.model'
import { Group, GroupRes } from '../../group-data/group/group.model'
import { Report, ReportRes } from '../../group-data/report/report.model'
import { HelperService } from '../../group-study/service/services/helper.service'
import { Response } from '../../shared/types'

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  prefix = '/pages/activity-center'

  constructor(
    public httpClient: HttpClient,
    public helper: HelperService,
  ) { }

  getGroupStatus(activityId: string, invitedCode?: string) {
    const paramsTemp: {
      scope: 'FULL' | 'GROUP'
      invitation_code?: string
      activity_id?: string
    } = {
      scope: 'FULL',
    }
    if (activityId && activityId != '') {
      paramsTemp.activity_id = activityId
    }
    if (invitedCode) {
      paramsTemp.invitation_code = invitedCode
    }
    return this.httpClient
      .get<Response<any>>(`${this.prefix}/api/v1/group_study/groups`,
      { params: paramsTemp })
      .pipe(
        map(it => {
          return it.data
        }),
      )
  }

  getGroupInvitation(groupId: string) {
    return this.httpClient
      .get<Response<{ invitation_code: string }>>(`${this.prefix}/api/v1/group_study/groups/share`, {
      params: {
        group_id: groupId,
      },
    })
      .pipe(
        map(
          it => it.data,
        ))
  }

  askForJoiningGroup(body: { invitation_code: string; is_try: boolean }) {
    return this.httpClient
      .post<Response<any>>(`${this.prefix}/api/v1/group_study/groups/join`, body)
      .pipe(
        map(it => {
          it.data.group = new Group(it.data.group)
          return it.data
        }),
        catchError(err => of(err.error.errors)),
      )
  }

  enroll(body: { name: string; type: string }) {
    return this.httpClient
      .post<Response<any>>(`${this.prefix}/api/v1/group_study/groups`, body)
      .pipe(
        map(it => {
          it.data.group = new Group(it.data.group)
          return it.data
        }),
      )
  }

  // 更新用户浏览记录
  track(body: {event_type: string; group_id: string; target?: string  }) {
    return this.httpClient.post<any>(`${this.prefix}/api/v1/group_study/track`, body)
  }

  // 获取活动参与权限, 目前活动id写死
  getActivityDetail(activityId: string) {
    return this.httpClient
      .get<Response<{ group_study?: Array<Group>; activity: ActivityRes }>>(`${this.prefix}/api/v1/activities/${activityId}`)
      .pipe(
        map(it => it.data),
      )
  }

  getActivities() {
    return this.httpClient
      .get<Response<{activities: Array<ActivityRes>}>>(`${this.prefix}/api/v1/activities/me`)
      .pipe(
        map(it => it.data),
      )
  }

  updateActivityStatus(activityId: string) {
    return this.httpClient.post<{activity_id: string}>(`${this.prefix}/api/v1/activities/read`, {
      activity_id: activityId,
    }).subscribe()
  }

  receiveReward(groupId: string) {
    return this.httpClient.post<Response<any>>(`${this.prefix}/api/v1/group_study/groups/reward`, {
      group_id: groupId,
    }).pipe(
      map(it => it.data),
    )
  }

  getReport(groupId: string) {
    return this.httpClient.get<Response<any>>(`${this.prefix}/api/v1/group_study/groups/reports`, {
      params: {
        group_id: groupId,
      },
    }).pipe(
      map(it => it.data.reports.map((report: ReportRes) => new Report(report)) as Report[]),
    )
  }

  getdanmu(count: number) {
    return this.httpClient.get<Response<{danmakus: string[]}>>(`${this.prefix}/api/v1/group_study/groups/danmakus`, {
      params: {
        count: count,
      },
    }).pipe(
      map(it => it.data.danmakus),
    )
  }

  dismissGroup(groupId: string) {
    return this.httpClient.post<Response<{group: GroupRes}>>(`${this.prefix}/api/v1/group_study/groups/${groupId}/dismiss`, {})
      .pipe(
        map(res => res.data.group),
      )
  }
}
