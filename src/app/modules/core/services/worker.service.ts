import { DestroyRef, Injectable, inject } from '@angular/core'
import { releaseProxy, wrap } from 'comlink'
import { captureException } from '@sentry/angular'
import { StudyWorker } from '../../../workers/study.type'
import { CommonWorker } from '../../../workers/common.type'

type WorkerName = 'study' | 'common'

type ValueOf<T> = T[keyof T]

// eslint-disable-next-line @typescript-eslint/no-unused-vars
declare let __webpack_public_path__: string

@Injectable({
  providedIn: 'root',
})
export class WorkerService {
  private destroyRef = inject(DestroyRef)

  private workerMap: Partial<Record<WorkerName, ReturnType<ValueOf<typeof this.workerGetterMap>>>> = {}
  private workerGetterMap = {
    study: () => {
      const worker = new Worker(/* webpackChunkName: "study-worker" */new URL('../../../workers/study.worker.ts', import.meta.url))
      worker.onerror = err => {
        console.error('From worker: ', err.message)
        captureException(err)
      }

      return wrap<StudyWorker>(worker)
    },
    common: () => {
      const worker = new Worker(/* webpackChunkName: "common-worker" */new URL('../../../workers/common.worker.ts', import.meta.url))
      worker.onerror = err => {
        console.error('From worker: ', err.message)
        captureException(err.error)
      }
      return wrap<CommonWorker>(worker)
    },
  } as const

  constructor() {
    // webworker 不能从 cdn 加载，因此需要设置 __webpack_public_path__
    __webpack_public_path__ = ''

    this.destroyRef.onDestroy(() => {
      Object.values(this.workerMap).forEach(v => {
        v[releaseProxy]()
      })
    })
  }

  getWorker<T extends WorkerName>(name: T) {
    let cache = this.workerMap[name]
    if (!cache) {
      cache = this.workerGetterMap[name]()
      this.workerMap[name] = cache
    }
    return cache as ReturnType<typeof this.workerGetterMap[T]>
  }
}
