import { DOCUMENT } from '@angular/common'
import { Inject, Injectable } from '@angular/core'
import { Modules, isOverSpecifiedAppVersion, mockMethod, setConfig } from '@maimemo/client-frontend-bridge'
import { PRODUCT_EXAMPLE } from '../../../configs/app.config'
import { isAndroid } from '../../shared/helper/env'

export interface ClientSignUpGroupStudy {
  name?: string
  error?: string
  dismiss?: boolean
}

export interface ButtonInClientAlert {
  id: string
  text: string
  text_color?: string
}

export type Product = typeof PRODUCT_EXAMPLE

export interface ShareResult {
  success: boolean
  code: number
  desc: string
}

export type PaymentOptions = Parameters<typeof Modules.purchase.requestPayment>[0] & {
  product: Product
  callback: (params: Awaited<ReturnType<typeof Modules.purchase.requestPayment>>) => void
}

@Injectable({
  providedIn: 'root',
})
export class ClientFrontendCommunicationService {
  groupStudyMethods: string[] = []

  private _win = this._doc.defaultView ?? window
  private isAndroid = isAndroid(this._win)

  constructor(
    @Inject(DOCUMENT) private _doc: Document,
  ) {
    this.mockClientMethods()
  }

  clientOpenNotificationPermissionIfNeed(title: string, message: string, callback?: (isPermit: boolean) => void) {
    const args = {
      title: title,
      message: message,
      callback: callback ?? ((isPermit: boolean) => {
        if (isPermit) {
          this.clientToast('已开启通知')
        } else {
          this.clientToast('未开启通知')
        }
      }),
    }
    Modules.common.openNotificationPermissionIfNeed(args).then().catch(console.error)
  }

  clientAlertJoinFailed(title: string, message: string, type: string) {
    if (this.isOverSpecifiedAppVersion('4.4.30')) {
      const buttons = [] as ButtonInClientAlert[]
      let buttonCallBack
      const setButtonsAndCallback = (type: string) => {
        switch (type) {
          case 'group_user_in_other_group':
            buttons.push({
              id: '0',
              text: '查看我的队伍',
            })
            buttonCallBack = () => {
              window.Memo.reload()
            }
            break
          case 'group_not_found':
            buttons.push({
              id: '0',
              text: '确定',
            })
            buttonCallBack = () => {}
            break
          case 'group_already_joined':
            buttons.push({
              id: '0',
              text: '取消',
            })
            buttonCallBack = () => {}
            break
          // case 'group_max_members_exceeded':
          //   buttons = buttons.concat([
          //     {
          //       id: '0',
          //       text: '取消',
          //     },
          //     {
          //       id: '1',
          //       text: '创建队伍',
          //     }])
          //   buttonCallBack = (args: string) => {
          //     if (args === '0') {
          //       return
          //     } else {
          //       const groupName = window.Memo.generateGroupName()
          //       this.clientSignUpGroupStudy({
          //         name: groupName,
          //       })
          //     }
          //   }
          //   break
          case 'can_not_join_group':
            buttons.push({
              id: '0',
              text: '我知道了',
            })
            buttonCallBack = () => {}
            break
          default:
            buttons.push({
              id: '0',
              text: '确定',
            })
        }
      }
      setButtonsAndCallback(type)
      Modules.common.alert({
        title: title,
        message: message,
        buttons: buttons,
        button_callback: buttonCallBack,
      } as any,
      )
    } else {
      if (window.Memo.alertJoinFailed) {
        window.Memo?.alertJoinFailed(title, message, type)
      }
    }
  }

  clientAlert(opts: Parameters<typeof Modules.common.alert>[0]) {
    if (this.isOverSpecifiedAppVersion('4.4.30')) {
      return Modules.common.alert(opts)
    } else {
      if (window.Memo.alertJoinFailed) {
        return Promise.resolve(window.Memo?.alertJoinFailed(opts.title, opts.message))
      }

      return Promise.reject('请更新到最新版本以使用此功能')
    }
  }

  clientSetPageTitle(title: string) {
    const args = { title: title }
    Modules.common.setTitle(args).then().catch(console.error)
  }

  clientShareInvitationCode(code: string) {
    const args = { code: code }
    Modules.groupStudy.shareInvitationCode(args).then().catch(console.error)
  }

  clientStartLoading() {
    Modules.common.setLoading({ is_loading: true, is_full_screen: true }).then().catch(console.error)
  }

  clientEndLoading() {
    Modules.common.setLoading({ is_loading: false, is_full_screen: true }).then().catch(console.error)
  }

  clientSignUpGroupStudy(info: ClientSignUpGroupStudy) {
    Modules.groupStudy.signUpGroupStudy({
      info: info as any,
      create_group: window.Memo.createGroup,
      generate_group_name: window.Memo.generateGroupName,
    })
  }

  clientUploadStudyRecord() {
    Modules.groupStudy.uploadStudyRecord()
      .catch(() => {
        setTimeout(() => {
          Modules.groupStudy.uploadStudyRecord()
        }, 1000)
      })
  }

  clientToast(info: string, type?: string) {
    Modules.common.toast({ message: info, type: type } as any)
  }

  clientVibrate() {
    Modules.common.vibrate()
  }

  clientCopy(content: string, tips: string) {
    return Modules.common.copy({
      content,
      msg: tips,
    })
  }

  async mockClientMethods() {
    this.groupStudyMethods = [] as string[]

    if (!this.isOverSpecifiedAppVersion('4.4.30')) {
      setConfig({ mockEnabled: true })
      mockMethod('common', 'setTitle', async (args: { title: string }) => {
        if (window.Memo?.setPageTitle) {
          window.Memo?.setPageTitle(args.title)
        }
      })
      mockMethod('groupStudy', 'shareInvitationCode', async (args: { code: string }) => {
        window.Memo?.shareInvitationCode(args.code)
      })
      mockMethod('common', 'setLoading', async (args: { is_loading: boolean }) => {
        if (args.is_loading) {
          if (window.Memo?.startLoading) {
            window.Memo?.startLoading('正在加载中')
          }
        } else {
          setTimeout(() => {
            window.Memo?.endLoading?.()
          }, 0)
        }
      })
      mockMethod('groupStudy', 'uploadStudyRecord', async () => {
        if (window.Memo?.uploadStudyRecord) {
          window.Memo?.uploadStudyRecord()
        }
      })
      mockMethod('groupStudy', 'signUpGroupStudy', async (args: any) => {
        if (window.Memo.signUpGroupStudy) {
          window.Memo.signUpGroupStudy(JSON.stringify(args.info))
        }
      })
      mockMethod('common', 'toast', async (args: { message: string }) => {
        if (window.Memo.toast) {
          window.Memo?.toast(args.message)
        }
      })
    }
  }

  isOverSpecifiedAppVersion(specifiedAppVersion: string): boolean {
    return isOverSpecifiedAppVersion(specifiedAppVersion)
  }

  clientShare(linkUrl: string) {
    Modules.common.showShareChannels(
      {
        channels: ['wechat', 'qq', 'qzone', 'weibo', 'link', 'album', 'wechat_timeline'],
        callback: (channel: string) => {
          share(channel, linkUrl)
        },
      },
    )

    function share(channel: string, url: string) {
      Modules.common.share({
        channel: channel,
        type: 'web_page',
        scene: '',

        // @ts-ignore
        share_content: { desc: '分享测试', img_url: '', link: linkUrl, thumbnail_url: '', title: '分享测试' },
        callback_listener: () => {
          //
        },
      })
    }
  }

  share(opts: Parameters<typeof Modules.common.share>[0]) {
    return Modules.common.share(opts)
  }

  requestPayment(options: PaymentOptions) {
    const isCompatible = this.isAndroid
      ? this.isOverSpecifiedAppVersion('5.0.01')
      : this.isOverSpecifiedAppVersion('4.10.10')

    if (!isCompatible) {
      return Promise.reject('请更新到最新版本以使用支付功能')
    }

    return Modules.purchase.requestPayment(options)
  }

  setNavButtons(args: Parameters<typeof Modules.common.setNavButtons>[0]) {
    return Modules.common.setNavButtons(args)
  }

  async getTokenFromClient(): Promise<string> {
    return Modules.common.getUserAccessToken()
      .then(res => res.data.token)
      .catch(err => {
        console.error(err)
        return ''
      })
  }

  async getStudyingBookInfo() {
    return Modules.study.getStudyingBookInfo()
      .then(res => res.data)
  }
}
