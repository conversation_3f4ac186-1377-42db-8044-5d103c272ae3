import { HttpClient } from '@angular/common/http'
import { Injectable, computed, inject, signal } from '@angular/core'
import { ModuleReturnType, Modules, ModulesType, getClientModuleInfo } from '@maimemo/client-frontend-bridge'
import { captureEvent, setTag } from '@sentry/angular'
import { BehaviorSubject, Observable, map, of, tap } from 'rxjs'
import { toSignal } from '@angular/core/rxjs-interop'
import { environment } from '../../../../environments/environment'
import { UserInfo, UserInfoRes } from '../../group-study-v2/models/member.model'
import { AccessToken, Response } from '../../shared/types'
import { MemoAlertService } from '../../ui/alert/alert.service'
import { ClientFrontendCommunicationService } from './client-frontend-communication.service'

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private httpClient = inject(HttpClient)
  private alert = inject(MemoAlertService)
  private cfc = inject(ClientFrontendCommunicationService)
  private readonly prefix = '/pages/activity-center'

  private _userInfo$ = new BehaviorSubject<UserInfo | undefined>(undefined)
  private userInfoFromClient = signal<ModuleReturnType<ModulesType['common']['getUserInfo']> | undefined>(undefined)

  private _token = ''
  private _accessToken: AccessToken | undefined
  private _totalSignDaysFromServer = 0
  private _totalSignDaysFromClient = 0

  private _studiedWordCount = 0

  userInfo$ = this._userInfo$.asObservable()
  userInfoSignal = toSignal(this._userInfo$)

  shouldHidePaymentInfo = computed(() => this.userInfoFromClient()?.hide_payment_info === true)

  constructor() {
    getClientModuleInfo('study').then(res => {
      if (!res.methods.includes('getDailyStatistics')) {
        return
      }
      Modules.study.getDailyStatistics()
        .then(r => {
          this._totalSignDaysFromClient = r.data.total_sign_count
          this._studiedWordCount = r.data.studied_word_count
        })
        .catch(e => {
          console.error('[USER] get daily statistics error', e)
          captureEvent(e)
        })
    })
  }

  get userInfo() {
    return this._userInfo$.value
  }

  get legacyToken(): string {
    return this._token
  }

  get isInDebtLearningMode() {
    return !!this.userInfo?.debt_learning && this.userInfo.debt_learning.status === 'INDEBTED'
  }

  get totalSignDays(): number {
    return this._totalSignDaysFromClient || this._totalSignDaysFromServer
  }

  private get maxWordsLimit(): number {
    return this.userInfo?.max_voc_count ?? 0
  }

  get usableWordsLimit(): number {
    return this.maxWordsLimit - this._studiedWordCount
  }

  private get cachedToken(): AccessToken | undefined {
    return this._accessToken
  }

  private set cachedToken(token: AccessToken | undefined) {
    this._accessToken = token
  }

  getAccessToken(legacyToken: string): Observable<AccessToken> {
    const cachedToken = this.cachedToken
    if (cachedToken) {
      return of(cachedToken)
    }
    return this.httpClient.post<Response<{ token: AccessToken }>>(`${this.prefix}/api/v1/users/login`, {
      legacy_token: legacyToken,
      responseType: 'json',
    }).pipe(map(x => {
      const token = x.data.token
      this.cachedToken = token
      setTag('token', token.access_token)
      return token
    }))
  }

  updateToken(token: string) {
    this._token = token
  }

  queryUserInfo(): Observable<UserInfo> {
    if (this.userInfo) {
      return of(this.userInfo)
    }

    return this.httpClient.post<Response<{ user: UserInfoRes }>>('/api/v1/users/info', {})
      .pipe(
        map(res => toUserInfo(res.data.user)),
        tap(user => {
          this._totalSignDaysFromServer = user.total_sign_days
          this._userInfo$.next(user)
        }),
      )
  }

  async checkShouldHidePaymentInfo() {
    if (this.userInfoFromClient() !== undefined) {
      return this.shouldHidePaymentInfo()
    }

    return Modules.common.getUserInfo({})
      .then(res => {
        this.userInfoFromClient.set(res.data)
        return res.data.hide_payment_info === true
      })
      .catch(e => {
        console.error('[USER] get user info error', e)
        return false
      })
  }

  checkDebtLearning(): boolean {
    if (!this.isInDebtLearningMode) {
      return false
    }

    this.alert.show({
      header: '欠款期间，此功能不可用',
      subHeader: `你欠款了 ${Math.abs(this.usableWordsLimit)} 个单词上限，此功能在欠款期间不可使用。`,
      message: '购买可享 14 天无理由退款。',
      cssClass: ['memo-alert', 'warning-message'],
      buttons: [
        { id: 'cancel', text: '我知道了' },
        {
          id: 'buy',
          text: '现在购买',
          cssClass: 'red-text',
          handler: async () => {
            const info = await getClientModuleInfo('purchase')

            if (!info.methods.includes('showWordLimitProductsModal')) {
              this.cfc.clientToast('请更新版本或自行购买单词上限')
              return
            }

            Modules.purchase.showWordLimitProductsModal({}).then(v => {
              const successStatusList = ['success', 'pending', 'unknown']

              if (environment.production) {
                captureEvent({
                  message: 'Purchase',
                  extra: v.data,
                })
              } else {
                console.debug('Purchase', v.data)
              }

              if (successStatusList.includes(v.data.status)) {
                location.reload()
              }
            })
          },
        },
      ],
    })

    return true
  }
}

function toUserInfo(res: UserInfoRes): UserInfo {
  return {
    avatar: res.inf_avatar,
    userId: Number(res.inf_uid),
    name: res.inf_username,
    total_sign_days: res.inf_total_checkout ? Number(res.inf_total_checkout) : 0,
    max_voc_count: res.inf_words_limit ? Number(res.inf_words_limit) : 0,
    debt_learning: res.inf_debt_learning,
  }
}
