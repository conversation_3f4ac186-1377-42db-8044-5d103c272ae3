import { Injectable } from '@angular/core'
import { UserBase } from '../../group-study-v2/models/member.model'
import { UserService } from './user.service'

@Injectable({
  providedIn: 'root',
})
export class UserMockService extends UserService {

  mockUser: UserBase = {
    'userId': 27516775,
    'avatar': 'http://cdn2.maimemo.com/avatar/9d/93/9d93a23f52d0335a043449fcc07dfd3b.jpg?1679196737552',
    'name': 'kk雅思冲啊！！',
  }

  override get userInfo(): UserBase {
    return this.mockUser
  }
}
