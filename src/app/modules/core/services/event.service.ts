import { HttpClient } from '@angular/common/http'
import { Injectable, inject } from '@angular/core'
import { EventMap, EventType, TrackEvent, TrackEvents } from '../../shared/event'

@Injectable({
  providedIn: 'root',
})
export class EventService {
  private http = inject(HttpClient)

  track<T extends EventType, K extends keyof EventMap[T]>(eventType: T, events: TrackEvent<T, K>[]) {
    const body = {
      events: {
        [eventType]: events,
      },
    } as TrackEvents<T, K>

    return this.http.post('https://api.maimemo.com/api/v1/datacenter/logs', body)
  }
}
