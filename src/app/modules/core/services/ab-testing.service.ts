import { HttpClient } from '@angular/common/http'
import { Injectable, inject } from '@angular/core'
import { Observable, catchError, map, of } from 'rxjs'
import { Response } from '../../shared/types'

@Injectable({
  providedIn: 'root',
})
export class ABTestingHelper {
  private http = inject(HttpClient)

  getTestGroup(code: string): Observable<{ group?: string }> {
    return this.http.post<Response<{ group?: string }>>(`/api/v1/abtest/rules/${code}/evaluates`, null)
      .pipe(
        catchError(error => {
          console.error(error)
          return of({ data: {} })
        }),
        map(res => res.data),
      )
  }
}
