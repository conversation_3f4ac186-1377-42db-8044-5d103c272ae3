import { Injectable } from '@angular/core'
import { ActivityService } from '../../group-data/activity/activity.service'

@Injectable({
  providedIn: 'root',
})
export class ABTestingService {
  constructor(
    public activityService: ActivityService,
  ) { }

  activitiesInABTest = {
    tickle: [
      '63411cf9b2e7333f9222409a',
    ],
    interactiveButton: [
      '63411cf9b2e7333f9222409a',
    ],
  }

  /**
   * 判断一个用户属于AB测试的哪一组
   */
  getUserABGroupType(groupId: string): 'A' | 'B' | undefined {
    const AGroup = '01234567'.split('')
    const BGroup = '89abcdef'.split('')
    const lastChar = groupId[groupId.length - 1]
    if (AGroup.includes(lastChar)) {
      return 'A'
    } else if (BGroup.includes(lastChar)) {
      return 'B'
    }
    return undefined
  }

  isShowInteractiveButtonInABTest(groupId: string): boolean {
    if (!this.activitiesInABTest.interactiveButton.includes(this.activityService.activityId)) {
      return true
    }
    const type = this.getUserABGroupType(groupId)
    if (type === 'A') {
      return true
    } else if (type === 'B') {
      return false
    }
    return false
  }

  tickleInABTest(groupId: string, tickleInGroupA: () => Promise<unknown>, tickleInGroupB: () => void) {
    if (!this.activitiesInABTest.interactiveButton.includes(this.activityService.activityId)) {
      console.log('A1')
      return tickleInGroupA()
    }
    const type = this.getUserABGroupType(groupId)
    console.log('type')
    if (type === 'A') {
      return tickleInGroupA()
    } else if (type === 'B') {
      return tickleInGroupB()
    }
  }
}
