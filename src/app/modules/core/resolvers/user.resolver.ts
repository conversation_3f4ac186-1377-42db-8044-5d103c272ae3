import { Inject, Injectable } from '@angular/core'
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router'
import { catchError, from, of, retry, switchMap, tap } from 'rxjs'
import { IS_DEV_MODE } from '../../../configs/app.config'
import { GroupStudyClientService } from '../../group-study/service/services/group-study-client.service'
import { UserService } from '../services/user.service'

@Injectable({
  providedIn: 'root',
})
export class UserInfoResolver {
  constructor(
    private user: UserService,
    private client: GroupStudyClientService,
    @Inject(IS_DEV_MODE) private isDevMode: boolean,
  ) {}

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const token = route.queryParamMap.get('token')

    // 先拿本地的
    if (localStorage && localStorage.getItem('token')) {
      this.user.updateToken(localStorage.getItem('token')!)
    }

    const handleToken$ = token
      ? of(token)
      : this.user.legacyToken
        ? of(this.user.legacyToken)
        : from(this.client.cfc.getTokenFromClient())

    return handleToken$
      .pipe(
        tap(tk => {
          if (tk) {
            localStorage && localStorage.setItem('token', tk)
            this.user.updateToken(tk)
          } else {
            !this.isDevMode && this.client.clientToast('请使用客户端登录')
          }
        }),
        switchMap(() =>
          this.user.queryUserInfo().pipe(
            retry({
              count: 2,
              delay: 300,
            }),
            catchError(error => {
              console.error(error)
              return of(null)
            }),
          ),
        ),
      )
  }
}
