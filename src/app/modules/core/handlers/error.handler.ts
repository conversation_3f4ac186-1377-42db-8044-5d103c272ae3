import { Injectable } from '@angular/core'
import { SentryErrorHandler } from '@sentry/angular'
import { ClientFrontendCommunicationService } from '../services/client-frontend-communication.service'

@Injectable()
export class GlobalErrorHandler extends SentryErrorHandler {
  constructor(public cfc: ClientFrontendCommunicationService) {
    super({
      showDialog: false,
    })
  }

  override handleError(error: any) {
    const chunkFailedMessage = /Loading chunk [\d]+ failed/
    if (chunkFailedMessage.test(error.message)) {
      this.cfc.clientToast('请重新打开页面')
    }
    super.handleError(error)
  }
}
