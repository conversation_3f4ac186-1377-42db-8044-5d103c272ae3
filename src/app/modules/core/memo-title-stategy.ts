import { Injectable } from '@angular/core'
import { DefaultTitleStrategy, RouterStateSnapshot } from '@angular/router'
import { Modules } from '@maimemo/client-frontend-bridge'

@Injectable({
  providedIn: 'root',
})
export class MemoTitleStrategy extends DefaultTitleStrategy {

  override updateTitle(snapshot: RouterStateSnapshot): void {
    const title = this.buildTitle(snapshot)
    super.updateTitle(snapshot)
    title && Modules.common.setTitle({
      title,
    })
      .catch(err => console.error('[Title]: ', err))
  }
}
