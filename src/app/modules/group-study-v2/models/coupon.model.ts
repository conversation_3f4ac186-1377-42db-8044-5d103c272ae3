import { ISODate } from '../../shared/types'

export const CouponStatus = {
  NORMAL: 'NORMAL',
  LOCKED: 'LOCKED',
  USED: 'USED',
  EXPIRED: 'EXPIRED',
  DELETED: 'DELETED',
} as const

export type CouponStatusName = typeof CouponStatus[keyof typeof CouponStatus]

export interface Coupon {
  id: string
  status: CouponStatusName
  name: string
  description: string

  valid_start_time: ISODate
  valid_end_time: ISODate

  unavailable_reason: string
  selected: boolean
  user_id: number
  created_time: ISODate
  updated_time: ISODate
}
