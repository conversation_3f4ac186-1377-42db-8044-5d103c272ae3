import { CamelCasedPropertiesDeep } from 'type-fest'
import { ISODate } from '../../shared/types'

/**
 * VOC: 单词量
 */
type RewardType = 'VOC'

export type RewardReason =
  // 两人组队 - 一模一样
  | '11'

  // 两人组队 - 一模两样
  | '12'

  // 三人组队 - 一模一样
  | '111'

  // 三人组队 - 一模两样 （相同成员中包括自己）
  | '112'

  // 三人组队 - 一模两样 （相同成员中不包括自己）
  | '211'

  // 三人组队 - 一模三样
  | '123'

  // 自由组队
  | 'CUSTOM'

  // 自由组队首日签到奖励
  | 'CUSTOM_FIRST_DAY'

type RewardStatus = 'UNREWARDED' | 'CANCELLED'

export interface HistoryRes {
  /**
   * id
   */
  id: string

  /**
   * 活动 id
   */
  activity_id: string

  /**
   * 队伍 id
   */
  group_id: string


  /**
   * 成员 uid
   */
  member_id: number

  /**
   * 设备 id
   */
  device_id: string

  /**
   * 是否签到
   */
  is_signed: boolean

  /**
   * 签到头像
   */
  sign_avatar?: number

  /**
   * 签到时间
   */
  sign_date?: ISODate

  /**
   * 学习单词数达标时间
   */
  study_voc_succeeded_time?: ISODate

  /**
   * 学习时长达标时间
   */
  study_time_succeeded_time?: ISODate

  /**
   * 学习成功时间
   */
  study_succeeded_time?: ISODate

  /**
   * 当日学习时长（秒）
   */
  study_time: number

  /**
   * 当日学习单词数
   */
  learned_voc_count: number

  /**
   * 当日新学单词数
   */
  learned_new_voc_count: number

  /**
   * 正在学习的书籍 id
   */
  learning_book_id: string

  /**
   * 正在学习的书籍分类
   */
  learning_book_category: string

  /**
   * 正在学习的书籍已学单词数
   */
  learning_book_learned_voc_count: number

  /**
   * 正在学习的书籍总计单词数
   */
  learning_book_voc_count: number

  /**
   * 奖励类型
   */
  reward_type: RewardType

  /**
   * 奖励数量
   */
  reward_count: number

  /**
   * 奖励原因
   */
  reward_reason?: RewardReason

  /**
   * 奖励状态
   */
  reward_status: RewardStatus

  /**
   * 撤回原因
   */
  reward_cancelled_reason?: string

  /**
   * 创建时间
   */
  created_time: ISODate

  /**
   * 更新时间
   */
  updated_time: ISODate

}

export type History = CamelCasedPropertiesDeep<HistoryRes>
