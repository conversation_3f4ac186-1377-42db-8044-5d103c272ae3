import { CamelCasedPropertiesDeep } from 'type-fest'
import { ISODate } from '../../shared/types'

type MemberStatus = 'NORMAL' | 'EXITED'

type MemberType = 'CREATOR' | 'PARTICIPANT'


export interface UserBaseRes {
  /**
   * 成员 uid
   */
  user_id: number

  /**
   * 成员头像
   */
  avatar: string

  /**
   * 成员昵称
   */
  name: string
}

export interface MemberRes extends UserBaseRes {

  /**
   * 加入时间
   */
  joined_time: ISODate

  /**
   * 用户状态，自建组队成员会有值
   */
  status?: MemberStatus

  /**
   * 成员身份类型
   */
  type: MemberType

  /**
   * 退出队伍时间
   */
  exited_time?: ISODate

  /**
   * 最后阅读时间
   */
  last_read_time?: ISODate

  /**
   * 当前用户
   */
  is_current_user: boolean

  /**
   * 在队伍中的位置, 只有加入自建组队时才允许指定位置
   */
  position?: number

}

export interface UserInfoRes {
  inf_avatar: string
  inf_uid: string
  inf_debt_learning?: {
    id: string
    status: 'INDEBTED' | 'PAID_OFF' | 'DELETED'
    device_id: string
  }
  inf_username: string
  inf_total_checkout: string
  inf_words_limit: string
}

export interface UserInfo extends UserBase {
  max_voc_count?: number
  total_sign_days: number
  debt_learning?: UserInfoRes['inf_debt_learning']
}

export type Member = CamelCasedPropertiesDeep<MemberRes>
export type UserBase = CamelCasedPropertiesDeep<UserBaseRes>
