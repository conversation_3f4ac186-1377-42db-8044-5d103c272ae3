import { CamelCasedPropertiesDeep } from 'type-fest'
import { ISODate } from '../../shared/types'

export interface ReportRes {
  /**
   * 报告 Id
   */
  _id: string

  /**
   * 活动 Id
   */
  activity_id: string

  /**
   * 队伍 Id
   */
  group_id: string

  /**
   * 创建者 id
   */
  creator: number

  /**
   * 巩固单词数
   */
  familiar_voc_count?: number

  /**
   * 上次阅读报告时间
   */
  first_read_time?: ISODate

  /**
   * 奖励领取时间
   */
  rewarded_time?: ISODate

  /**
   * 创建时间
   */
  created_time: ISODate

  /**
   * 更新时间
   */
  updated_time: ISODate

}


export type Report = CamelCasedPropertiesDeep<ReportRes>
