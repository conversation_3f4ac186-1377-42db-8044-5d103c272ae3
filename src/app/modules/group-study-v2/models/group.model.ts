import { CamelCasedPropertiesDeep } from 'type-fest'
import { ISODate } from '../../shared/types'
import { CustomGroupStudyActivityConfigRes } from './activity.model'
import { EventRes } from './event.model'
import { HistoryRes } from './history.model'
import { MemberRes } from './member.model'

export type GroupStatus =
  // 组队中
  | 'GROUPING'

  // 组队成功
  | 'GROUP_SUCCEEDED'

  // 组队失败
  | 'GROUP_FAILED'

  // 磨合期
  | 'TESTING'

  // 磨合失败
  | 'TEST_FAILED'

  // 学习期
  | 'LEARNING'

  // 学习成功
  | 'LEARN_SUCCEED'

  // 手动解散
  | 'GROUP_DISMISSED'

/**
 * COUPLE: 两人组队
 * TRIO: 三人组队
 * CUSTOM: 自由组队
 */
export type GroupType = 'COUPLE' | 'TRIO' | 'CUSTOM'

export interface GroupRes {
  /**
   * 队伍 id
   */
  id: string

  /**
   * 队伍 id
   */
  group_id: string

  /**
   * 活动 id
   */
  activity_id: string

  /**
   * 队伍名称
   */
  name: string

  /**
   * 队伍类型
   */
  type: GroupType

  /**
   * 组队成功时间
   */
  group_succeeded_time?: ISODate

  /**
   * 学习开始时间
   */
  study_start_time?: ISODate

  /**
   * 队伍状态
   */
  status: GroupStatus

  /**
   * 队伍成员
   */
  members: MemberRes[]

  /**
   * 创建时间
   */
  created_time: ISODate

  /**
   * 更新时间
   */
  updated_time: ISODate

  /**
   * 心心状态 （客户端用 520活动特殊）
   */
  heart_status?: 'GREY' | 'RED'

  /**
   * 获得奖励最少的用户 “非酋奖”
   */
  unlucky_members?: number[]

  promotion?: {
    banners: string[]
    link: string
  }

}

export interface GroupWithConfigRes extends GroupRes {
  // 队伍创建者
  creator: number

  // 活动配置
  config: CustomGroupStudyActivityConfigRes
}

export interface GroupDataRes {

  /**
   * 当前用户的 ID
   */
  current_user_id?: number

  /**
   * 当前用户所在的队伍: 如果当前用户没有加入队伍，则为 null
   */
  group: GroupRes | null

  /**
   * 邀请者的 ID: 如果传入的邀请码无效，则该字段为 null
   */
  invite_user_id?: number

  /**
   * 邀请者的队伍: 如果传入的邀请码无效，则该字段为 null
   */
  invite_user_group?: GroupRes

  /**
   * 当前用户所在队伍的奖励历史: 如果 scope = FULL，但是用户所在的队伍没有奖励，则该字段为 []
   */
  history?: HistoryRes[]

  /**
   * 用于互动板显示
   */
  events?: EventRes[]

  /**
   * 新规则上线后过渡用
   * @deprecated
   */
  show_group_succeeded_animation?: boolean

  // 只有 group 不为空时，才会返回此字段
  // 表示队伍是否已发布
  is_public?: boolean
}

export type PublishLogStatus = 'PUBLIC' | 'DELETED'

export interface GroupStudyPublishLogRes {
  _id: string
  status: PublishLogStatus
  activity_id: string
  group_id: string
  creator: number

  /**
   * 当前队伍人数
   */
  members_count: number

  /**
   * 队伍剩余人数
   */
  remaingin_members_count: number

  config: CustomGroupStudyActivityConfigRes
  created_time: ISODate
  updated_time: ISODate
  deleted_time?: ISODate
}

export interface GroupMeta {
  /**
   * 活动 ID
   */
  activity_id?: string

  /**
   * 邀请码
   */
  invitation_code?: string

  /**
   * 队伍 ID
   */
  group_id?: string
}

export interface GroupReqParams extends Omit<GroupMeta, 'group_id'> {

  scope?: 'FULL' | 'GROUP'

  /**
   * 更新时间，scope 为 FULL时,传递此字段后 history 和 events 会仅获取 update_time 之后的数据
   *
   * 格式是 YYYY-MM-DD HH:mm:ss
   */
  update_time?: ISODate
}

export interface GroupingStatusRes {
  // 自建组队数量上限
  max_custom_group_count: number

  // 该用户已参与的自建队伍数量
  custom_group_count: number
}

export type Group = CamelCasedPropertiesDeep<GroupRes>
export type GroupWithConfig = CamelCasedPropertiesDeep<GroupWithConfigRes>
export type GroupData = CamelCasedPropertiesDeep<GroupDataRes>
export type GroupStudyPublishLog = CamelCasedPropertiesDeep<GroupStudyPublishLogRes>
