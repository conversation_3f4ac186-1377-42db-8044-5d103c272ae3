import { CamelCasedPropertiesDeep } from 'type-fest'
import { ISODate } from '../../shared/types'
import { Group } from './group.model'

export const VIEWABLE_EVENT_TARGET = {
  GROUP_SUCCEEDED_ANIMATION: 'GROUP_SUCCEEDED_ANIMATION',
} as const

export type EventPostType =
  // 学习记录
  | 'STUDY_RECORD'

  // 学习报告
  | 'REPORT'

  // 点击
  | 'CLICK'

  // 阅读
  | 'VIEW'

  // 点赞
  | 'LIKE'

  // 拍一拍
  | 'TICKLE'

export type EventType =
  // 创建队伍
  | 'CREATE_GROUP'

  // 加入队伍
  | 'JOIN_GROUP'

  // 组队失败
  | 'GROUP_FAILED'

  // 磨合失败
  | 'TEST_FAILED'

  // 组队成功
  | 'GROUP_SUCCEEDED'

  // 学习完成
  | 'LEARN_SUCCEED_TODAY'

  // 拍一拍
  | 'TICKLE'

  // 学习期结束
  | 'LEARN_SUCCEED'

  | 'TEST_SUCCEEDED'

  // 点赞
  | 'LIKE'

  // 阅读
  | 'VIEW'

  // 点击
  | 'CLICK'

  // 学习报告
  | 'REPORT'

  | 'INTERACTIVE_BOARD'

  | 'PAY'

  | 'REFUND'

export type TickleType =
  // 创建者 完成 目标 完成
  | 'DONE_DONE'

  // 创建者 完成 目标 未完成
  | 'DONE_UNDONE'

  // 创建者 未完成 目标 完成
  | 'UNDONE_DONE'

  // 创建者 未完成 目标 未完成
  | 'UNDONE_UNDONE'

  | 'GROUP_EVENT_SENDER_RECEIVER_UNDONE'


export interface EventRes {
  /**
   * 报告 Id
   */
  _id: string

  /**
   * 活动 Id
   */
  activity_id: string

  /**
   * 队伍 Id
   */
  group_id: string

  /**
   * 创建者 id
   */
  creator: number

  /**
   * 事件类型
   */
  event_type: EventType

  /**
   * 拍一拍类型
   */
  tickle_type?: TickleType

  /**
   * 目标 id
   */
  target_id?: string

  /**
   * 目标类别
   */
  target_class?: string

  /**
   * 内容
   */
  comment?: string

  /**
   * 创建时间
   */
  created_time: ISODate

}

export interface RefundPayload {
  group_status?: Group['status']
  amount: string
  members_count: string
  refund_status: 'REFUNDING' | 'REFUNDED'
}

export interface EventTrackBody {
  /**
   * 队伍 id
   */
  group_id: string

  /**
   * 事件类型
   */
  event_type: EventPostType

  /**
   * 事件目标 (CALENDAR_LAST_BUTTON)
   */
  target?: string

}

export type Event = CamelCasedPropertiesDeep<EventRes>
