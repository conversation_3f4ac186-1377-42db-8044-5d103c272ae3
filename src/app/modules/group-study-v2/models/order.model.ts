import { CamelCasedPropertiesDeep } from 'type-fest'
import { ISODate } from '../../shared/types'

/**
 * UNPAID: 待支付
 * PAID: 已支付
 * REFUND: 已退款
 */
type OrderStatus = 'UNPAID' | 'PAID' | 'REFUND'

export interface OrderRes {
  /**
   * 创建者
   */
  creator: number

  /**
   * 队伍 id
   */
  group_id: string

  /**
   * 订单 id
   */
  order_id: string

  /**
   * 订单状态
   */
  status: OrderStatus

  /**
   * 创建时间
   */
  created_time: ISODate

  /**
   * 更新时间
   */
  updated_time: ISODate

  /**
   * 支付时间
   */
  paid_time?: ISODate

  /**
   * 退款时间
   */
  refunded_time?: ISODate

}

export type Order = CamelCasedPropertiesDeep<OrderRes>
