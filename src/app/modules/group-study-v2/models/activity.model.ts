import { CamelCasedPropertiesDeep } from 'type-fest'
import { ISODate } from '../../shared/types'
import { GroupRes, GroupStatus } from './group.model'

export const PayBy = {
  CREATOR: 'CREATOR',
  EACH: 'ALL_MEMBER',
} as const

export type PaymentType = typeof PayBy[keyof typeof PayBy]

export type UserStatus = 'DOING' | 'UNREWARDED' | 'OVER' | 'EXITED'

export interface CustomGroupStudyActivityConfigRes {

  /**
   * 支付类型
   */
  payment_type: PaymentType

  /**
   * 队伍人数
   */
  group_size: number

  /**
   * 每日学习时长 (秒)
   */
  daily_study_duration: number

  /**
   * 每日学习单词数
   */
  daily_study_voc: number

  /**
   * 磨合期时长 (秒)
   */
  testing_duration: number

  /**
   * 学习期时长 （秒）
   */
  study_duration: number

  /**
   * 创建者加入队伍
   */
  creator_auto_join?: boolean

  /**
   * 加入队伍的每人天价格
   */
  price?: number

}

interface OfficialGroupStudyActivityConfigRes {
  /**
   * 报名开始时间
   */
  registration_start_time: ISODate

  /**
   * 报名截止时间
   */
  registration_end_time: ISODate

  /**
   * 磨合期时长 (秒)
   */
  testing_duration: number

  /**
   * 学习期时长 （秒）
   */
  study_duration: number

  /**
   * 组队学习倒计时 （秒）
   */
  group_duration: number

  /**
   * 每日学习时长 (秒)
   */
  daily_study_duration: number

  /**
   * 每日学习单词数
   */
  daily_study_voc: number

  /**
   * 奖励数量
   */
  reward_rule: RewardRuleRes

  /**
   * 特殊活动配置
   */
  special_event?: '520'

  /**
   * 特殊活动配置 开始时间
   */
  special_event_520_start_date?: ISODate

  /**
   * 特殊活动配置 特殊时间
   */
  special_event_520_special_date?: ISODate

  /**
   * 特殊活动配置 结束时间
   */
  special_event_520_end_date?: ISODate

}

export interface UserActivityRes {
  is_read?: boolean
  group_study?: {
    is_rewarded: boolean
    reward_vocs: number
    group_status: GroupStatus
  }
  last_read_time: ISODate

  /**
   * 创建时间
   */
  created_time: ISODate

  /**
   * 更新时间
   */
  updated_time: ISODate
}

type GroupStudyActivityConfigRes = OfficialGroupStudyActivityConfigRes | CustomGroupStudyActivityConfigRes

interface RewardRuleRes {

  /**
   * 两人组队-一模一样
   */
  pattern_11: number

  /**
   * 两人组队-一模两样
   */
  pattern_12: number

  /**
   * 三人组队-一模一样
   */
  pattern_111: number

  /**
   * 三人组队-一模两样 （包括自己）
   */
  pattern_112: number

  /**
   * 三人组队-一模两样 （不包括自己）
   */
  pattern_211: number

  /**
   * 三人组队-一模三样
   */
  pattern_123: number

}

export type ActivityType = 'GROUP_STUDY_' | 'GROUP_STUDY_CUSTOM' | 'GROUP_STUDY'

/**
 * 目前在新版自建组队中使用，默认是自建组队类型
 */
export interface ActivityRes<T extends ActivityType = 'GROUP_STUDY_CUSTOM'> {

  /**
   * 活动 Id
   */
  id: string

  /**
   * 活动 Id
   */
  _id: string

  /**
   * 活动名称
   */
  name: string

  /**
   * 活动标题 活动圈用
   */
  title: string

  /**
   * 描述 活动圈用
   */
  description: string

  decorate?: string
  decorate_style?: string
  seal_img?: string

  /**
   * 是否已读 活动圈用
   */
  is_read?: boolean

  /**
   * 活动类型
   */
  type: T

  /**
   * 创建者 id
   */
  creator?: number

  /**
   * 创建者类型
   */
  creator_type?: 'USER' | 'ADMIN'

  /**
   * 活动开始时间
   */
  start_time: ISODate

  /**
   * 活动结束时间
   */
  end_time?: ISODate

  /**
   * 活动状态
   */
  status?: string

  /**
   * 活动配置
   */
  config?: T extends 'GROUP_STUDY_CUSTOM' ? CustomGroupStudyActivityConfigRes : OfficialGroupStudyActivityConfigRes

  /**
   * 用户状态
   */
  user_status?: UserStatus

  /**
   * 创建时间
   */
  created_time: ISODate

  /**
   * 更新时间
   */
  updated_time: ISODate

  /**
   * 心心状态 （客户端用 520活动特殊）
   */
  heart_status?: 'GREY' | 'RED'

  /**
   * 关联的推广申请记录 id
   */
  proposal_id?: string

  extra?: {
    promotion_activities?: string[]
  }

}

export interface ActivityWithUserStatusRes<T extends ActivityType = 'GROUP_STUDY_CUSTOM'> extends Activity<T> {
  user_activity: UserActivityRes
}

export interface ActivityDetailRes<T extends ActivityType = 'GROUP_STUDY_CUSTOM'> {
  /**
   * 队伍信息
   */
  group_study?: GroupRes[]

  /**
   * 活动信息
   */
  activity: ActivityRes<T>
}

export type GroupStudyActivityConfig = CamelCasedPropertiesDeep<GroupStudyActivityConfigRes>
export type CustomGroupStudyActivityConfig = CamelCasedPropertiesDeep<CustomGroupStudyActivityConfigRes>
export type OfficialGroupStudyActivityConfig = CamelCasedPropertiesDeep<OfficialGroupStudyActivityConfigRes>
export type Activity<T extends ActivityType = 'GROUP_STUDY_CUSTOM'> = CamelCasedPropertiesDeep<ActivityRes<T>>
export type ActivityDetail<T extends ActivityType = 'GROUP_STUDY_CUSTOM'> = CamelCasedPropertiesDeep<ActivityDetailRes<T>>
export type ActivityWithUserStatus<T extends ActivityType = 'GROUP_STUDY_CUSTOM'> = CamelCasedPropertiesDeep<ActivityWithUserStatusRes<T>>
