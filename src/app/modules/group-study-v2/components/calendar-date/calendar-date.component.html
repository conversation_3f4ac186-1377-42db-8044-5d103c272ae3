@if (!blank()) {
  <header>
    <h4>{{ mainTitle() }}</h4>
    @if (rewardedAmount() !== -1) {
      <memo-badge [style.--badge-font-weight]="'bold'" [value]="rewardedAmount()" [withBorder]="true"></memo-badge>
    }
  </header>
  <section class="member-status-container" [class.grid-layout]="!calendarContentRef()">
    @if (calendarContentRef(); as template) {
      <ng-container [ngTemplateOutlet]="template"></ng-container>
    } @else {
      @for (m of members(); track $index; let i = $index) {
        @let member = membersForDisplay()[i];
        <app-calendar-date-member
          [calendarDateItem]="calendarDateItem()!"
          [skin]="member && memberStatsMap()?.[member.userId]?.skin"
          [isCurrentUser]="member && currentUserInfo()?.userId === member.userId"
          [isFinished]="member && !!memberStatsMap()?.[member.userId]?.isFinished"
          [isExited]="member && !!memberStatsMap()?.[member.userId]?.isExited"
          [themable]="member && !!memberStatsMap()?.[member.userId]?.studyLog?.rewardedAmount"
        ></app-calendar-date-member>
      }
    }
  </section>
} @else {
  <header>
    <h4>
      <ion-skeleton-text [animated]="true" style="width: 3em"></ion-skeleton-text>
    </h4>
  </header>
  <div>
    <ion-skeleton-text [animated]="true"></ion-skeleton-text>
    <ion-skeleton-text [animated]="true" style="width: 50%"></ion-skeleton-text>
  </div>
}
