import { ChangeDetectionStrategy, Component, TemplateRef, computed, contentChild, effect, input, signal } from '@angular/core'
import { UserService } from '../../../core/services/user.service'
import { getRGBAFromColorType } from '../../../shared/helper/color'
import { trackBy } from '../../../shared/helper/template'
import { createEmptyArray } from '../../../shared/helper/utils'
import { Member, UserBase } from '../../models/member.model'
import { reArrangeList } from '../../pages/study-detail/helper'
import { MemberDailyStudyStats } from '../../pages/study-report/type'
import { CalendarDateItem } from '../../utils/study'

@Component({
  selector: 'app-calendar-date',
  templateUrl: './calendar-date.component.html',
  styleUrls: ['./calendar-date.component.scss'],
  host: {
    '[class.active-date]': 'isActive()',
    '[class.disabled]': 'isDisabled()',
    '[style.--date-alpha]': 'dateAlpha()',
    '[style.--date-theme]': 'dateTheme()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class CalendarDateComponent {
  calendarContentRef = contentChild<TemplateRef<HTMLElement>, TemplateRef<HTMLElement>>('calendarContent', {
    read: TemplateRef,
  })

  blank = input(false)
  isActive = input(false)
  isDisabled = input(false)
  dateTitle = input<string>()
  members = input<Member[]>()
  calendarDateItem = input<CalendarDateItem>()
  memberStatsMap = input<Record<number, MemberDailyStudyStats>>()
  isThemable = input(true)
  currentUserStudyStats = input<MemberDailyStudyStats>()
  currentUserInfo = input<UserBase>()
  maxItemsPerRow = input<number>()

  blankList = createEmptyArray(10)
  rewardedAmount = computed(() => {
    const studyStatus = this.currentUserStudyStats()
    const dateItem = this.calendarDateItem()
    if (!studyStatus || !dateItem) return -1

    const rewardedAmount = studyStatus.studyLog?.rewardedAmount

    if (dateItem.offsetToToday < 0) {
      return rewardedAmount || 0
    }

    if (dateItem.offsetToToday === 0) {
      return rewardedAmount && studyStatus.isFinished ? rewardedAmount : -1
    }

    return -1
  })

  membersForDisplay = signal<Member[]>([])
  mainTitle = computed(() => {
    return this.dateTitle() || '第 ' + ((this.calendarDateItem()?.relativeIndex || 0) + 1) + ' 天'
  })

  trackByMemberId = trackBy('userId')

  private dateAlpha = computed(() => {
    return this.isDisabled() ? 0.3 : 1
  })

  dateTheme = computed(() => {
    const rgb = this.dateThemeRGB()
    return `${rgb.r}, ${rgb.g}, ${rgb.b}`
  })

  private dateThemeRGB = computed(() => {
    if (!this.isThemable()) return getRGBAFromColorType('grey-2')
    if (!this.members() || !this.memberStatsMap()) return getRGBAFromColorType('grey-2')

    const currentUserStats = this.currentUserStudyStats()

    // 未完成的就灰色
    if (!currentUserStats || !currentUserStats.isFinished || this.rewardedAmount() === 0) {
      return getRGBAFromColorType('grey-2')
    }

    return getRGBAFromColorType(currentUserStats.skin || 'grey-2')
  })

  gridColumnsConfig = ''
  gridGapConfig = 5

  constructor(
    private user: UserService,
  ) {
    effect(() => {
      const members = this.members()
      const memberStatsMap = this.memberStatsMap()
      if (!members || !memberStatsMap || !this.maxItemsPerRow()) return
      this.updateMembersForDisplay(members)
    })
  }

  private updateMembersForDisplay(members: Member[]) {
    // 先分类在学人员和已退出人员
    const stillLearningList: Member[] = []
    const exitedList: Member[] = []

    // 最后要居中显示的人员，有当前用户的话显示当前用户，没有的话将会以列表第一个用户为准
    let currentUserIndexInStillLearningList = -1
    members.forEach((member, index) => {
      const studyStats = this.memberStatsMap()?.[member.userId]
      const isExited = studyStats?.isExited
      const isCurrentUser = member.userId === this.currentUserInfo()?.userId || member.isCurrentUser

      if (isExited) {
        exitedList.push(member)
      } else {
        stillLearningList.push(member)
        if (isCurrentUser) {
          currentUserIndexInStillLearningList = stillLearningList.length - 1
        }
      }
    })

    const [membersForDisplay] = reArrangeList(stillLearningList, {
      targetItemIndex: currentUserIndexInStillLearningList,
      itemsPerRow: this.maxItemsPerRow(),
      offset: exitedList.length,
    })

    membersForDisplay.push(...exitedList)

    this.membersForDisplay.set(membersForDisplay)
  }
}
