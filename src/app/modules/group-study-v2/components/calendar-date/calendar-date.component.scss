:host {
  --date-theme: 130, 194, 171;
  --date-alpha: 1;

  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 62px;
  border-radius: 4px;
  background-color: rgba(var(--date-theme), 0.1);
  padding: 8px 6px;
  content-visibility: auto;
}

.member-status-container {
  width: 100%;
}

// 不用 outline 是因为 safari 16.4 之前的版本里 outline 的 radius 不跟随 border-radius
:host.active-date::after {
  --border-width: 1px;
  content: '';
  width: calc(100% - (var(--border-width) * 2));
  height: calc(100% - (var(--border-width) * 2));
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 4px;
  border: var(--border-width) solid rgb(var(--date-theme));
}

:host.disabled {
  background-color: var(--title-font-color) !important;

  & > * {
    opacity: var(--date-alpha);
  }
}

.grid-layout {
  margin-top: 8px;
  display: grid;
  grid-template-columns: var(--grid-columns);
  row-gap: var(--grid-gap);
  column-gap: var(--grid-gap);
  grid-auto-flow: row;

  & > * {
    min-width: 10px;
    max-width: 18px;
  }
}

header {
  position: relative;
  margin: 0;
  font-size: 11px;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 18px;
}

memo-badge {
  --badge-size: 24px;
  --badge-color: var(--date-theme);
  position: absolute;
  right: 0;
  top: 50%;
  transform: scale(0.5) translate(50%, -105%);
  transform-origin: center center;
}

h4 {
  margin: 0;
}
