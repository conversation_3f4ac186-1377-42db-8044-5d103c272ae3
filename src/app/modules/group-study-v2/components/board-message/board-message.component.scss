:host {
  width: 100%;
  display: flex;
  padding: 7px 0;
}

.message-info {
  flex: 1;
  padding-left: 10px;
}

.secondary-text {
  margin-left: 1em;
  font-size: 13px;
  color: var(--second-font-color);
}

.info-top {
  justify-content: space-between;

  & > :first-child {
    flex: 1;
    min-width: 0;
    font-size: 15px;
    font-weight: bold;
  }
}

.message-body {
  margin-top: 7px;
  padding: 10px;
  border-radius: 6px;
  background-color: var(--title-font-color);
}

.msg-content {
  margin: 0;
  padding: 0;
  width: fit-content;
  font-size: 14px;
}

.info-top {
  display: flex;
  align-items: center;
}

.likes-container {
  display: flex;
  align-items: flex-start;
  margin-top: 4px;
  border-radius: 15px;
  padding: 7px;
  background-color: var(--button-ai-bg);
  border-radius: 15px;
}

.divider {
  height: 15px;
  width: 1px;
  margin: 0 5px;
  background-color: var(--disabled-color);
  transform: translateY(5px);
}

.like-by-members {
  flex: 1;
  position: relative;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(22px, 1fr));
  column-gap: 3px;
  row-gap: 5px;
  padding-left: 5px;
}

.like-icon {
  --size: 25px;

  display: inline-block;
  position: relative;
  width: var(--size);
  height: var(--size);
  // translate 3%是因为点赞的图标为矩形，解决居中显示的问题
  transform: scale(2) translateY(-3%);
  border-radius: var(--size);
  vertical-align: middle;
}
