<app-member-avatar
  memoClick
  [imgSrc]="message().speaker.avatar"
  [size]="40"
  (memoDbclick)="onDbClickMember($event)"
></app-member-avatar>
<div class="message-info">
  <div class="info-top">
    <span class="truncate">{{ message().title }}</span>
    <span class="secondary-text">{{ message().createdTime | date: 'MM.dd HH:mm' : 'UTC+8' }}</span>
  </div>
  <div class="message-body">
    <p class="msg-content" [innerHTML]="message().content"></p>
    <div class="likes-container" [style.width]="likedByMembers().length === 0 && 'fit-content'">
      <div class="like-icon" #likeEle (memoClick)="thumbsUp()"></div>
      @if (likedByMembers().length) {
        <div class="divider"></div>
        <div class="like-by-members">
          @for (item of likedByMembers(); track item.userId) {
            <app-member-avatar [imgSrc]="item.avatar" shape="round" [size]="18"></app-member-avatar>
          }
        </div>
      }
    </div>
  </div>
</div>
