import { ChangeDetectionStrategy, Component, effect, ElementRef, input, output, viewChild } from '@angular/core'
import Lot<PERSON>, { AnimationItem } from 'lottie-web'
import { likeAnimation } from '../../../../../assets/animations/likeAnimation'
import { Member } from '../../models/member.model'
import { Message } from '../../models/message.model'
import { Speaker } from '../../models/speaker.model'

@Component({
  selector: 'app-board-message',
  templateUrl: './board-message.component.html',
  styleUrls: ['./board-message.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class BoardMessageComponent {
  likeEle = viewChild<ElementRef<HTMLElement>>('likeEle')

  message = input.required<Message>()

  likedByMembers = input<Member[]>([])

  dbClickMember = output<{ element: HTMLElement; target: Speaker }>()

  like = output<Message>()

  likeAnimation?: AnimationItem

  constructor() {
    effect(() => {
      const el = this.likeEle()
      if (!el || !!this.likeAnimation) return

      this.likeAnimation = this.createAnimation(el, likeAnimation)
    })
  }

  // TODO: 待优化
  private createAnimation(dom: ElementRef, animationJSON: any): AnimationItem {
    Lottie.setQuality('high')
    const animation = Lottie.loadAnimation({
      container: dom.nativeElement,
      renderer: 'svg',
      loop: false,
      autoplay: false,
      animationData: animationJSON,
    })
    return animation
  }

  onDbClickMember(element: HTMLElement) {
    this.dbClickMember.emit({ element, target: this.message().speaker })
  }

  thumbsUp() {
    this.like.emit(this.message())
  }
}
