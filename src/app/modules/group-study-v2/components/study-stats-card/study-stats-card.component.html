@if (!blank()) {
  <app-card
    class="study-card"
    [showInteractiveButton]="!!activityUIConfig()?.showInteractiveButton"
    [hasUnreadMessage]="!!activityUIConfig()?.hasUnreadMessage"
    [showSwitchGroupButton]="showSwitchGroupButton()"
    (clickFloatingButton)="onClickFloatingButton($event)"
  >
    @let dateItem = calendarDateItem()!;
    <h2>{{ dateItem.desc }}</h2>
    @if (errorMessage() === '') {
      <p class="date-desc">
        @if (dateItem.relativeIndex === 0) {
          <span>首日磨合期：</span>
        }
        <span>{{ dateItem.formattedDate }}</span>
        @if (dateItem.offsetToToday === 0) {
          <span>「今天」</span>
        }
      </p>
    }
    @if (dateItem.relativeIndex === 0 && dateItem.offsetToToday === 0) {
      <p class="study-rules">
        组内至少 2
        名成员首日完成学习并签到视为成功参与活动，否则队伍次日自动解散。若你首日并未完成学习任务，你将会被移出队伍。
        <span style="color: var(--color-forget)">组队首日，每人随机奖励 1 ~ 9600 单词上限。</span>
        <ng-content select="[extra-info-testing-day]"></ng-content>
      </p>
    }
    <div class="avatar-slides" [class.swipable]="isSwiperVisible() && isEnabledSwipe()" [ngClass]="maskClass()">
      @if (isSwiperVisible()) {
        <memo-flat-list
          #signSwiper
          class="sign-flat-list"
          [style.--embla-slide-flex]="isEnabledSwipe() ? '0 0 20%' : 1"
          [data]="membersForSignSlides()"
          [emblaOptions]="horizontalCarouselOptions()"
          [itemSize]="67"
          [itemStyle]="{
            paddingTop: 'calc(var(--gap-base) * 2)',
          }"
          [containerStyle]="{
            marginBottom: 'calc(var(--gap-base) * 2)',
          }"
          [isVirtualMode]="isVirtualMode()"
          [startIndex]="initialSlideIndex()"
          [centeredSlides]="!isEnabledSwipe()"
          (afterInit)="onFlatListInit(signSwiper)"
          (internalChange)="onFlatListChange($event, signSwiper)"
        >
          <ng-template #slide let-i="index" let-member="item">
            @if (member) {
              @if (i !== 0) {
                <memo-icon class="sign-slide-icon" name="cross" size="8px"></memo-icon>
              }
              <app-sign-avatar
                class="avatar-slide"
                [attr.user-id]="member.userId"
                [isCurrentUser]="checkIsMemberTheCurrentUser(member)"
                [studyStats]="memberStatsMap()?.[member.userId]"
                [skin]="memberStatsMap()?.[member.userId]?.skin"
                [lanternThemeEnable]="showLanternTheme()"
                (avatarClicked)="syncRippleEffect(member.userId, 'list')"
              ></app-sign-avatar>
            }
          </ng-template>
        </memo-flat-list>
      }
      @if (errorMessage() !== '') {
        <p
          class="error-msg"
          [style.padding-bottom]="isSummaryBlock() && 0"
          [style.padding-top.px]="isSummaryBlock() && 20"
        >
          {{ errorMessage() }}
          <ng-content select="[extra-info-exit-first]"></ng-content>
        </p>
      }
      @if (opHandlersTmpl(); as tmpl) {
        <ng-container [ngTemplateOutlet]="tmpl"></ng-container>
      }
    </div>
    @if (errorMessage() === '') {
      <div
        class="study-stats-list-container"
        [style.--item-count]="membersForSignSlides().length >= 3 ? 3 : membersForSignSlides().length"
      >
        <memo-flat-list
          #listSwiper
          [style.--embla-slide-flex]="membersForSignSlides().length >= 3 ? undefined : 1"
          class="list-flat-list"
          [horizontal]="false"
          [data]="membersForSignSlides()"
          [itemSize]="67"
          [startIndex]="initialSlideIndex()"
          [isVirtualMode]="isVirtualMode()"
          [emblaOptions]="verticalCarouselOptions()"
          (afterInit)="onFlatListInit(listSwiper)"
        >
          <ng-template #slide let-i="index" let-member="item">
            @if (member) {
              @if (isSummaryBlock()) {
                <app-study-stats-list-item
                  memoRipple
                  [memberInfo]="member"
                  [studyReport]="memberReportsMap()?.[member.userId]"
                  [studyBadgeText]="studyBadgeText()"
                  [showRewardBadge]="true"
                  [isCurrentUser]="checkIsMemberTheCurrentUser(member)"
                  [showStudyBadge]="!!unluckyMembers()?.includes(member.userId)"
                  [lanternThemeEnable]="showLanternTheme()"
                  (dbclickAvatar)="onDbclickAvatar($event, member)"
                ></app-study-stats-list-item>
              } @else {
                @let studyStats = memberStatsMap()?.[member.userId];
                <app-study-stats-list-item
                  memoRipple
                  [memberInfo]="member"
                  [studyStats]="studyStats"
                  [showRewardBadge]="isAlreadyHasResult(member)"
                  [lanternThemeEnable]="showLanternTheme()"
                  [requiredDuration]="activityConfig()?.dailyStudyDuration || 0"
                  [requiredVocCounts]="activityConfig()?.dailyStudyVoc || 0"
                  [studyBadgeText]="studyBadgeText()"
                  [isCurrentUser]="checkIsMemberTheCurrentUser(member)"
                  [showRepairButton]="
                    shouldCheckFinishStatus() && checkIsMemberTheCurrentUser(member) && !studyStats?.isFinished
                  "
                  (clickRepair)="clickRepair.emit(member)"
                  (dbclickAvatar)="onDbclickAvatar($event, member)"
                  (rippleStart)="syncRippleEffect(member.userId, 'sign')"
                ></app-study-stats-list-item>
              }
            }
          </ng-template>
        </memo-flat-list>
      </div>
    }
    <memo-button class="view-all-btn" skin="light" (memoClick)="onClickCheckMembers()">
      <span>全员概况</span>
      <memo-icon name="arrow" size="0.8em"></memo-icon>
    </memo-button>
  </app-card>
} @else {
  <app-card class="study-card">
    <h2>
      <ion-skeleton-text [animated]="true" style="width: 8rem; margin: 0 auto"></ion-skeleton-text>
    </h2>
    <p class="date-desc">
      <ion-skeleton-text [animated]="true" style="width: 8rem; margin: 0 auto"></ion-skeleton-text>
    </p>
    <div class="avatar-slides swiper-skeleton skeleton-flex" style="display: flex; align-items: center">
      <ng-container *rxFor="let item of blankList">
        <div>
          <ion-thumbnail>
            <ion-skeleton-text [animated]="true"></ion-skeleton-text>
          </ion-thumbnail>
          <ion-skeleton-text [animated]="true"></ion-skeleton-text>
        </div>
      </ng-container>
    </div>
    <div class="study-stats-list-container">
      @for (item of blankListThree; track $index) {
        <ion-item>
          <ion-thumbnail slot="start">
            <ion-skeleton-text [animated]="true"></ion-skeleton-text>
          </ion-thumbnail>
          <ion-label>
            <ion-skeleton-text [animated]="true" style="width: 5em"></ion-skeleton-text>
            <div class="skeleton-flex">
              <ion-skeleton-text [animated]="true" style="width: 4em"></ion-skeleton-text>
              <ion-skeleton-text [animated]="true" style="width: 4em"></ion-skeleton-text>
              <ion-skeleton-text [animated]="true" style="width: 4em"></ion-skeleton-text>
            </div>
          </ion-label>
        </ion-item>
      }
    </div>
    <ion-skeleton-text
      class="view-all-btn"
      [animated]="true"
      style="width: 5em; line-height: 1em; border-radius: 0; margin-left: auto; margin-right: auto"
    ></ion-skeleton-text>
  </app-card>
}
