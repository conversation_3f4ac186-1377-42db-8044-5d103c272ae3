import { ChangeDetectionStrategy, Component, TemplateRef, computed, contentChild, effect, input, output, signal, viewChild, viewChildren } from '@angular/core'
import { Subject, bufferCount, take } from 'rxjs'
import { EmblaOptionsType } from 'embla-carousel-angular'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { DestroyService } from '../../../core/services/destroy.service'
import { UserService } from '../../../core/services/user.service'
import { createEmptyArray } from '../../../shared/helper/utils'
import { MemoRippleDirective } from '../../../ui/utils/memo-ripple.directive'
import { CustomGroupStudyActivityConfig } from '../../models/activity.model'
import { Member } from '../../models/member.model'
import { reArrangeList } from '../../pages/study-detail/helper'
import { MemberDailyStudyStats, MemberStudyStats } from '../../pages/study-report/type'
import { ActivityUIConfig } from '../../services/group-study-storage.service'
import { SignAvatarComponent } from '../sign-avatar/sign-avatar.component'
import { CalendarDateItem } from '../../utils/study'
import { FlatListComponent } from '../../../ui/flat-list/flat-list.component'
import { dayjsWithTZ, toStudyDate } from '../../../shared/helper/time'

@Component({
  selector: 'app-study-stats-card',
  templateUrl: './study-stats-card.component.html',
  styleUrls: ['./study-stats-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DestroyService],
  standalone: false,
})
export class StudyStatsCardComponent {
  opHandlersTmpl = contentChild<TemplateRef<HTMLElement>>('opHandlers')

  signSwiperComp = viewChild<FlatListComponent>('signSwiper')
  listSwiperComp = viewChild<FlatListComponent>('listSwiper')

  private signAvatars = viewChildren(SignAvatarComponent)

  private statsList = viewChildren(MemoRippleDirective)

  studyBadgeText = input('非酋奖')

  blank = input(false)

  calendarDateItem = input<CalendarDateItem>()

  members = input<Member[] | undefined>(undefined)

  unluckyMembers = input<number[]>()

  memberStatsMap = input<Record<number, MemberDailyStudyStats>>()

  memberReportsMap = input<Record<number, MemberStudyStats>>()

  activityConfig = input<CustomGroupStudyActivityConfig>()

  activityUIConfig = input<ActivityUIConfig>()

  showSwitchGroupButton = input(false)

  showLanternTheme = input(false)

  clickHandle = output<'members' | 'interaction' | 'switch'>()

  dbClickMember = output<{ element: HTMLElement; targetMember: Member }>()

  clickRepair = output<Member>()

  isVirtualMode = computed(() => (this.members()?.length ?? 0) >= 50)

  // 30 天以内可以尝试上传备份数据以修复
  shouldCheckFinishStatus = computed(() => toStudyDate(Date.now()).diff(dayjsWithTZ(this.calendarDateItem()?.date), 'd') <= 30)

  private readonly commonCarouselOptions: EmblaOptionsType = {
    skipSnaps: true,
    containScroll: 'keepSnaps',
  }

  horizontalCarouselOptions = computed<EmblaOptionsType>(() => {
    return {
      ...this.commonCarouselOptions,
      active: this.isEnabledSwipe(),
      startIndex: this.initialSlideIndex(),
    }
  })

  verticalCarouselOptions = computed<EmblaOptionsType>(() => {
    return {
      ...this.commonCarouselOptions,
      axis: 'y',
      active: this.membersForSignSlides().length > 3,
      startIndex: this.initialSlideIndex(),
    }
  })

  readonly cssMasks = {
    left: ', transparent, #000 15%',
    right: ', #000 85%, transparent 100%',
  }

  blankList = createEmptyArray(5)
  blankListThree = createEmptyArray(3)
  private _isCurrentUserExited = signal(false)
  private _isAllMemberExited = signal(false)

  membersForSignSlides = signal<Member[]>([])
  initialSlideIndex = signal(0)
  activeSignSlideIndex = signal(0)

  syncFlatList$ = new Subject<FlatListComponent>()

  hasCurrentUser = signal(false)
  // slides 里的中位数索引，用于双击回到中心
  // 队伍中有当前用户时为当前用户的索引，否则时队伍第一名成员
  private medianIndexInSlides = -1

  maskClass = computed(() => {
    const comp = this.signSwiperComp()
    if (!comp) return ''
    const isNearStart = comp.isNearStart()
    const isNearEnd = comp.isNearEnd()

    if (!isNearStart && !isNearEnd) {
      return 'mask-both'
    }

    if (!isNearStart) {
      return 'mask-left'
    }
    if (!isNearEnd) {
      return 'mask-right'
    }

    return ''
  })

  isEnabledSwipe = computed(() => {
    const members = this.members()
    return members && members.length > 5
  })

  isCenteredLayoutSlides = computed(() => {
    const members = this.members()
    return this.isEnabledSwipe() || (members && members.length % 2 !== 0)
  })

  isSummaryBlock = computed(() => {
    return this.calendarDateItem()?.relativeIndex === -2
  })

  isSwiperVisible = computed(() => {
    return this.errorMessage() === '' && !this.opHandlersTmpl()
  })

  errorMessage = computed(() => {
    return this._isAllMemberExited()
      ? '所有人退出组队'
      : this._isCurrentUserExited()
        ? '你已退出组队'
        : ''
  })

  constructor(
    private user: UserService,
  ) {
    this.syncFlatList$.pipe(
      bufferCount(2),
      take(1),
      takeUntilDestroyed(),
    )
      .subscribe(([a, b]) => {
        a.sync(b)
        b.sync(a)
      })

    effect(() => {
      const members = this.members()
      if (members && this.memberStatsMap()) {
        this.handleMembersChanges(members)
      }
    })
  }

  onFlatListInit(instance: FlatListComponent) {
    this.syncFlatList$.next(instance)
  }

  onFlatListChange({ type }: { type: 'slidechange' }, instance: FlatListComponent) {
    if (type !== 'slidechange') return

    this.activeSignSlideIndex.set(instance.getCurrentIndex())
  }

  private handleMembersChanges(members: Member[]) {
    // 先分类在学人员和已退出人员
    const stillLearningList: Member[] = []
    const exitedList: Member[] = []

    // 最后要居中显示的人员，有当前用户的话显示当前用户，没有的话将会以列表第一个用户为准
    let currentUserIndexInStillLearningList = -1
    members.forEach((member, index) => {
      const studyStats = this.memberStatsMap()?.[member.userId]
      const isExited = studyStats?.isExited ?? (this.isSummaryBlock() && member.status === 'EXITED')
      const isCurrentUser = this.checkIsMemberTheCurrentUser(member)

      if (!this.hasCurrentUser() && isCurrentUser) {
        this.hasCurrentUser.set(true)
      }

      if (isExited) {
        exitedList.push(member)
        if (!this._isCurrentUserExited() && isCurrentUser) {
          this._isCurrentUserExited.set(true)
        }
      } else {
        stillLearningList.push(member)
        if (isCurrentUser) {
          currentUserIndexInStillLearningList = stillLearningList.length - 1
        }
      }
    })

    this._isAllMemberExited.set(members.length !== 0 && exitedList.length === members.length)

    const [membersForSlides, medianIndex] = reArrangeList(stillLearningList, {
      targetItemIndex: currentUserIndexInStillLearningList,
    })

    membersForSlides.push(...exitedList)
    this.membersForSignSlides.set(membersForSlides)
    this.activeSignSlideIndex.set(medianIndex)
    this.initialSlideIndex.set(medianIndex)
    this.medianIndexInSlides = medianIndex
  }

  checkIsMemberTheCurrentUser(member: Member) {
    return member.isCurrentUser || member.userId === this.user.userInfo?.userId
  }

  onDbclickAvatar(element: HTMLElement, member: Member) {
    const isSpecialMember = this.hasCurrentUser()
      ? this.checkIsMemberTheCurrentUser(member)
      : member.userId === this.members()![0].userId

    if (isSpecialMember && this.signSwiperComp()) {
      setTimeout(() => {
        this.signSwiperComp()?.scrollToIndex(this.medianIndexInSlides)
      })
    }

    this.dbClickMember.emit({
      element,
      targetMember: member,
    })
  }

  onClickCheckMembers() {
    this.clickHandle.emit('members')
  }

  onClickFloatingButton(buttonType: 'left' | 'right') {
    this.clickHandle.emit(buttonType === 'right' ? 'interaction' : 'switch')
  }

  isAlreadyHasResult(member: Member): boolean {
    if (this.calendarDateItem()!.offsetToToday < 0) {
      return true
    }

    const studyStats: MemberDailyStudyStats | undefined = this.memberStatsMap()?.[member.userId]

    if (this.calendarDateItem()!.offsetToToday === 0) {
      return !!studyStats?.isFinished || !!studyStats?.isExited
    }

    return !!studyStats?.isExited
  }

  syncRippleEffect(userId: number, targetType: 'sign' | 'list') {
    const targetComp = targetType === 'sign' ? this.signSwiperComp() : this.listSwiperComp()
    const targetIndex = this.membersForSignSlides().findIndex(v => v.userId === userId)

    if (this.isVirtualMode()) {
      targetComp?.scrollToIndex(targetIndex)
    }

    setTimeout(() => {
      const rippleList = targetType === 'sign' ? this.signAvatars() : this.statsList()
      const rippleRef = rippleList.reduce((result, v) => {
        if (result !== undefined) return result
        const ref = v instanceof MemoRippleDirective
          ? v
          : v.rippleRef!
        const elRef = v instanceof MemoRippleDirective ? v._elementRef : v?.elementRef
        if (elRef.nativeElement.getAttribute('user-id') === String(userId)) {
          return ref
        }
        return undefined
      }, undefined as MemoRippleDirective | undefined)

      rippleRef?.startAnimation(true)
    })
  }
}
