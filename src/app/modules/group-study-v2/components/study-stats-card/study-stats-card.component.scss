:host {
  --gap-base: 5px;

  display: block;
  width: 100%;
}

@media screen and (max-width: 425px) {
  :host {
    --gap-base: 3px;
  }
}

.avatar-slides {
  width: 100%;
  overflow-x: hidden;

  & > .sign-flat-list {
    margin: calc(var(--gap-base) * 2) 0;
  }
}

.swipable.mask-left {
  mask: linear-gradient(90deg, transparent, #000 15%);
}

.swipable.mask-right {
  mask: linear-gradient(90deg, #000 85%, transparent 100%);
}

.swipable.mask-both {
  mask: linear-gradient(90deg, transparent, #000 15%, #000 85%, transparent 100%);
}

.sign-slide-icon {
  --avatar-size-half: 20px;

  position: absolute;
  left: 0;
  top: calc(var(--gap-base) * 2 + var(--avatar-size-half));
  transform: translate(-50%, -50%);
  color: var(--border-color);
}

app-card {
  padding: calc(var(--gap-base) * 3);
}

p,
h2,
ul {
  margin: 0;
}

ul {
  padding: 0;
}

h2 {
  font-weight: bold;
  font-size: 18px;
  text-align: center;
}

.date-desc,
.study-rules {
  color: var(--second-font-color);
}

.date-desc {
  margin-top: calc(var(--gap-base) * 2);
  font-size: 11px;
  text-align: center;
  line-height: 1rem;
}

.study-rules {
  margin-top: calc(var(--gap-base) * 3);
  font-size: 12px;
}

.study-stats-list-container {
  --item-height: 60px;
  --item-count: 3;
  --list-container-height: calc((var(--item-height) + 4px) * var(--item-count));

  margin-top: calc(var(--gap-base) * 3);
  height: var(--list-container-height);
  overflow: hidden;
}

.swiper-vertical {
  height: 100%;
}

.view-all-btn {
  margin-top: calc(var(--gap-base) * 3);
  font-size: 12px;
  min-width: 5em;
  padding: 5px 6px;
  line-height: 1.5;

  & > :first-child {
    color: var(--second-font-color);
  }

  memo-icon {
    margin-left: 0.3em;
    color: var(--default-font-color);
    transform: rotateZ(-90deg);
    transform-origin: 50% 50%;
  }
}

.error-msg {
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: var(--red-bgColor);
}

.skeleton-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.swiper-skeleton {
  margin-top: calc(var(--gap-base) * 4);

  ion-thumbnail > ion-skeleton-text {
    --border-radius: 50%;
  }

  ion-thumbnail + ion-skeleton-text {
    margin-top: 11px;
  }
}

ion-item {
  --min-height: calc(var(--item-height) + 4px);
  --padding-start: 8px;
  --padding-end: 8px;

  ion-thumbnail {
    --size: 44px;
    & > ion-skeleton-text {
      --border-radius: 5px;
    }
  }
}

.list-flat-list {
  --embla-container-height: var(--list-container-height);
  --embla-slides-gap: 4px;
  --embla-slide-size: var(--item-height);
}
