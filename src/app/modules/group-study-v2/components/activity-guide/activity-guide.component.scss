:host {
  --gap-base: 5px;

  display: flex;
  flex-direction: column;

  & > :not(:last-child) {
    margin-bottom: calc(var(--gap-base) * 3);
  }
}

h3 {
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  margin-bottom: var(--gap-base);
  font-size: 15px;
  font-weight: 500;
  color: var(--theme-green);

  &::before {
    content: "";
    width: 6px;
    height: 6px;
    margin-right: 8px;
    border-radius: 50%;
    aspect-ratio: 1 / 1;
    background-color: var(--theme-green);
  }
}

p {
  margin: 0;
  padding: 0;
  font-size: 13px;
  line-height: 1.75;
}

.bold {
  font-weight: bold;
}
