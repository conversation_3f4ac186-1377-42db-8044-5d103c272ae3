import { ChangeDetectionStrategy, Component, HostBinding, Input, computed, input } from '@angular/core'
import { Member } from '../../models/member.model'

@Component({
  selector: 'app-member-avatar',
  templateUrl: './member-avatar.component.html',
  styleUrls: ['./member-avatar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class MemberAvatarComponent {
  private readonly defaultAvatarSrc = 'assets/images/default-avatar.png'

  member = input<Member>()

  imgSrc = input<string>()

  @HostBinding('style.--size.px')
  @Input() size = 50

  shape = input<'square' | 'round'>('square')

  @HostBinding('class.round-avatar')
  get isRoundShape(): boolean {
    return this.shape() === 'round'
  }

  memberAvatarSrc = computed(() => {
    const inputImgSrc = this.imgSrc() ?? this.member()?.avatar ?? ''
    return inputImgSrc === '' ? this.defaultAvatarSrc : inputImgSrc
  })
}
