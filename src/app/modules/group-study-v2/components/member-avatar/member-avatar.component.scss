:host {
  --border-size: 2px;
  --size: 50px;
  --color: var(--border-color);

  box-sizing: content-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: var(--border-size) solid var(--color);
  border-radius: 6px;
  overflow: hidden;
  width: var(--size);
  height: var(--size);
  min-width: var(--size);
  color: var(--color);
}

:host.round-avatar {
  border-radius: 50%;
}

img {
  width: 100%;
  height: 100%;
}

:root.dark :host {
  --color: var(--gray-level-140);
}
