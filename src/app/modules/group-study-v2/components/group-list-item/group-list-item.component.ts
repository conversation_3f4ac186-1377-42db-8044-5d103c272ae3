import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core'
import { fromSeconds } from '../../../shared/helper/time'
import { GroupWithConfig } from '../../models/group.model'
import { wrapLinkToExternal } from '../../../shared/helper/utils'

@Component({
  selector: 'app-group-list-item',
  templateUrl: './group-list-item.component.html',
  styleUrls: ['./group-list-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class GroupListItemComponent {
  readonly group = input<GroupWithConfig>()
  readonly cancelable = input(false)
  readonly maxMembersPerRow = input(0)
  readonly memberSize = input(34)
  readonly withBannerViewTransition = input(false)

  readonly recall = output()

  isCommercialGroup = computed(() => !!this.commercialBanner())
  commercialBanner = computed(() => this.group()?.promotion?.banners?.[0] || '')
  commercialBannerLink = computed(() => wrapLinkToExternal(this.group()?.promotion?.link))

  groupConditions = computed(() => {
    const group = this.group()
    const members = this.actualMembers()
    if (!group) {
      return []
    }
    const { config } = group

    const membersInfo = `${members.length}/${config.groupSize} 人`
    const studyDuration = `${fromSeconds(config.studyDuration + config.testingDuration, 'day')} 天`
    const dailyVoc = `${config.dailyStudyVoc} 单词`
    const dailyStudyDurationMin = `${fromSeconds(config.dailyStudyDuration, 'min')} 分钟`

    return [membersInfo, studyDuration, dailyVoc, dailyStudyDurationMin]
  })

  actualMembers = computed(() => {
    const group = this.group()
    if (!group || (this.maxMembersPerRow() === undefined)) {
      return []
    }
    const { config, members = [], creator } = group
    const filteredMembers = config && config.creatorAutoJoin === false
      ? members.filter(v => v.userId !== creator)
      : members

    return filteredMembers
  })

  isPayWithCreator = computed((): boolean => {
    const group = this.group()
    return !!group && group.config.paymentType === 'CREATOR'
  })

  isJoinable = computed((): boolean => {
    return this.group()?.status === 'GROUPING'
  })

  isMembersCollapsed = computed((): boolean => {
    return this.actualMembers.length > (this.maxMembersPerRow() - (this.isJoinable() ? 1 : 0))
  })

  maxMembers = computed((): number => {
    const maxMembersPerRow = this.maxMembersPerRow()
    if (maxMembersPerRow === 0) {
      return 0
    }

    const joinCount = this.isJoinable() ? 1 : 0
    const ellipsisCount = this.isMembersCollapsed() ? 1 : 0
    return maxMembersPerRow - joinCount - ellipsisCount
  })
}
