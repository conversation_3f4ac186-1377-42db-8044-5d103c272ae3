:host {
  --group-item-padding: 18px;
  --members-gap: 5px;

  display: block;
  width: 100%;
  background-color: var(--bg-color-2);
  border-radius: 10px;
  overflow: hidden;
}

.card-content {
  padding: 14px var(--group-item-padding);
}

.card-content > :not(:last-child) {
  margin-bottom: calc(var(--gap-base) * 2);
}

:root.dark :host {
  --border-color: var(--gray-level-140);
}

header {
  height: 25px;
  justify-content: space-between;
}

header,
.group-info,
.members-container {
  display: flex;
  align-items: center;
}

.base-tag {
  --tag-color: 54, 181, 157;

  min-width: fit-content;
  width: max-content;
  font-weight: 500;
  padding: 3px 8px;
  border-radius: 5px;
  font-size: 13px;
  color: rgb(var(--tag-color));
  background-color: rgba(var(--tag-color), 0.1);
}

.base-tag.btn-recall {
  --tag-color: 220, 102, 62;
  font-size: 12px;
}

.commercial-tag {
  --tag-color: 225, 96, 77;
  font-size: 12px;
  margin: 0 0.3em;
}

.group-name {
  font-weight: bold;
  margin: 0;
}

.group-info {
  height: 20px;
}

.group-conditions {
  min-width: 0;
  flex: 1;
  font-size: 13px;

  .divider {
    margin: 0 0.4em;
    color: var(--border-color);
  }
}

.members-container {
  height: 34px;
  flex-wrap: wrap;
  overflow: hidden;

  & > :not(:last-child) {
    margin-right: var(--members-gap);
  }
}

.ellipsis-member {
  --size: 34px;
  width: var(--size);
  height: var(--size);
  display: flex;
  align-items: center;
  justify-content: center;
}

ion-skeleton-text {
  line-height: 1em;
}
