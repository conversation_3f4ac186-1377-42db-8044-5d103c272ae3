@if (group(); as groupItem) {
  @if (isCommercialGroup()) {
    <memo-commercial-banner
      [src]="commercialBanner()"
      [withViewTransition]="withBannerViewTransition()"
    ></memo-commercial-banner>
  }
  <div class="card-content">
    <header>
      <div class="flex flex-1 min-w-0">
        <p class="group-name truncate">{{ groupItem.name }}</p>
        @if (isCommercialGroup()) {
          <div class="base-tag commercial-tag">赞助 | 免费组队</div>
        }
      </div>
      @if (cancelable() && !isCommercialGroup()) {
        <div>
          <button class="base-tag btn-recall" (memoClick)="$event.stopPropagation(); recall.emit()">撤回发布</button>
        </div>
      }
    </header>
    <div class="group-info">
      <div class="group-conditions">
        @for (item of groupConditions(); track item; let i = $index) {
          <span>{{ item }}</span>
          @if (i !== groupConditions().length - 1) {
            <span class="divider">|</span>
          }
        }
      </div>
    </div>
    <div class="members-container">
      @for (member of actualMembers(); track member.userId; let i = $index) {
        @if (i < maxMembers()) {
          <app-member-avatar [size]="memberSize()" [member]="member"></app-member-avatar>
        }
      }
      @if (isMembersCollapsed()) {
        <ng-container [ngTemplateOutlet]="ellipsisMember"></ng-container>
      }
      @if (isJoinable()) {
        <app-member-avatar [size]="memberSize()" imgSrc="plus"></app-member-avatar>
      }
    </div>
  </div>
} @else {
  <div class="card-content">
    <header>
      <p class="group-name">
        <ion-skeleton-text animated [style.width.em]="6"></ion-skeleton-text>
      </p>
    </header>
    <div class="group-info">
      <ion-skeleton-text animated [style.width.em]="10"></ion-skeleton-text>
    </div>
    <div class="members-container">
      <ion-skeleton-text animated [style.width]="'100%'"></ion-skeleton-text>
    </div>
  </div>
}

<ng-template #ellipsisMember>
  <div class="ellipsis-member" [style.--size.px]="memberSize()">
    <svg width="18" height="4" viewBox="0 0 18 4" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_604_27)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M2 4C3.1 4 4 3.1 4 2C4 0.9 3.1 0 2 0C0.9 0 0 0.9 0 2C0 3.1 0.9 4 2 4Z"
          fill="var(--border-color)"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M9 4C10.1 4 11 3.1 11 2C11 0.9 10.1 0 9 0C7.9 0 7 0.9 7 2C7 3.1 7.9 4 9 4Z"
          fill="var(--border-color)"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M16 4C17.1 4 18 3.1 18 2C18 0.9 17.1 0 16 0C14.9 0 14 0.9 14 2C14 3.1 14.9 4 16 4Z"
          fill="var(--border-color)"
        />
      </g>
      <defs>
        <clipPath id="clip0_604_27">
          <rect width="18" height="4" fill="var(--border-color)" />
        </clipPath>
      </defs>
    </svg>
  </div>
</ng-template>
