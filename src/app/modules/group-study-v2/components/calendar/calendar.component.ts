import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostBinding, computed, effect, input, output, signal } from '@angular/core'
import { Platform } from '@ionic/angular'
import { Subject, debounceTime, merge, takeUntil } from 'rxjs'
import { DestroyService } from '../../../core/services/destroy.service'
import { trackBy } from '../../../shared/helper/template'
import { createEmptyArray } from '../../../shared/helper/utils'
import { Member, UserBase } from '../../models/member.model'
import { MemberDailyStudyStats, MemberStudyReport, MemberStudyStats } from '../../pages/study-report/type'
import { CalendarDateItem } from '../../utils/study'

@Component({
  selector: 'app-calendar',
  templateUrl: './calendar.component.html',
  styleUrls: ['./calendar.component.scss'],
  host: {
    '[style.--grid-gap.px]': 'gridGapConfig()',
    '[style.--grid-columns]': 'gridColumnsConfig()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DestroyService],
  standalone: false,
})
export class CalendarComponent {
  readyToShow = input(false)

  calendars = input<CalendarDateItem[]>([])

  members = input<Member[]>()

  activeDateIndex = input(0)

  studyStatsMap = input<Record<string, Record<number, MemberDailyStudyStats>>>({})

  currentUserInfo = input<UserBase>()

  isCurrentUserInGroup = input(false)

  currentUserStudySumStats = input<MemberStudyStats>()

  currentUserStudyReport = input<MemberStudyReport>()

  isCurrentUserTheCreator = input(false)

  clickCalendarDate = output<[number, CalendarDateItem | undefined]>()

  private readonly MAX_CALENDAR_ITEM_SIZE = 15
  private readonly MIN_CALENDAR_ITEM_SIZE = 10
  private readonly MAX_GRID_COLUMN_GAP = 5
  private readonly MIN_GRID_COLUMN_GAP = 2

  isInitialized = computed(() => this.calendars().length > 0)
  blankList = createEmptyArray(9)

  maxDateItemPerRow = signal(-1)
  startColOfSummaryItem = computed(() => {
    const calendars = this.calendars()
    if (calendars.length === 0) return 0
    const summaryBlockIndex = calendars.length + 1
    const remainder = summaryBlockIndex % 3
    return remainder === 0 ? 3 : remainder
  })

  private checkLayout$ = new Subject<void>()

  gridGapConfig = signal(this.MAX_GRID_COLUMN_GAP)

  gridColumnsConfig = signal('repeat(auto-fill, minmax(15px, 1fr))')

  itemPaddingInline = 6

  @HostBinding('style.--calendar-items-gap-column.px')
  itemColumnGap = 7

  constructor(
    private elementRef: ElementRef<HTMLElement>,
    private cdr: ChangeDetectorRef,
    private platform: Platform,
    private destroy$: DestroyService,
  ) {
    merge(
      this.checkLayout$,
      this.platform.resize,
    )
      .pipe(
        debounceTime(200),
        takeUntil(this.destroy$),
      )
      .subscribe(() => {
        this.calculateGridLayout()
        this.cdr.markForCheck()
      })

    effect(() => {
      const members = this.members()
      if (members && members.length > 0) {
        this.checkLayout$.next()
      }
    })
  }

  private calculateGridLayout() {
    if (!this.elementRef) {
      return
    }
    const { nativeElement } = this.elementRef
    const { length } = this.members() || []
    const containerWidth = nativeElement.clientWidth
    const itemsColumnGap = this.itemColumnGap * 2
    const calendarItemWidth = ((containerWidth - itemsColumnGap) / 3) - (this.itemPaddingInline * 2)

    const itemCountPerRowForMaxWidth = Math.floor((calendarItemWidth / (this.MAX_CALENDAR_ITEM_SIZE + this.MAX_GRID_COLUMN_GAP)))
    if (itemCountPerRowForMaxWidth > length) {
      this.gridGapConfig.set(this.MAX_GRID_COLUMN_GAP)
      this.gridColumnsConfig.set(`repeat(auto-fill, minmax(${this.MAX_CALENDAR_ITEM_SIZE}px, 1fr))`)
      this.maxDateItemPerRow.set(4)
      return
    }

    const gap = Math.min(this.MAX_GRID_COLUMN_GAP, Math.max(this.MIN_GRID_COLUMN_GAP, length % this.MIN_GRID_COLUMN_GAP))
    const itemCountPerRow = Math.floor((calendarItemWidth / (this.MIN_CALENDAR_ITEM_SIZE + gap)))

    // 不满一行的情况
    if (itemCountPerRow > length) {
      const delta = itemCountPerRow - length
      // 差一两个满一行就撑满一行
      if (delta <= 2) {
        this.gridGapConfig.set(this.MAX_GRID_COLUMN_GAP - 2)
        this.gridColumnsConfig.set(`repeat(auto-fit, minmax(${this.MIN_CALENDAR_ITEM_SIZE}px, 1fr))`)
        this.maxDateItemPerRow.set(length)
        return
      }

      // 只有半行左右的话则用 auto-fill
      const half = itemCountPerRow / 2
      const offset = this.platform.is('tablet') ? 4 : 2
      if (Math.abs(half - delta) <= offset) {
        this.gridGapConfig.set(this.MAX_GRID_COLUMN_GAP - 1)
        this.gridColumnsConfig.set(`repeat(auto-fill, minmax(${this.MAX_CALENDAR_ITEM_SIZE - 1}px, 1fr))`)
        this.maxDateItemPerRow.set(length)
        return
      }
    }

    this.gridGapConfig.set(gap)
    this.gridColumnsConfig.set(`repeat(${itemCountPerRow}, 1fr)`)
    this.maxDateItemPerRow.set(itemCountPerRow)
  }

  trackByCalendarDate = trackBy('date')

  onClickCalendarDate(index: number, item?: CalendarDateItem) {
    this.clickCalendarDate.emit([index, item])
  }
}
