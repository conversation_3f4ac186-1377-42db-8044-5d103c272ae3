@if (readyToShow() && isInitialized() && maxDateItemPerRow() !== -1) {
  <ng-container
    *rxFor="
      let item of calendars();
      index as i;
      trackBy: trackByCalendarDate;
      strategy: calendars().length <= 7 ? 'immediate' : 'normal'
    "
  >
    <app-calendar-date
      [isActive]="i === activeDateIndex()"
      [isDisabled]="item.offsetToToday > 0"
      [members]="members()"
      [memberStatsMap]="studyStatsMap()[item.formattedDate] || {}"
      [calendarDateItem]="item"
      [maxItemsPerRow]="maxDateItemPerRow()"
      [isThemable]="!(isCurrentUserTheCreator() && !isCurrentUserInGroup())"
      [currentUserStudyStats]="
        currentUserInfo() && (studyStatsMap()[item.formattedDate] || {})[currentUserInfo()!.userId]
      "
      [currentUserInfo]="currentUserInfo()"
      (click)="onClickCalendarDate(i, item)"
    >
    </app-calendar-date>
  </ng-container>
} @else {
  @for (item of blankList; track $index) {
    <app-calendar-date [blank]="true"></app-calendar-date>
  }
}

@if (isInitialized()) {
  <app-calendar-date
    [dateTitle]="'累计'"
    [isActive]="activeDateIndex() === -2"
    [style.grid-column]="startColOfSummaryItem() + ' / 4'"
    [currentUserInfo]="currentUserInfo()"
    (click)="onClickCalendarDate(-2)"
  >
    <ng-template #calendarContent>
      <p class="calendar-summary">
        <span>获得上限</span>
        <span>{{ currentUserStudySumStats()?.totalRewarded || 0 }}</span>
      </p>
      @if (currentUserStudyReport(); as report) {
        <p class="calendar-summary">
          <span>排名</span>
          <span>{{ report.rank }}</span>
        </p>
      }
    </ng-template>
  </app-calendar-date>
}
