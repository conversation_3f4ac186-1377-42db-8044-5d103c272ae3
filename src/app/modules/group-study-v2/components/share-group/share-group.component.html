@for (section of sections; track section.title) {
  <section>
    <header>
      <h3>{{ section.title }}</h3>
      @if (section.desc) {
        <div class="section-desc">{{ section.desc }}</div>
      }
    </header>
    <div class="share-grid">
      @for (item of section.children; track item.label) {
        <button class="share-item" (click)="onClickItem(item)">
          <img width="36" height="36" [src]="ASSETS_PATH + item.image + '.png'" alt="" />
          <span>{{ item.label }}</span>
        </button>
      }
    </div>
  </section>
}
