import { DOCUMENT } from '@angular/common'
import { ChangeDetectionStrategy, Component, inject, Input } from '@angular/core'
import { GroupStudyClientService } from '../../../group-study/service/services/group-study-client.service'
import { SocialMediaName } from '../../../shared/helper/social'
import { MemoModalService } from '../../../ui/modal/modal.service'

type ShareSection = {
  title: string
  desc?: string
  children: ShareButton[]
}

type ShareButton = {
  label: string
  image: string
  handler: () => Promise<unknown>
}

@Component({
  selector: 'app-share-group',
  templateUrl: './share-group.component.html',
  styleUrls: ['./share-group.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
})
export class ShareGroupComponent {
  @Input()
  shareText = ''

  readonly ASSETS_PATH = 'assets/images/social-media/'

  private readonly specialPlatforms: Record<SocialMediaName, { label: string; link: string }> = {
    xiaohongshu: {
      label: '小红书',
      link: 'https://www.xiaohongshu.com/page/topics/628ef564c0892d00016f697c',
    },
    weibo: {
      label: '新浪微博',
      link: 'https://weibo.com/p/100808c5dde57b800f5b1fd0c3057fa51a12e7',
    },
  }

  sections: ShareSection[] = [
    {
      title: '口令分享',
      children: [
        // {
        //   label: '微信好友',
        //   image: 'wechat',
        //   handler: () => this.shareToSocialMedia('wechat'),
        // },
        {
          label: '朋友圈',
          image: 'wechat_moment',
          handler: () => this.shareToSocialMedia('wechat_timeline'),
        },
        {
          label: 'QQ',
          image: 'qq',
          handler: () => this.shareToSocialMedia('qq'),
        },
        {
          label: '复制口令',
          image: 'link',
          handler: () => {
            return this.client.cfc.clientCopy(this.shareText || '未找到口令', '复制成功')
          },
        },
      ],
    },
    {
      title: '快速找人',
      desc: '结交新的小伙伴',
      children: [
        {
          label: '小红书',
          image: 'xiaohongshu',
          handler: () => this.shareToSocialMedia('xiaohongshu'),
        },
        {
          label: '新浪微博',
          image: 'weibo',
          handler: () => this.shareToSocialMedia('weibo'),
        },
      ],
    },
  ]

  private _doc = inject(DOCUMENT)
  private modal = inject(MemoModalService)
  private client = inject(GroupStudyClientService)
  private _win = this._doc.defaultView || window

  onClickItem(item: ShareButton) {
    item.handler()
      .then(() => this.modal.dismiss())
  }

  private async goToExternalLinks(to: keyof typeof this.specialPlatforms) {
    const { link } = this.specialPlatforms[to]
    this._win.open(link + '?__open_mode=1')
  }

  private async shareToSocialMedia(channel: 'wechat' | 'wechat_timeline' | 'qq' | SocialMediaName): Promise<unknown> {
    if (channel === 'xiaohongshu' || channel === 'weibo') {
      this.goToExternalLinks(channel)
      return
    }

    return this.client.cfc.share({
      channel,
      type: 'text',
      scene: '',
      share_content: {
        link: this.shareText,
      },
      callback_listener: () => {
        //
      },
    })
  }
}
