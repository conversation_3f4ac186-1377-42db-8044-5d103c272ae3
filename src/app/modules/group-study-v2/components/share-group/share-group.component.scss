:host {
  display: block;
  padding: 15px;
  color: #5d5d5d;
}

section {
  margin-bottom: 28px;
}

// https://github.com/angular/angular/issues/32811
.section-desc {
  padding: 1px 6px 2px 12px;
  margin-left: -5px;
  font-size: 12px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0.85);
  transform-origin: 50% 50%;
  background: url("^assets/images/bubble_left.png") no-repeat;
  background-size: 100% 100%;
  max-height: 100%;
}

header {
  padding: 5px 0.5rem;
  position: relative;
  display: flex;
  align-items: center;
}

header::before {
  content: "";
  position: absolute;
  width: 2px;
  height: 1em;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(54, 181, 157, 0.2);
}

h3 {
  margin: 0;
  padding: 0;
  font-size: 14px;
  font-weight: bold;
}

.share-grid {
  margin-top: 2px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}

.share-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  border: none;
  background: none;
}

.share-item:focus-visible {
  outline: none;
}

.share-item img {
  box-sizing: content-box;
  padding: 6px 8px;
}

.share-item span {
  font-size: 12px;
  color: #989898;
  line-height: 1.5em;
}
