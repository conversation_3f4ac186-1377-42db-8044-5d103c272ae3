:host {
  --member-theme: 130, 194, 171;
  --member-alpha: 0.5;

  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  background: none;
  border: 1.3px dotted rgba(170, 170, 170, 0.5);
  max-height: 18px;
  max-width: 18px;
}

:host.has-result {
  background-color: rgba(var(--member-theme), var(--member-alpha));
  border-width: 0;
}

:host.current-user {
  border: none;
  border: 1.3px solid rgba(142, 142, 142, var(--date-alpha)) !important;
}

memo-icon {
  transform: scale(0.7);
}
