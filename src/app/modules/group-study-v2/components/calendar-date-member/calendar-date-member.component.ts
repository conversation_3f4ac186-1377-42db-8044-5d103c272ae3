import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core'
import { ExColorType } from '../../../group-study/models/model'
import { getRGBAFromColorType } from '../../../shared/helper/color'
import { CalendarDateItem } from '../../utils/study'

@Component({
  selector: 'app-calendar-date-member',
  templateUrl: './calendar-date-member.component.html',
  styleUrls: ['./calendar-date-member.component.scss'],
  host: {
    class: 'aspect-square',
    '[class.current-user]': 'isCurrentUser()',
    '[class.has-result]': 'hasResult()',
    '[style.--member-theme]': 'memberTheme()',
    '[style.--member-alpha]': 'memberAlpha()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class CalendarDateMemberComponent {
  private readonly defaultSkin: ExColorType = 'grey-2'

  calendarDateItem = input.required<CalendarDateItem>()

  isFinished = input(false)

  isExited = input(false)

  themable = input(false)

  skin = input<ExColorType | undefined>(this.defaultSkin)

  isCurrentUser = input(false)

  private memberThemeRGBA = computed(() => {
    const skin = this.skin()
    if (!skin) return getRGBAFromColorType(this.defaultSkin)

    const color = !this.isCrossVisible() && this.themable()
      ? skin
      : this.defaultSkin

    return getRGBAFromColorType(color)
  })

  memberTheme = computed(() => {
    const rgb = this.memberThemeRGBA()
    return `${rgb.r}, ${rgb.g}, ${rgb.b}`
  })

  memberAlpha = computed(() => {
    const alpha = this.memberThemeRGBA().a
    return alpha === 2
      ? '0.3'
      : alpha === 3
        ? '0.7'
        : '0.5'
  })

  hasResult = computed(() => {
    if (this.calendarDateItem().offsetToToday < 0) {
      return true
    }

    return !!this.isFinished() || !!this.isExited()
  })

  isCrossVisible = computed(() => {
    const isExited = this.isExited()
    if (isExited === undefined) return false
    return !isExited && (this.hasResult() && !this.isFinished())
  })
}
