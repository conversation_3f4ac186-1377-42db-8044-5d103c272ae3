import { ChangeDetectionStrategy, Component, ElementRef, HostBinding, ViewChild, computed, inject, input, output } from '@angular/core'
import { ExColorType } from '../../../group-study/models/model'
import { getColorRGBFromColorType, getRGBAFromColorType } from '../../../shared/helper/color'
import { MemoRippleDirective } from '../../../ui/utils/memo-ripple.directive'
import { MemberDailyStudyStats } from '../../pages/study-report/type'

@Component({
  selector: 'app-sign-avatar',
  templateUrl: './sign-avatar.component.html',
  styleUrls: ['./sign-avatar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class SignAvatarComponent {
  private readonly defaultSkinColor = getColorRGBFromColorType('grey-1')

  elementRef = inject<ElementRef<HTMLElement>>(ElementRef)
  studyStats = input<MemberDailyStudyStats>()
  isCurrentUser = input(false)
  skin = input<ExColorType | undefined>('green-1')
  lanternThemeEnable = input(false)

  avatarClicked = output()

  @ViewChild(MemoRippleDirective)
  rippleRef?: MemoRippleDirective

  @HostBinding('style.--theme')
  get avatarTheme(): string {
    if (this.isFailed || !this.studyStats()?.studyLog?.rewardedAmount) {
      return this.defaultSkinColor
    }

    const { r, g, b } = this.themeRGB()
    return `${r}, ${g}, ${b}`
  }

  private themeRGB = computed(() => {
    return getRGBAFromColorType(this.skin() ?? 'grey-1')
  })

  @HostBinding('class.failed')
  get isFailed() {
    return this.isExited() || this.isNotFinished()
  }

  signAvatarSrc = computed(() => {
    const imgName = this.studyStats()?.studyLog?.signAvatarImg ?? 'question_mark.png'

    const extraSuffix = this.lanternThemeEnable() ? '-lantern' : ''

    return `assets/images/completion-status${extraSuffix}/${imgName}`
  })

  isNotFinished = computed(() => {
    return !this.studyStats()?.isFinished
  })

  statsDesc = computed(() => {
    if (this.isExited()) {
      return '已退出'
    }

    if (this.studyStats()?.isFinished) {
      return '已完成'
    }

    return '未完成'
  })

  isExited = computed(() => {
    return !!this.studyStats()?.isExited
  })
}
