<div #container class="avatar-container">
  <div
    class="avatar-modal"
    memoRipple
    [triggerElement]="elementRef?.nativeElement"
    (rippleStart)="avatarClicked.emit()"
  ></div>
  @if (!isExited()) {
    <img decoding="async" [src]="signAvatarSrc()" alt="sign avatar" />
  } @else {
    <memo-icon class="exited-img" size="1em" name="cross"></memo-icon>
  }
  <div *ngIf="isCurrentUser()" class="self-badge aspect-square" [class.failed]="isFailed">我</div>
</div>

<span class="stats-desc">{{ statsDesc() }}</span>
