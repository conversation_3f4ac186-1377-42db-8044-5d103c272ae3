:host {
  --theme: 130, 194, 171;

  display: flex;
  flex-direction: column;
  align-items: center;

  & > :not(:first-child) {
    margin-top: 11px;
  }
}

.avatar-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  border: 2px solid rgb(var(--theme));
  background-color: rgba(var(--theme), 0.1);

  img {
    width: 80%;
  }
}

.avatar-modal {
  position: absolute;
  width: 140%;
  height: 140%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  z-index: -1;
}

.exited-img {
  font-size: 14px;
  color: rgb(var(--theme));
}

.self-badge {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 60%) scale(0.8);
  font-size: 10px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2em;
  height: 2em;
  border-radius: 50%;
  color: var(--bg-color-2);
  background-color: rgb(var(--theme));
  border: 1px solid var(--bg-color-2);
}


.stats-desc {
  font-size: 13px;
  font-weight: bold;
}

:host.failed {
  .stats-desc {
    color: rgb(var(--theme));
  }
}
