<!-- eslint-disable @angular-eslint/template/conditional-complexity -->
<div class="avatar-container" memoClick (memoDbclick)="onDbclickAvatar($event)">
  <app-member-avatar [imgSrc]="memberInfo()?.avatar" [size]="44"></app-member-avatar>
  @if (showRewardBadge()) {
    <memo-badge
      class="floating-badge"
      [style.--badge-font-weight]="'bold'"
      [style.--badge-color]="
        studyReport() ? (rewardedAmount() | themeByReward: studyReport()?.totalStudyDays ?? 0) : 'var(--member-theme)'
      "
      [value]="rewardedAmount()"
      [withBorder]="true"
    ></memo-badge>
  }
</div>
<section class="study-info">
  <div class="user-info-text">
    <span class="user-name truncate">{{ memberInfo()?.name ?? '' }}</span>
    @if (studyStats()?.studyLog?.succeededTime || isExited()) {
      <span>
        {{ isExited() ? '已退出' : (studyStats()?.studyLog?.succeededTime | date: 'HH:mm' : 'UTC+8') }}
      </span>
    }
    @if (showRepairButton()) {
      <memo-badge
        style="margin-left: 0.5em"
        [style.--badge-size.px]="11"
        [style.--badge-radius.px]="4"
        [style.--badge-bg-alpha]="0.2"
        [style.--badge-color]="redBadgeTheme"
        [style.--badge-bg]="redBadgeTheme"
        [style.--padding-x.px]="6"
        [style.--padding-y.px]="4"
        [style.zoom]="0.9"
        [textContent]="'数据异常'"
        (memoClick)="$event.stopPropagation(); clickRepair.emit()"
      ></memo-badge>
    }
    @if (showStudyBadge()) {
      <memo-study-badge
        [style.zoom]="0.9"
        [content]="studyBadgeText()"
        [isCurrentUser]="isCurrentUser()"
      ></memo-study-badge>
    }
  </div>
  @if (!studyReport()) {
    <div class="study-stats stats-daily">
      <div [class.failed-state]="!studyStats()?.hasSigned">
        @if (studyStats()?.hasSigned && studyStats()?.studyLog?.signAvatar) {
          <span class="sign-avatar-container icon-stats-base aspect-square">
            <img class="aspect-square" [src]="signAvatarSrc()" alt="签到头像" />
          </span>
        } @else {
          <ng-container [ngTemplateOutlet]="notReachYetIcon"></ng-container>
        }
        <span>{{ studyStats()?.hasSigned ? '已签到' : '未签到' }}</span>
      </div>
      <div>
        <ng-container
          [ngTemplateOutlet]="
            (studyStats()?.studyLog?.studyDuration || -1) >= requiredDurationMin() ? checkedIcon : notReachYetIcon
          "
        ></ng-container>
        <span class="truncate"
          >{{ studyStats()?.studyLog?.studyDuration || 0 }}/{{ requiredDurationMin() || 0 }} 分钟</span
        >
      </div>
      <div>
        <ng-container
          [ngTemplateOutlet]="
            (studyStats()?.studyLog?.studyVocCounts || -1) >= requiredVocCounts() ? checkedIcon : notReachYetIcon
          "
        ></ng-container>
        <span class="truncate"
          >{{ studyStats()?.studyLog?.studyVocCounts || 0 }}/{{ requiredVocCounts() || 0 }} 单词</span
        >
      </div>
    </div>
  } @else {
    <div class="study-stats stats-total">
      <div>
        签到
        <span class="study-value">{{ studyReport()?.totalStudyDays }}</span>
        天
      </div>
      <span class="divider"></span>
      <div>
        <span class="study-value">{{ studyReport()?.totalStudyDuration }}</span>
        分钟
      </div>
      <span class="divider"></span>
      <div>
        学习
        <span class="study-value">{{ studyReport()?.totalVocCount }}</span>
        次单词
      </div>
    </div>
  }
</section>

<ng-template #checkedIcon>
  <memo-icon size="14px" name="checkBadge"></memo-icon>
</ng-template>

<ng-template #notReachYetIcon>
  <span class="icon-not-reach-yet icon-stats-base aspect-square"></span>
</ng-template>
