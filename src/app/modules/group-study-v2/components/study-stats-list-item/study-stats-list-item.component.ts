import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core'
import { getColorRGBFromColorType, getRGBAFromColorType } from '../../../shared/helper/color'
import { fromSeconds } from '../../../shared/helper/time'
import { UserBase } from '../../models/member.model'
import { MemberDailyStudyStats, MemberStudyStats } from '../../pages/study-report/type'

@Component({
  selector: 'app-study-stats-list-item',
  templateUrl: './study-stats-list-item.component.html',
  styleUrls: ['./study-stats-list-item.component.scss'],
  host: {
    '[class.failed]': 'isFailed() || isExited()',
    '[attr.user-id]': 'memberInfo()?.userId',
    '[style.--member-theme]': 'memberTheme()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class StudyStatsListItemComponent {
  readonly ASSETS_PREFIX = 'assets/images/sign-status/'
  readonly CHINA_IMG_SRC = 'assets/images/china.png'

  memberInfo = input<UserBase>()

  studyStats = input<MemberDailyStudyStats>()

  studyReport = input<MemberStudyStats>()

  showRewardBadge = input(false)

  showStudyBadge = input(false)

  studyBadgeText = input('非酋奖')

  showRepairButton = input(false)

  /**
   * 队伍要求的每日学习时长，单位：秒
   */
  requiredDuration = input(0)

  /**
   * 队伍要求的每日学习单词量
   */
  requiredVocCounts = input(0)

  isFailed = input(false)

  lanternThemeEnable = input(false)

  isCurrentUser = input(false)

  dbclickAvatar = output<HTMLElement>()

  clickRepair = output()

  memberTheme = computed(() => {
    const { r, g, b } = this.skinColorRGB()
    return `${r}, ${g}, ${b}`
  })

  redBadgeTheme = getColorRGBFromColorType('red')

  private skinColorRGB = computed(() => {
    const studyStatus = this.studyStats()

    if (
      studyStatus?.skin
      && !this.isFailed()
      && this.showRewardBadge()
      && this.rewardedAmount() > 0
    ) {
      return getRGBAFromColorType(studyStatus.skin)
    }

    return getRGBAFromColorType('grey-2')
  })

  requiredDurationMin = computed(() => fromSeconds(this.requiredDuration(), 'min'))

  isExited = computed(() => {
    return !!this.studyStats()?.isExited
  })

  signAvatarSrc = computed(() => {
    const signAvatar = this.studyStats()?.studyLog?.signAvatar
    const imgName = `img_${signAvatar}`
    let extraSuffix = ''

    if (signAvatar !== undefined && this.lanternThemeEnable()) {
      extraSuffix = '-lantern'
    }

    return `assets/images/sign-status${extraSuffix}/${imgName}.png`
  })

  rewardedAmount = computed(() => {
    if (!this.studyStats() && !this.studyReport()) {
      return 0
    }

    if (this.studyStats()) {
      return this.studyStats()?.studyLog?.rewardedAmount ?? 0
    }

    return this.studyReport()?.totalRewarded ?? 0
  })

  onDbclickAvatar(target: HTMLElement) {
    this.dbclickAvatar.emit(target)
  }
}
