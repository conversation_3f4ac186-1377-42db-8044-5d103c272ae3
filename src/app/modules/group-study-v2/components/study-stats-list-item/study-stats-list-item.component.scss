:host {
  width: 100%;
  padding: 8px;
  border-radius: 8px;
}

:host.failed,
.failed-state {
  color: var(--default-font-color) !important;
}

:host.failed {
  .avatar-container,
  .study-stats > * > :first-child {
    opacity: 0.3;
  }
}

.avatar-container {
  position: relative;
}

.floating-badge {
  --badge-size: 24px;

  position: absolute;
  right: 4px;
  top: 4px;
  transform: scale(0.5) translate(50%, -50%);
  transform-origin: top right;
}

.study-info {
  min-width: 0;
  flex: 1;
  margin-left: 12px;
  font-size: 12px;
}

:host,
.user-info-text,
.study-stats,
.study-stats > * {
  display: flex;
  align-items: center;
  line-height: 1rem;
}

.user-info-text {
  margin-bottom: 4px;
  color: var(--default-font-color);

  & > span:not(:first-of-type):last-of-type {
    display: inline-flex;
    align-items: center;
    min-width: max-content;

    &::before {
      content: '·';
      display: inline-block;
      margin: 0 0.5em;
    }
  }

  memo-study-badge {
    margin-left: 10px;
  }
}

.user-name {
  --color: var(--second-font-color);

  color: var(--color);
}

.china-theme {
  --color: var(--red-bgColor);
}

.stats-daily {
  --item-gap: 0.5em;

  & > * {
    &:not(:first-child) {
      min-width: 0;
      flex: 1;
      margin-left: var(--item-gap);
    }

    :not(:first-child) {
      margin-left: 0.3em;
    }
  }

  :nth-child(2) {
    justify-content: center;
  }
}

.stats-total {
  padding-right: 1em;
  justify-content: space-between;
}

.divider {
  height: 1.2em;
  width: 1px;
  background-color: var(--border-color);
}

.study-stats memo-icon {
  color: var(--theme-green);
}

.sign-avatar-container img {
  width: 100%;
}

.icon-stats-base {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  overflow: hidden;
}

.icon-not-reach-yet {
  background-color: var(--bg-color-2);
  border: 1.5px solid #ededed;
}

.study-value {
  margin: 0 0.3em;
  font-weight: bold;
  color: var(--theme-green);
}

@media (max-width: 425px) {
  .stats-daily {
    --item-gap: 0.3em;
  }

  .study-stats {
    white-space: nowrap;
    width: 110%;
    transform: scale(0.9);
    transform-origin: 0 50%;
  }

  .stats-total {
    padding-right: 0;
  }

  .study-info {
    margin-left: 10px;
  }
}
