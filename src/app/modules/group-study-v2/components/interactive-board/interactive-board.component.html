<header>
  <h2>最新动态</h2>
  <button class="btn-close aspect-square" (click)="onClose()">
    <memo-icon name="shrink" size="14px"></memo-icon>
  </button>
</header>
<div #scrollEl class="message-list">
  <div style="position: relative" [style.px]="virtualizer.getTotalSize()">
    <div
      style="position: absolute; top: 0; left: 0; width: 100%"
      [style.transform]="
        'translateY(' + (virtualizer.getVirtualItems()[0] ? virtualizer.getVirtualItems()[0].start : 0) + 'px)'
      "
    >
      @for (item of virtualizer.getVirtualItems(); track item.key) {
        @let msg = messages()[item.index];
        <div #listItem [attr.data-index]="item.index">
          @if (firstOfDateList().includes(msg.id)) {
            @let offsetDay = getDaysFromCreatedTime(msg.createdTime);
            <div class="date-block">
              @if (offsetDay > 0) {
                第{{ offsetDay }}天
              }
              {{ msg.createdTime | studyDate }}
            </div>
          }
          <app-board-message
            class="message-item"
            [message]="msg"
            [likedByMembers]="messageLikedByMembersMap[msg.id] || []"
            (dbClickMember)="onDbClickMember($event)"
            (like)="onLikeMessage($event)"
          ></app-board-message>
        </div>
      }
    </div>
  </div>
</div>
