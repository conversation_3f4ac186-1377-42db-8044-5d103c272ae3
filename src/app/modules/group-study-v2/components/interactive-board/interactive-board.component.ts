import { ChangeDetectorRef, Component, effect, Input, OnInit, signal, viewChild, viewChildren, type ElementRef } from '@angular/core'
import { ModalController } from '@ionic/angular'
import { take } from 'rxjs'
import { injectVirtualizer } from '@tanstack/angular-virtual'
import { UserService } from '../../../core/services/user.service'
import { Event } from '../../models/event.model'
import { Group } from '../../models/group.model'
import { Member } from '../../models/member.model'
import { Message } from '../../models/message.model'
import { Speaker } from '../../models/speaker.model'
import { GroupStudyStorageService } from '../../services/group-study-storage.service'
import { GroupStudyTickleService } from '../../services/group-study-tickle.service'
import { toStudyDate } from '../../../shared/helper/time'
import { InteractiveBoardService } from './interactive-board.service'

@Component({
  selector: 'app-interactive-board',
  templateUrl: './interactive-board.component.html',
  styleUrls: ['./interactive-board.component.scss'],
  standalone: false,
})
export class InteractiveBoardComponent implements OnInit {
  @Input() group!: Group

  @Input() events: Event[] = []

  private scrollRef = viewChild<ElementRef<HTMLElement>>('scrollEl')
  private listItems = viewChildren<ElementRef<HTMLElement>>('listItem')

  messages = signal<Message[]>([])

  firstOfDateList = signal<string[]>([])

  virtualizer = injectVirtualizer(() => ({
    count: this.messages().length,
    estimateSize: () => 170,
    overscan: 10,
    scrollElement: this.scrollRef(),
    getItemKey: index => {
      return this.messages()[index].id
    },
  }))

  /**
   * {
   *  [Message.id]: Member[]
   * }
   */
  messageLikedByMembersMap: Record<string, Member[]> = {}

  constructor(
    private modal: ModalController,
    private tickle: GroupStudyTickleService,
    private board: InteractiveBoardService,
    private gss: GroupStudyStorageService,
    private cdr: ChangeDetectorRef,
    private user: UserService,
  ) {
    effect(() => {
      this.listItems().forEach(el => {
        this.virtualizer.measureElement(el.nativeElement)
      })
    })
  }

  ngOnInit(): void {
    this.gss.getActivity$(this.group.activityId)
      .pipe(
        take(1),
      )
      .subscribe(activity => {
        const {
          likedByMap = {},
          allMessages = [],
          firstOfDateList = [],
        } = this.board.generateMessagesData(this.group, activity, this.events || [])

        this.messages.set(allMessages)
        this.firstOfDateList.set(firstOfDateList)
        this.messageLikedByMembersMap = likedByMap
      })
  }

  onClose() {
    this.modal.dismiss()
  }

  onDbClickMember(payload: { element: HTMLElement; target: Speaker }) {
    const { element, target } = payload
    const group = this.group
    if (!group) {
      return
    }
    this.tickle.tickle(element, group, target, target.type === 'USER')
  }

  onLikeMessage(message: Message) {
    const group = this.group
    const likedByMembers = this.messageLikedByMembersMap[message.id] || []
    if (!group || likedByMembers.some(v => v.userId === this.user.userInfo?.userId)) {
      return
    }

    this.gss.track('LIKE', group.groupId, message.id).subscribe({
      next: () => {
        const memberInfo = this.gss.getMemberInfoById(group.groupId, this.user.userInfo!.userId)
        if (memberInfo) {
          likedByMembers.push(memberInfo)
          this.messageLikedByMembersMap[message.id] = likedByMembers
          this.cdr.detectChanges()
        }
      },
    })
  }

  getDaysFromCreatedTime(date: string) {
    const targetDate = toStudyDate(this.group.studyStartTime!)

    return Math.ceil(toStudyDate(date).diff(targetDate, 'd', true))
  }
}
