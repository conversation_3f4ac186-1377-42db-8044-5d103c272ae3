import { Injectable, SecurityContext } from '@angular/core'
import { Dom<PERSON>anitizer } from '@angular/platform-browser'
import { <PERSON>dal<PERSON>ontroller } from '@ionic/angular'
import dayjs from 'dayjs'
import { toStudyDate } from '../../../shared/helper/time'
import { groupBy } from '../../../shared/helper/utils'
import { Activity } from '../../models/activity.model'
import { Event, EventType } from '../../models/event.model'
import { Group } from '../../models/group.model'
import { Member } from '../../models/member.model'
import { Message } from '../../models/message.model'
import { Speaker } from '../../models/speaker.model'
import { DATE_FORMAT } from '../../pages/study-detail/helper'
import { GroupStudyStorageService } from '../../services/group-study-storage.service'
import { InteractiveBoardComponent } from './interactive-board.component'

interface MessagesData {
  messagesMap?: Record<string, Message[]>
  likedByMap?: Record<string, Member[]>
  firstOfDateList?: string[]
  allMessages?: Message[]
}
@Injectable({
  providedIn: 'root',
})
export class InteractiveBoardService {
  private readonly ALLOWED_EVENT_TYPES: EventType[] = [
    'CREATE_GROUP', 'JOIN_GROUP', 'GROUP_SUCCEEDED', 'GROUP_FAILED',
    'TEST_SUCCEEDED', 'TEST_FAILED',
    'LEARN_SUCCEED_TODAY', 'LEARN_SUCCEED',
    'TICKLE',
    'REFUND',
  ]

  private readonly EVENT_TYPE_TO_TITLE: Partial<Record<EventType, string>> = {
    CREATE_GROUP: '创建队伍',
    JOIN_GROUP: '加入队伍',
    GROUP_FAILED: '组队期失败',
    GROUP_SUCCEEDED: '组队成功',
    TEST_SUCCEEDED: '磨合期成功',
    TEST_FAILED: '磨合期失败',
    LEARN_SUCCEED_TODAY: '完成今日任务',
    LEARN_SUCCEED: '完成学习任务',
    REFUND: '退款通知',
  }

  private readonly EVENT_TYPE_TO_CONTENT: Partial<Record<EventType, string>> = {
    CREATE_GROUP: '快来加入我的队伍吧！🤘',
    JOIN_GROUP: '我来了，未来大家一起努力呀！🙌',
    GROUP_SUCCEEDED: '恭喜组队成功，赶紧去学习吧！🥳',
    TEST_SUCCEEDED: '恭喜你们的队伍通过磨合期！🎉未来的日子要和小伙伴们坚持学习收获奖励哦！',
    LEARN_SUCCEED: '本次组队结束啦👋🏻，期待你的再次组队 ~',
  }

  private readonly _groupMemberSpeakerMap = new Map<string, Record<number, Speaker>>()
  private readonly EVENT_ID_TO_MESSAGE = new Map<string, Message>()

  constructor(
    private gss: GroupStudyStorageService,
    private modal: ModalController,
    private sanitizer: DomSanitizer,
  ) {}

  // TODO: 待优化
  generateMessagesData(group: Group, activity: Activity, events: Event[]): MessagesData {
    const result: MessagesData = {}

    if (!group) {
      return result
    }

    const eventsForMessage: Event[] = []
    const eventsForLikedBy: Event[] = []
    const firstOfDateList: string[] = []
    const allMessages: Message[] = []

    for (const event of events) {
      if (event.eventType === 'LIKE') {
        eventsForLikedBy.push(event)
      }

      if (this.ALLOWED_EVENT_TYPES.includes(event.eventType) && event.targetId !== 'INTERACTIVE_BOARD') {
        if (event.eventType === 'REFUND') {
          const refundInfo = GroupStudyStorageService.getRefundInfoFromEvent(event)
          if (refundInfo.refund_status === 'REFUNDING') {
            continue
          }
        }

        eventsForMessage.push(event)
      }
    }

    if (eventsForMessage.length) {
      const eventsForMessageGroupByDate = groupBy(eventsForMessage, v => toStudyDate(v.createdTime).format(DATE_FORMAT))

      const sortedDates = Object.entries(eventsForMessageGroupByDate).sort((a, b) => {
        return dayjs(a[0]).isBefore(dayjs(b[0])) ? 1 : -1
      })
      const messagesGroupByDate = sortedDates
        .reduce((acc, [date, sameDayEvents]) => {
          const sortedSameDayEvents = sameDayEvents.sort((a, b) => {
            const aDate = dayjs(a.createdTime)
            const bDate = dayjs(b.createdTime)
            if (aDate.isSame(bDate, 'millisecond')) {
              const isACreate = a.eventType === 'CREATE_GROUP' ? 1 : -1
              const isBCreate = b.eventType === 'CREATE_GROUP' ? 1 : -1
              return isACreate - isBCreate
            } else {
              return aDate.isBefore(bDate) ? 1 : -1
            }
          })

          const firstEvent = sortedSameDayEvents?.[0]
          firstEvent && firstOfDateList.push(firstEvent.id)
          const isToday = dayjs(date).isSame(toStudyDate(Date.now()), 'd')

          let finishedMembers: number[] = []

          // 今天意味着数据会实时更新，不能直接读缓存
          if (isToday || !firstEvent || !this.EVENT_ID_TO_MESSAGE.has(firstEvent.id)) {
            finishedMembers = sortedSameDayEvents
              .filter(event => event.eventType === 'LEARN_SUCCEED_TODAY')
              .map(event => event.creator)
          }

          const messages = sortedSameDayEvents
            .map(event => {
              const message = !isToday && this.EVENT_ID_TO_MESSAGE.has(event.id)
                ? this.EVENT_ID_TO_MESSAGE.get(event.id)!
                : this.toMessage(group, event, activity, finishedMembers)

              !this.EVENT_ID_TO_MESSAGE.has(event.id) && this.EVENT_ID_TO_MESSAGE.set(event.id, message)

              return message
            })
          acc[date] = messages
          allMessages.push(...messages)

          return acc
        }, {} as Record<string, Message[]>)
      result.messagesMap = messagesGroupByDate
    }

    if (eventsForLikedBy.length) {
      const eventsForLikeByGroupByMessageId = groupBy(eventsForLikedBy, v => v.targetId!)

      const membersGroupByMessageId = Object.keys(eventsForLikeByGroupByMessageId).reduce((acc, messageId) => {
        const likedByMessages = eventsForLikeByGroupByMessageId[messageId]

        acc[messageId] = likedByMessages.map(v => this.gss.getMemberInfoById(group.groupId, v.creator)!)

        return acc
      }, {} as Record<string, Member[]>)
      result.likedByMap = membersGroupByMessageId
    }

    result.firstOfDateList = firstOfDateList
    result.allMessages = allMessages

    return result
  }

  toMessage(group: Group, event: Event, activity: Activity, finishedMembers: number[] = []): Message {
    const { groupId } = group
    const speaker: Speaker = event.creator === 0
      ? {
          name: '',
          userId: event.creator,
          type: 'BOT',
          avatar: 'assets/images/maimemo_bot.png',
          status: 'NORMAL',
        }
      : this.getSpeaker(
          groupId,
          this.gss.getMemberInfoById(groupId, event.creator)
          ?? {
            userId: event.creator,
            avatar: '',
            name: '未知成员',
            type: 'PARTICIPANT',
            isCurrentUser: false,
            joinedTime: '',
          },
        )

    return {
      id: event.id,
      type: event.eventType,
      title: this.getMessageTitle(groupId, event),
      content: this.getMessageContent(group, event, activity, finishedMembers),
      speaker,
      createdTime: event.createdTime,
    }
  }

  getSpeaker(groupId: string, member: Member): Speaker {
    const memberSpeakerMap = this._groupMemberSpeakerMap.get(groupId) || {}
    if (memberSpeakerMap[member.userId]) {
      return memberSpeakerMap[member.userId]
    }

    const speaker: Speaker = {
      type: 'USER',
      userId: member.userId,
      name: member.name,
      avatar: member.avatar,
      status: member.status,
    }

    memberSpeakerMap[member.userId] = speaker

    if (!this._groupMemberSpeakerMap.has(groupId)) {
      this._groupMemberSpeakerMap.set(groupId, memberSpeakerMap)
    }

    return speaker
  }

  private getMessageContent(group: Group, event: Event, activity: Activity, finishedMembers: number[] = []) {
    const eventType = event.eventType
    const { creator } = event
    const { members = [] } = group
    const finishOrder = finishedMembers.indexOf(creator)

    switch (eventType) {
      case 'GROUP_FAILED':
        return `${this.getMentionMemberString(members)} 参与队伍人数不足 2 人，组队失败。`
      case 'TEST_FAILED':
        return `${this.getMentionMemberString(members)} 通过磨合期的人数少于 2 人，组队失败。`
      case 'LEARN_SUCCEED_TODAY':

        if (finishOrder === finishedMembers.length - 1) {
          return '今天我最早完成耶😌！'
        } else if (finishOrder === 0) {
          return '久等了，我 done 了😎！'
        }
        return '我也完成学习啦😚~'
      case 'TICKLE':
        return event.comment || '未知消息'
      case 'REFUND': {
        const member = this.gss.getMemberInfoById(group.groupId, creator)
        const { config } = activity
        const isCreator = creator === activity.creator
        const isExitAtTestingDay = member?.status === 'EXITED' && member.exitedTime && !toStudyDate(group.studyStartTime!).add(1, 'd').isAfter(toStudyDate(member.exitedTime))
        const refundInfo = GroupStudyStorageService.getRefundInfoFromEvent(event)
        if (refundInfo.group_status === 'GROUP_FAILED') {
          return `@${this.getStrongTag(member?.name || '')} 报名队伍人数不满足组队条件，已原路退款 ${refundInfo.amount} 元给你。`
        }

        if (refundInfo.group_status === 'TESTING') {
          return `@${this.getStrongTag(member?.name || '')} 队伍未满员，在磨合期结束后会退相应费用给你。`
        }

        if (refundInfo.group_status === 'LEARNING') {
          const refundedMembersCount = Number(refundInfo.members_count)
          if (isCreator && config?.paymentType === 'CREATOR') {
            if (!isExitAtTestingDay) {
              return `@${this.getStrongTag(member?.name || '')} 有 ${refundedMembersCount} 人参与组队失败，已原路退款 ${refundInfo.amount} 元给你，其余人将继续组队学习。`
            }

            if (refundedMembersCount > 1) {
              return `@${this.getStrongTag(member?.name || '')} 你和 ${refundedMembersCount - 1} 人参与组队失败，已原路退款 ${refundInfo.amount} 元给你，其余人将继续组队学习。`
            }
          }
          return `@${this.getStrongTag(member?.name || '')} 你未通过磨合期，已原路退款 ${refundInfo.amount} 元给你，其余人将继续组队学习。`
        }

        return `@${this.getStrongTag(member?.name || '')} 队伍未通过磨合期，已原路退款 ${refundInfo.amount} 元给你。`
      }
      default:
        return this.EVENT_TYPE_TO_CONTENT[eventType] || '未知消息'
    }
  }

  private getMessageTitle(groupId: string, event: Event) {
    const eventType = event.eventType

    if (eventType === 'TICKLE') {
      const targetMember = this.gss.getMemberInfoById(groupId, Number(event.targetId))
      return `拍了拍 ${targetMember?.name || '未知成员'}`
    }

    return this.EVENT_TYPE_TO_TITLE[eventType] || '未知状态'
  }

  async showInteractiveBoard(group: Group, events: Event[]) {
    if (!group) {
      return
    }
    this.gss.track('CLICK', group.groupId, 'INTERACTIVE_BOARD').subscribe()
    const modalRef = await this.modal.create({
      component: InteractiveBoardComponent,
      componentProps: {
        group,
        events,
      },
    })

    modalRef.present()

    return modalRef
  }

  private getStrongTag(text: string): string {
    return '<strong>' + this.sanitizer.sanitize(SecurityContext.HTML, text) + '</strong>'
  }

  private getMentionMemberString(members: Member[]): string {
    return members
      .map(v => `@${this.getStrongTag(v.name)}`)
      .join(' ')
  }
}
