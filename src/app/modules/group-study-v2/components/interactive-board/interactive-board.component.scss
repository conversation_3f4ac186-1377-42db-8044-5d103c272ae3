:host {
  --gap-base: 5px;
  --border-color: #e5e5e5;

  padding: calc(var(--gap-base) * 2) 0;
  background-color: var(--bg-color-2);
  display: flex;
  align-items: center;
  flex-direction: column;
  height: 100%;
  color: var(--font-color-1);
}

header {
  width: 100%;
  padding: calc(var(--gap-base) * 2) calc(var(--gap-base) * 4);
  display: flex;
  align-items: center;
  justify-content: space-between;

  h2 {
    font-size: 1rem;
    font-weight: bold;
    margin: 0;
  }
}

.btn-close {
  --size: 22px;

  width: var(--size);
  height: var(--size);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--second-font-color);
  background-color: var(--bg-color-1);
  border-radius: 5px;
  border: none;
  padding: 0;
}

:root.dark :host .btn-close {
  background-color: var(--gray-level-100);
}

.date-block {
  font-size: 13px;
  color: var(--second-font-color);
  margin-bottom: calc(var(--gap-base) * 2);
  margin-top: calc(var(--gap-base) * 3);
}

.message-list {
  flex: 1;
  overflow-y: auto;
  width: 100%;
  height: 100%;
  padding: 0 calc(var(--gap-base) * 4);
  overflow-y: auto;
}
