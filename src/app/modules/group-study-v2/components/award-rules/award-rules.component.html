<ul class="rules-set">
  <li>
    <span>
      组队首日，每人奖励 1 ~ 9600 单词上限
      <memo-icon
        class="icon-attention"
        name="attention"
        size="1em"
        [routerLink]="['.', 'reward-probability']"
        queryParamsHandling="merge"
      ></memo-icon>
    </span>
  </li>
  <li>
    <span>次日开始，邻近你身边的组员当日签到头像若有相同则获得奖励（首尾组员互为相邻）</span>
  </li>
</ul>
<p style="color: var(--green-color-1);font-size: 14px;">
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="10" height="8" viewBox="0 0 10 8" fill="none">
    <path id="矩形 1" fill-rule="evenodd" style="fill:currentColor" opacity="1" d="M8.84 3.54L1.35 0.54C0.9 0.35 0.48 0.85 0.73 1.26L2.19 3.75C2.28 3.9 2.28 4.1 2.19 4.25L0.73 6.74C0.48 7.15 0.9 7.65 1.35 7.46L8.84 4.46C9.25 4.29 9.25 3.71 8.84 3.54Z"></path>
  </svg>
  举个栗子：
</p>
<ul class="example-list">
  @for (item of awardExamples; track item.imgUrl) {
    <li class="example-item">
      <p>{{ item.description }}</p>
      <picture>
        <source media="(min-width: 768px)" [srcset]="item.imgUrl + '_wide.png'">
        <img decoding="async" [src]="item.imgUrl + '.png'" alt="award example">
      </picture>
    </li>
  }
</ul>
<picture>
  <source media="(min-width: 768px)" [srcset]="IMG_ASSET_PREFIX + 'rule_sheet_wide' + (isDark() ? '_dark' : '') +'.png'">
  <img class="rules-sheet-image" [src]="IMG_ASSET_PREFIX + 'rule_sheet' + (isDark() ? '_dark' : '') + '.png'" alt="">
</picture>
<ul class="rules-set">
  @for(item of awardRules; track item) {
    <li>
      <!-- 加多一层是因为 li::before 的样式会在低版本 Safari 上崩坏-->
      <span>{{ item }}</span>
    </li>
  }
</ul>
