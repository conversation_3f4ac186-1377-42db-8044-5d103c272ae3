:host {
  --gap-base: 5px;
  display: flex;
  flex-direction: column;

  & > :not(:last-child) {
    margin-bottom: calc(var(--gap-base) * 3);
  }
}

h2 {
  text-align: center;
  font-size: 1rem;
  font-weight: 600;
  margin-top: 0;
}

p {
  margin: 0;
}

:host > p:first-of-type {
  font-weight: 500;
  font-size: 15px;
}

.example-list {
  margin: calc(var(--gap-base) * -4) 0 0 0;
  padding: 0;
}

.example-item {
  display: block;
  list-style: none;
  padding: calc(var(--gap-base) * 3) 0;

  p {
    font-size: 13px;
    margin-top: 0;
    margin-bottom: 12px;
    color: var(--second-font-color);
  }

  img {
    width: 100%;
    max-width: 500px;
  }
}

.example-item:not(:last-child) {
  border-bottom: 1px dashed var(--border-color);
}

.rules-sheet-image {
  display: block;
  width: 100%;
}

.rules-set {
  margin: 5px 0;
  padding: 0;
  list-style: none;
  display: flex;
  flex-direction: column;

  & > :not(:last-child) {
    margin-bottom: 7px;
  }
}

.rules-set > li {
  padding-left: 0;
  font-size: 13px;
  display: flex;
  align-items: first baseline;

  &::before {
    content: "";

    margin-right: 8px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--theme-green);
    transform: translateY(-0.15em);
  }

  span {
    flex: 1;
  }
}

.icon-attention {
  color: var(--second-font-color);
  vertical-align: middle;
  transform: translateY(-1px);
}
