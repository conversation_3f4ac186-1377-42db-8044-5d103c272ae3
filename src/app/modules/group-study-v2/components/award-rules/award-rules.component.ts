import { ChangeDetectionStrategy, Component, signal } from '@angular/core'

@Component({
  selector: 'app-award-rules',
  templateUrl: './award-rules.component.html',
  styleUrls: ['./award-rules.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class AwardRulesComponent {
  readonly IMG_ASSET_PREFIX = 'assets/images/v2/awards/'

  isDark = signal(false)

  awardExamples: {
    description: string
    imgUrl: string
  }[] = [
    {
      description: '临近你的组员与你有连续 5 个相同头像，你将获得 80 个单词上限奖励',
      imgUrl: this.IMG_ASSET_PREFIX + 'example_combo_5',
    },
    {
      description: '临近你的组员与你有连续 3 个相同头像，你将获得 9 个单词上限奖励',
      imgUrl: this.IMG_ASSET_PREFIX + 'example_combo_3',
    },
    {
      description: '临近你的组员与你的头像均不相同，你将只获得 1 个单词上限奖励',
      imgUrl: this.IMG_ASSET_PREFIX + 'example_combo_1',
    },
  ]

  awardRules: string[] = [
    '队首的组员和队尾的组员互为邻近；',
    '若你出现两次 “未达标”，则你将退出此次组队，并在此之后你两侧的队员将互为相邻；',
    '只有在组队学习最后一天完成后才可以领取奖励，中途退出无奖励。',
  ]

  constructor() {
    this.isDark.set(document.documentElement.classList.contains('dark'))
  }
}
