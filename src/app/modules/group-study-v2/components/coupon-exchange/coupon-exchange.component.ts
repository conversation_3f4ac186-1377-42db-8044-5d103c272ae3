import { ChangeDetectionStrategy, Component, Input, inject } from '@angular/core'
import { MemoModalService } from '../../../ui/modal/modal.service'
import { Coupon } from '../../models/coupon.model'

@Component({
  selector: 'memo-coupon-exchange',
  templateUrl: './coupon-exchange.component.html',
  styleUrls: ['./coupon-exchange.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class CouponExchangeComponent {
  @Input() coupons: Coupon[] = []

  private modal = inject(MemoModalService)

  onClickExchange() {
    this.modal.dismiss()
  }
}
