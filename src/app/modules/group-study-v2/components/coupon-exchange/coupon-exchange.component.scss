:host {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  width: 80vw;
  max-width: max-content;
  color: var(--title-color);
}

p {
  margin: 0;
  line-height: 1.7;
  font-weight: 500;
  max-width: 85%;
  text-align: center;
}

.coupon-tips {
  margin-top: 0.5em;
  color: var(--second-font-color);
  font-size: 12px;
}

:host > :first-child {
  margin-top: 15px;
}

img {
  margin-top: 15px;
  width: 50%;
  height: auto;
}

.color-green {
  color: var(--theme-green);
}

footer {
  width: 100%;
}
