import { HttpClient } from '@angular/common/http'
import { Injectable, inject } from '@angular/core'
import { Observable, map } from 'rxjs'
import { ShareContentConfig, SocialMediaName } from '../../shared/helper/social'
import { dayjsWithTZ } from '../../shared/helper/time'
import { fromResData } from '../../shared/helper/utils'
import { ListParams, Response } from '../../shared/types'
import { ActivityDetail, ActivityDetailRes, ActivityType, ActivityWithUserStatus, ActivityWithUserStatusRes } from '../models/activity.model'
import { Coupon } from '../models/coupon.model'
import { EventTrackBody } from '../models/event.model'
import { Group, GroupData, GroupDataRes, GroupReqParams, GroupRes, GroupStudyPublishLog, GroupStudyPublishLogRes, GroupWithConfig, GroupWithConfigRes, GroupingStatusRes } from '../models/group.model'
import { RewardProbability, RewardProbabilityRes } from '../models/probability.model'
import { Report, ReportRes } from '../models/report.model'
import { WorkerService } from '../../core/services/worker.service'
import { CreateActivityPayload, CreateActivityRes, JoiningGroupParams, JoiningGroupRes } from './request.type'

@Injectable({
  providedIn: 'root',
})
export class GroupStudyNetworkService {
  private readonly prefix = '/pages/activity-center'
  private http = inject(HttpClient)
  private workerService = inject(WorkerService)

  // private commonWorker = this.workerService.getWorker('common')

  queryGroupData(params: GroupReqParams): Observable<GroupData> {
    const {
      activity_id = '',
      invitation_code = '',
      update_time,
      scope = 'FULL',
    } = params || {}

    const reqParams: GroupReqParams = {
      scope,
    }

    if (activity_id && activity_id !== '') {
      reqParams.activity_id = activity_id
    }

    if (invitation_code && invitation_code !== '') {
      reqParams.invitation_code = invitation_code
    }

    if (update_time) {
      reqParams.update_time = dayjsWithTZ(update_time).format('YYYY-MM-DD HH:mm:ss')
    }

    return this.http.get<Response<GroupDataRes>>(`${this.prefix}/api/v1/group_study/groups`, {
      params: { ...reqParams },
    })
      .pipe(
        map(res => fromResData(res.data)),
      )
  }

  queryGroupById(groupId: string): Observable<GroupData> {
    return this.http.get<Response<{ group: GroupRes }>>(`${this.prefix}/api/v1/group_study/groups/${groupId}`)
      .pipe(
        map(res => fromResData(res.data)),
      )
  }

  queryPublicGroups(params?: ListParams): Observable<{ groups: GroupWithConfig[] }> {
    return this.http.get<Response<{ groups: GroupWithConfigRes[] }>>(`${this.prefix}/api/v1/group_study/groups/public`, { params: { ...params } })
      .pipe(
        map(res => fromResData(res.data)),
      )
  }

  queryHistoryGroups(params?: ListParams) {
    return this.http.get<Response<{ groups: Group[] }>>(`${this.prefix}/api/v1/group_study/groups/history`, { params: { ...params } })
      .pipe(
        map(res => res.data.groups),
      )
  }

  publishGroup(groupId: string): Observable<GroupStudyPublishLog> {
    return this.http.post<Response<{ public_group: GroupStudyPublishLogRes }>>(`${this.prefix}/api/v1/group_study/groups/public/${groupId}`, {})
      .pipe(
        map(res => fromResData(res.data.public_group)),
      )
  }

  recallGroupPublish(groupId: string): Observable<unknown> {
    return this.http.delete<Response<unknown>>(`${this.prefix}/api/v1/group_study/groups/public/${groupId}`)
  }

  queryActivity(activityId: string): Observable<ActivityDetail> {
    return this.http.get<Response<ActivityDetailRes>>(`${this.prefix}/api/v1/activities/${activityId}`)
      .pipe(
        map(res => fromResData(res.data)),
      )
  }

  queryActivities(): Observable<ActivityWithUserStatus<ActivityType>[]> {
    return this.http.get<Response<{ activities: ActivityWithUserStatusRes<ActivityType>[] }>>(`${this.prefix}/api/v1/activities/me`)
      .pipe(
        map(res => fromResData(res.data.activities)),
      )
  }

  queryGroupReports(groupId: string): Observable<Report[]> {
    return this.http.get<Response<{ reports: ReportRes[] }>>(`${this.prefix}/api/v1/group_study/groups/reports`, { params: { group_id: groupId } })
      .pipe(
        map(res => fromResData(res.data.reports)),
      )
  }

  uploadEventTrack(body: EventTrackBody): Observable<unknown> {
    return this.http.post<Response<unknown>>(`${this.prefix}/api/v1/group_study/track`, body)
  }

  getGroupInvitation(groupId: string) {
    return this.http
      .get<Response<{ invitation_code: string }>>(`${this.prefix}/api/v1/group_study/groups/share`, {
        params: {
          group_id: groupId,
        },
      })
      .pipe(
        map(res => fromResData(res.data)),
      )
  }

  createActivity(payload: CreateActivityPayload) {
    return this.http.post<Response<CreateActivityRes>>(`${this.prefix}/api/v1/activities`, { activity: payload })
      .pipe(
        map(res => res.data),
      )
  }

  joinTheGroup(body: JoiningGroupParams) {
    return this.http
      .post<Response<JoiningGroupRes>>(`${this.prefix}/api/v1/group_study/groups/join`, body)
      .pipe(
        map(res => res.data),
      )
  }

  getRewards(groupId: string) {
    return this.http.post<Response<{ reward_count: number; coupons?: Coupon[] }>>(`${this.prefix}/api/v1/group_study/groups/reward`, { group_id: groupId })
      .pipe(
        map(res => res.data),
      )
  }

  getRewardById(rewardId: string) {
    return this.http.post<Response<{ coupons?: Coupon[] }>>(`${this.prefix}/api/v1/group_study/coupons/acquire`, undefined, {
      params: { reward_id: rewardId },
    })
  }

  getGroupingPrice(params: CreateActivityPayload) {
    const { config, ...rest } = params
    return this.http.post<Response<{ price: { total_price: number; unit_price: number; max_reward: number } }>>(
      `${this.prefix}/api/v1/group_study/price`,
      {
        ...rest,
        ...config,
      })
      .pipe(
        map(res => fromResData(res.data.price)),
      )
  }

  recordReadActivity(activityId: string) {
    return this.http.post<Response<unknown>>(`${this.prefix}/api/v1/activities/read`, { activity_id: activityId })
  }

  queryRewardProbabilities(): Observable<Record<number, RewardProbability[]>> {
    return this.http.get<Response<{ probability: Record<number, RewardProbabilityRes[]> }>>(`${this.prefix}/api/v1/group_study/groups/reward/probability`)
      .pipe(
        map(res => fromResData(res.data.probability)),
      )
  }

  queryGroupingStatus(): Observable<GroupingStatusRes> {
    return this.http.get<Response<GroupingStatusRes>>(`${this.prefix}/api/v1/group_study/groups/limitations`)
      .pipe(
        map(res => res.data),
      )
  }

  getShareContentPreset(params: { platform: SocialMediaName; groupId?: string; book_catalog?: string }): Observable<ShareContentConfig> {
    const { platform, groupId, book_catalog } = params
    const actualParams: Record<string, string> = {
      platform: {
        xiaohongshu: '小红书',
        weibo: '新浪微博',
      }[platform] || platform,
    }

    if (groupId) {
      actualParams.group_id = groupId
    }

    if (book_catalog) {
      params.book_catalog = book_catalog
    }

    return this.http.get<Response<ShareContentConfig>>(
      `${this.prefix}/api/v1/group_study/groups/share/blog`,
      {
        params: actualParams,
      },
    )
      .pipe(
        map(res => res.data),
      )
  }

  dismissGroup(groupId: string) {
    return this.http.post<Response<{ group: GroupRes }>>(`${this.prefix}/api/v1/group_study/groups/${groupId}/dismiss`, {})
      .pipe(
        map(res => res.data.group),
      )
  }
}
