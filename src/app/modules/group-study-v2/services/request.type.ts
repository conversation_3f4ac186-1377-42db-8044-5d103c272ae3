import { Product } from '../../core/services/client-frontend-communication.service'
import { ActivityRes, ActivityType, CustomGroupStudyActivityConfigRes } from '../models/activity.model'
import { GroupRes } from '../models/group.model'
import { History } from '../models/history.model'

export interface CreateActivityPayload {
  title: string
  type: ActivityType
  config: CustomGroupStudyActivityConfigRes
}

export interface CreateActivityRes {
  activity: ActivityRes
  goods_sn?: string
  group_order?: Order
}

export interface CouponForOrder {
  total_discounted: number
  coupon_ids: string[]
  available_count: number
  description: string
  select_url: string
}

export interface Order {
  product: Product
  coupon?: CouponForOrder
  final_price?: number
  code?: string
}

export interface JoiningGroupParams {
  invitation_code?: string
  group_id?: string

  /**
   * 在队伍中的位置, 只有加入自建组队时才允许指定位置
   * 从 0 开始，如果用户没有指定位置，可以不传
  */
  position?: number
}

export interface JoiningGroupRes {
  /**
   * 组队状态
   */
  group: GroupRes

  /**
   * 历史
   */
  history?: History[]

  /**
   * 商品sn
   */
  goods_sn?: string
  group_order?: Order
}
