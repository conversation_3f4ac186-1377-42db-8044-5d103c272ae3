import { AnimationBuilder } from '@angular/animations'
import { Injectable } from '@angular/core'
import { UserService } from '../../core/services/user.service'
import { GroupStudyClientService } from '../../group-study/service/services/group-study-client.service'
import { MemoToastService } from '../../ui/toast/toast.service'
import { MemoTickleService } from '../../ui/utils/tickle.service'
import { Group } from '../models/group.model'
import { Member, UserBase } from '../models/member.model'
import { GroupStudyStorageService } from './group-study-storage.service'

@Injectable({
  providedIn: 'root',
})
export class GroupStudyTickleService extends MemoTickleService {

  private readonly TICKLE_ERROR_MESSAGE: Record<string, string> = {
    'event_tickle_type_error': '拍一拍类型有误',
    'event_tickle_target_1error': '拍一拍目标不存在',
    'event_tickle_target_is_oneself': '不能拍自己哦',
    'event_tickle_exceeded_limit_times_today': '你今天已达提醒 TA 的次数',
    'group_not_learning': '该队伍不处于学习期',
  }

  constructor(
    protected override builder: AnimationBuilder,
    private gss: GroupStudyStorageService,
    private client: GroupStudyClientService,
    private toast: MemoToastService,
    private user: UserService,
  ) {
    super(builder)
  }

  override tickle(element: HTMLElement, group: Group, target: UserBase & Pick<Member, 'status'>, withTrack = true): void {

    if (target.userId === this.user.userInfo?.userId) {
      this.toast.showToast('不能拍自己哦')
      return
    }

    if (target.status === 'EXITED') {
      this.toast.showToast('该成员已退出队伍')
      return
    }

    const currentMember = group.members.find(m => m.userId === this.user.userInfo?.userId)

    if (currentMember && currentMember.status === 'EXITED') {
      this.toast.showToast('你已退出队伍，无法使用“拍一拍”')
      return
    }

    super.tickle(element)

    this.client.clientVibrate()
    withTrack && this.gss.track('TICKLE', group.groupId, target.userId.toString())
      .subscribe({
        next: () => {
          this.toast.showToast(`我 拍了拍 **${target.name}**`)
        },
        error: e => {
          if (e.status === 0) {
            this.toast.showToast('网络错误')
            return
          }
          this.toast.showToast(this.getTickleErrorMessage(e.error.errors[0].code))
        },
      })

  }

  private getTickleErrorMessage(code: string): string {
    return this.TICKLE_ERROR_MESSAGE[code] || '拍一拍失败'
  }
}
