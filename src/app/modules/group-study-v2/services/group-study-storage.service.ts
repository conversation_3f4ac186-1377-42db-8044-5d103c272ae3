import { computed, inject, Injectable, OnDestroy, signal, WritableSignal } from '@angular/core'
import { Router } from '@angular/router'
import { captureException } from '@sentry/angular'

import { Big } from 'big.js'
import dayjs, { Dayjs } from 'dayjs'
import { BehaviorSubject, catchError, EMPTY, filter, firstValueFrom, interval, lastValueFrom, map, NEVER, Observable, of, retry, Subscription, switchMap, tap, throwError } from 'rxjs'
import { Modules } from '@maimemo/client-frontend-bridge'
import { ABTestingHelper } from '../../core/services/ab-testing.service'
import { UserService } from '../../core/services/user.service'
import { ACTIVE_GROUP_STATUS, INACTIVE_GROUP_STATUS, TEST_GROUP_LANTERN_2024 } from '../../shared/constants'
import { dayjsWithTZ, fromSeconds, toStudyDate } from '../../shared/helper/time'
import { ISODate, ListParams } from '../../shared/types'
import { Activity, ActivityDetail, ActivityType, OfficialGroupStudyActivityConfig } from '../models/activity.model'
import { ChatPermission } from '../models/chat.model'
import { Event, EventPostType, RefundPayload } from '../models/event.model'
import { Group, GroupData, GroupingStatusRes, GroupMeta, GroupReqParams, GroupRes, GroupStatus, GroupStudyPublishLog } from '../models/group.model'
import { Member } from '../models/member.model'
import { Report } from '../models/report.model'
import { GroupDisplayConfig } from '../pages/group-detail/config'
import { ColorifyStrategy, DATE_FORMAT, generateStudyStatsFromRecord, GROUP_STATUS_EARLY_LEARNING, GROUP_STATUS_LATER_LEARNING, StudyColorifier } from '../pages/study-detail/helper'
import { generateStudyReports, generateStudyStatsMap } from '../pages/study-report/helper'
import { MemberDailyStudyStats, MemberStudyReport, MemberStudyStats } from '../pages/study-report/type'
import { getCompareFn, isEqual } from '../utils/diff'
import { WorkerService } from '../../core/services/worker.service'
import { CalendarData, CalendarDateItem, checkIsMemberExitedAtSomeTime, DailyStatsData } from '../utils/study'
import { History } from '../models/history.model'
import { ChatService } from './chat.service'
import { GroupStudyNetworkService } from './group-study-network.service'
import { CreateActivityPayload, JoiningGroupParams } from './request.type'

export interface ActivityUIConfig {
  showInteractiveButton?: boolean
  hasUnreadMessage?: boolean
}

interface ActivitiesQueryOptions {
  activeOnly?: boolean
  lastViewFirst?: boolean

  /**
   * default: `true`
   */
  withOfficialActivities?: boolean

  /**
   * 为 true 时，将会在 没有活跃队伍 且 有处于报名期的官方活动 时返回 报名期的官方活动
   */
  withOpeningOfficials?: boolean
}

@Injectable({
  providedIn: 'root',
})
export class GroupStudyStorageService implements OnDestroy {
  private network = inject(GroupStudyNetworkService)
  private chat = inject(ChatService)
  protected user = inject(UserService)
  private router = inject(Router)
  private abTesting = inject(ABTestingHelper)
  private workerService = inject(WorkerService)

  private readonly unitPricePerDay = 0.05
  private readonly routeBase = '/group-study/v2/'

  private readonly _pollingSubscriptionsMap = new Map<string, Subscription>()
  private readonly _memberStudySuccessDatesMap = new Map<number, Set<ISODate>>()
  private readonly _activityMap = new Map<string, Activity>()
  private readonly _activityUIConfigMap = new Map<string, WritableSignal<ActivityUIConfig>>()

  private readonly CODE_TO_AID = new Map<string, string>()
  private readonly AID_TO_CODE = new Map<string, string>()

  // key: groupId | activityId
  private readonly _groupDataMap = new Map<string, BehaviorSubject<GroupData | null>>()

  // key: groupId
  private readonly _groupReportsMap = new Map<string, Report[]>()
  private readonly _groupDailyStudyStatsMap = new Map<string, Record<number, MemberStudyStats>>()
  private readonly _groupFinalReportsMap = new Map<string, MemberStudyReport[]>()
  private readonly _groupMemberMap = new Map<string, Record<number, Member>>()
  private readonly _groupChatPermissionsMap = new Map<string, ChatPermission[]>()

  /**
   * 队伍数据更新后的回调
   */
  private callbacksForGroupDataDelta: ((groupData: GroupData) => void)[] = []

  private _squareTestGroupType?: 'SHOW_GROUPING_SQUARE' | 'PUBLISH_GROUP'
  private _lanternTestGroup = signal<string | undefined>(undefined)
  private _commercialTestGroup = signal<'A' | 'B' | 'C'>('A')
  private _hasHistoryGroups = false

  private sensitiveControlGroup = signal('')
  isSensitiveControlOn = computed(() => this.sensitiveControlGroup() === 'ON')

  get defaultGroupingUnitPrice(): number {
    return this.unitPricePerDay
  }

  extraGroupSize = signal<number | undefined>(undefined)
  readonly maxGroupSizeLimit = 12

  get squareTestGroupType(): 'SHOW_GROUPING_SQUARE' | 'PUBLISH_GROUP' | undefined {
    return this._squareTestGroupType
  }

  get hasHistoryGroups() {
    return this._hasHistoryGroups
  }

  get isNewUserForGroupStudy() {
    return this.user.totalSignDays < 30 && !this.hasHistoryGroups
  }

  isLanternThemeVisible = computed(() => {
    return this._lanternTestGroup() === 'ON'
  })

  commercialTestGroup = this._commercialTestGroup.asReadonly()

  // private studyWorker = this.workerService.getWorker('study')

  promotionGroupIdToBanner = new Map<string, string>()

  constructor() {
    // 保存用户学习成功历史
    this.registerCallbackForGroupDataDelta(groupData => {
      const { history = [] } = groupData
      this.storeStudyHistory(history, this._memberStudySuccessDatesMap)
    })

    // UIConfig 相关
    this.registerCallbackForGroupDataDelta(async groupData => {
      const { events = [], group, inviteUserGroup } = groupData
      if (
        !group
        || (
          inviteUserGroup
          && inviteUserGroup.groupId !== group.groupId
        )
      ) {
        return
      }
      const uiConfig = this.getActivityUIConfig(group.activityId)

      // ======= 聊天功能入口 ==========
      const chatPermissions$ = this.chat.queryStudyGroupChatPermissions(group.groupId)
        .pipe(
          catchError(() => of<ChatPermission[]>([])),
          tap(chatPermissions => this._groupChatPermissionsMap.set(group.groupId, chatPermissions)),
        )

      const permissions = await firstValueFrom(chatPermissions$)

      // 有聊天功能的话直接显示
      if (permissions.some(v => v.action === 'READ' && v.permitted === true)) {
        uiConfig.update(v => {
          v.showInteractiveButton = true
          return v
        })
        return
      }
      // ======= 聊天功能入口 ==========

      if (group.members.every(v => v.userId !== this.user.userInfo?.userId)) {
        uiConfig.update(v => {
          v.showInteractiveButton = false
          return v
        })
        return
      }

      const allowStatus: GroupStatus[] = ['GROUPING', 'GROUP_SUCCEEDED', 'LEARN_SUCCEED', 'GROUP_FAILED', 'TESTING', 'TEST_FAILED', 'LEARNING']
      uiConfig.update(v => {
        v.showInteractiveButton = allowStatus.includes(group.status)
        return v
      })

      const eventsOfClickBoard: Event[] = []
      const eventsExcludeClickBoard: Event[] = []

      events.forEach(event => {
        if (event.targetId === 'INTERACTIVE_BOARD' || event.eventType === 'LIKE') {
          eventsOfClickBoard.push(event)
        }
        if (event.targetId !== 'INTERACTIVE_BOARD') {
          eventsExcludeClickBoard.push(event)
        }
      })

      const lastViewTime = eventsOfClickBoard
        .slice(-1)[0]?.createdTime

      if (!lastViewTime) {
        uiConfig.update(v => {
          v.hasUnreadMessage = true
          return v
        })
        return
      }
      if (eventsExcludeClickBoard.length === 0) {
        uiConfig.update(v => {
          v.hasUnreadMessage = false
          return v
        })
        return
      }
      const finalEventTime = eventsExcludeClickBoard[eventsExcludeClickBoard.length - 1].createdTime
      uiConfig.update(v => {
        v.hasUnreadMessage = dayjs(finalEventTime).valueOf() > dayjs(lastViewTime).valueOf()
        return v
      })
    })

    // 保存用户学习成功历史
    this.registerCallbackForGroupDataDelta(groupData => {
      const group = GroupStudyStorageService.getGroupFromGroupData(groupData)

      if (!group) {
        return
      }

      const memberMap = this._groupMemberMap.get(group.groupId) ?? {}

      group.members.forEach(v => {
        memberMap[v.userId] = v
      })

      this._groupMemberMap.set(group.groupId, memberMap)
    })

    this.initTestGroup()
    // this.initFestivalTestGroup()
    // this.initSensitiveControl()
    this.initCommercialTestGroup()
  }

  private initSensitiveControl() {
    this.abTesting.getTestGroup('SENSITIVE_CONTROL')
      .pipe(
        retry(3),
      )
      .subscribe(res => {
        this.sensitiveControlGroup.set(res.group ?? '')
      })
  }

  private initCommercialTestGroup() {
    this.abTesting.getTestGroup('GROUP_STUDY_PROMOTION_PROPOSAL_PERMISSION')
      .pipe(
        retry(3),
      )
      .subscribe(res => {
        const group = (res.group === 'B' || res.group === 'C')
          ? res.group
          : 'A'
        this._commercialTestGroup.set(group)
      })
  }

  private storeStudyHistory(history: History[], dataMap: Map<number, Set<string>>) {
    history.forEach(v => {
      if (v.studySucceededTime) {
        let dataSet = dataMap.get(v.memberId)
        if (!dataSet) {
          dataMap.set(v.memberId, new Set())
          dataSet = dataMap.get(v.memberId)
        }

        if (dataSet) {
          const formattedDate = toStudyDate(v.studySucceededTime).format(DATE_FORMAT)
          !dataSet.has(formattedDate) && dataSet.add(formattedDate)
        }
      }
    })
  }

  private initTestGroup() {
    this.abTesting.getTestGroup('GROUP_STUDY_CUSTOM')
      .subscribe(({ group }) => {
        if (!group || group === '') {
          this.extraGroupSize.set(undefined)
          return
        }

        this.extraGroupSize.set(parseInt(group) || undefined)
      })
  }

  private initFestivalTestGroup() {
    this.abTesting.getTestGroup(TEST_GROUP_LANTERN_2024)
      .subscribe(({ group }) => {
        this._lanternTestGroup.set(group)
      })
  }

  checkHistoryGroups() {
    return this.queryHistoryGroups()
      .pipe(
        tap(groups => {
          this._hasHistoryGroups = groups.length > 0
        }),
      )
  }

  getSquareTestGroup(): Observable<typeof this._squareTestGroupType> {
    if (this._squareTestGroupType) {
      return of(this._squareTestGroupType)
    }

    return this.abTesting.getTestGroup('GROUPING_SQUARE')
      .pipe(
        map(({ group }) => {
          const value = !group || group === ''
            ? undefined
            : group as Exclude<typeof this._squareTestGroupType, undefined>

          this._squareTestGroupType = value
          return value
        }),
      )
  }

  hasPermissionToChat(groupId: string): boolean {
    const permissions = this._groupChatPermissionsMap.get(groupId)

    if (!permissions) {
      return false
    }
    return permissions.some(v => v.action === 'READ' && v.permitted === true)
  }

  registerCallbackForGroupDataDelta(callback: (groupData: GroupData) => void) {
    this.callbacksForGroupDataDelta.push(callback)
  }

  /**
   * 计算组队价格
   *
   * @param days 天数
   * @param headCount 人数
   * @returns
   */
  calcGroupingPrice(days: number, unitPrice: number, headCount = 1): number {
    return Big(days).mul(headCount).mul(unitPrice).toNumber()
  }

  getGroupingPrice(config: CreateActivityPayload) {
    return this.network.getGroupingPrice(config)
  }

  createActivity(payload: CreateActivityPayload) {
    return this.network.createActivity(payload)
  }

  joinTheGroup(params: JoiningGroupParams) {
    return this.network.joinTheGroup(params)
  }

  storeInvitationCode(invitationCode: string, activityId: string) {
    this.CODE_TO_AID.set(invitationCode, activityId)
    this.AID_TO_CODE.set(activityId, invitationCode)
  }

  recordReadActivity(activityId: string) {
    return this.network.recordReadActivity(activityId)
  }

  queryActivities(opts: ActivitiesQueryOptions = {}): Observable<Activity<ActivityType>[]> {
    const {
      activeOnly = false,
      lastViewFirst = false,
      withOfficialActivities = true,
      withOpeningOfficials = false,
    } = opts
    return this.network.queryActivities()
      .pipe(
        map(activities => {
          if (!activeOnly) {
            return activities
          }

          const activeActivities: typeof activities = []
          let openingOfficialActivity: Activity<ActivityType> | undefined

          for (const activity of activities) {
            if (!activity.type.startsWith('GROUP_STUDY')) {
              continue
            }

            // 排除掉自己创建但未参与的自建队伍
            if (activity.type === 'GROUP_STUDY_CUSTOM') {
              const { config } = activity as Activity<'GROUP_STUDY_CUSTOM'>
              const isCreatedByCurrentUser = activity.creator === this.user.userInfo?.userId
              const isCreatorNotInTheGroup = !config?.creatorAutoJoin
              if (isCreatedByCurrentUser && isCreatorNotInTheGroup) {
                continue
              }
            } else {
              // 官方组队
              if (!withOfficialActivities) {
                continue
              }
            }

            // 用户未退出队伍 或 自己未领取奖励
            // 队伍处于 “组队中”、“磨合中”、“学习中” 或者 “学习成功，但处于学习期最后一天 ”
            const isUserActiveInActivity = activity.userStatus === 'DOING' || activity.userStatus === 'UNREWARDED'

            const isGroupActive = activity.userActivity?.groupStudy
              && GroupStudyStorageService.isGroupActive(activity.userActivity.groupStudy.groupStatus)

            const isActive = isUserActiveInActivity && isGroupActive

            if (withOpeningOfficials && GroupStudyStorageService.isOfficialActivityOpening(activity)) {
              if (!openingOfficialActivity) {
                openingOfficialActivity = activity
              }
            }

            if (!isActive) {
              continue
            }

            activeActivities.push(activity)
          }

          if (activeActivities.length === 0 && withOpeningOfficials && openingOfficialActivity) {
            return [openingOfficialActivity]
          }

          const result = activeActivities
            .sort((a, b) => +new Date(b.createdTime) - +new Date(a.createdTime))

          if (lastViewFirst) {
            result.sort((a, b) => +new Date(b.userActivity.lastReadTime) - +new Date(a.userActivity.lastReadTime))
          }

          return result
        }),
      )
  }

  queryActivityDetail(activityId: string): Observable<ActivityDetail> {
    return this.network.queryActivity(activityId)
      .pipe(
        tap(({ activity }) => this.checkActivityUpdates(activity)),
      )
  }

  queryGroupData(params: GroupReqParams, pure = false): Observable<GroupData> {
    return this.network.queryGroupData(params)
      .pipe(
        tap(groupData => !pure && this.checkDeltaGroupDataUpdates(groupData, 'activityId')),
      )
  }

  queryGroupById(groupId: string, pure = false): Observable<GroupData> {
    return this.network.queryGroupById(groupId)
      .pipe(
        tap(groupData => !pure && this.checkDeltaGroupDataUpdates(groupData, 'groupId')),
      )
  }

  queryHistoryGroups(params?: ListParams) {
    return this.network.queryHistoryGroups(params)
  }

  queryGroupingStatus(): Observable<GroupingStatusRes> {
    return this.network.queryGroupingStatus()
  }

  queryGroupReports(groupId: string): Observable<Report[]> {
    return this.network.queryGroupReports(groupId)
  }

  getGroupReports(groupId: string): Observable<Report[]> {
    if (this._groupReportsMap.has(groupId)) {
      return of(this._groupReportsMap.get(groupId)!)
    }

    return this.queryGroupReports(groupId)
      .pipe(
        tap(reports => this._groupReportsMap.set(groupId, reports)),
      )
  }

  queryActiveActivities(): Observable<Activity<ActivityType>[]> {
    return this.queryActivities({
      activeOnly: true,
      withOfficialActivities: false,
    })
  }

  getNextActiveActivity(group: Group): Observable<Activity<ActivityType>> {
    return this.queryActivities({
      activeOnly: true,
    }).pipe(
      switchMap(activities => {
        if (activities.length <= 1) {
          return NEVER
        }
        const currentActivityIndex = activities.findIndex(v => v.id === group.activityId)
        const nextActivityIndex = currentActivityIndex === activities.length - 1 ? 0 : currentActivityIndex + 1
        return of(activities[nextActivityIndex])
      }),
    )
  }

  clearReportsCache() {
    this._groupReportsMap.clear()
    this._groupFinalReportsMap.clear()
  }

  /**
   * 轮询组队数据
   * @param activityId
   * @returns
   */
  pollingToCheckUpdates(type: 'activity' | 'group', id: string): Observable<GroupData> {
    return interval(2000)
      .pipe(
        switchMap(() => {
          if (type === 'group') {
            return this.queryGroupById(id)
          }

          const params: GroupReqParams = {
            update_time: new Date().toISOString(),
          }

          // AID_TO_CODE 中有对应的 invitation_code 的话
          // 说明还未加入队伍
          const invitationCode = this.AID_TO_CODE.get(id)
          if (invitationCode) {
            params.invitation_code = invitationCode
          } else {
            params.activity_id = id
          }

          return this.queryGroupData(params)
        }),
      )
  }

  /**
   * 获取保存本地的队伍数据 Observable
   * @param idKey: activity_id | group_id
   * @returns
   */
  getGroupData$(idKey: string): BehaviorSubject<GroupData | null> {
    if (!this._groupDataMap.has(idKey)) {
      this._groupDataMap.set(idKey, new BehaviorSubject<GroupData | null>(null))
    }

    return this._groupDataMap.get(idKey)!
  }

  getActivity$(activityId: string): Observable<Activity> {
    if (this._activityMap.has(activityId)) {
      return of(this._activityMap.get(activityId)!)
    }

    return this.queryActivityDetail(activityId).pipe(
      map(res => res.activity),
    )
  }

  getActivityUIConfig(activityId: string): WritableSignal<ActivityUIConfig> {
    if (!this._activityUIConfigMap.has(activityId)) {
      this._activityUIConfigMap.set(activityId, signal({}))
    }

    return this._activityUIConfigMap.get(activityId)!
  }

  registerPollingSubscription(type: 'activity' | 'group', id: string) {
    if (this._pollingSubscriptionsMap.has(id)) {
      return
    }
    const subscription = this.pollingToCheckUpdates(type, id).subscribe()
    this._pollingSubscriptionsMap.set(id, subscription)
  }

  getInvitationCode(groupId: string) {
    return this.network.getGroupInvitation(groupId)
  }

  ngOnDestroy(): void {
    this.clearPollingSubscriptions()
    this.callbacksForGroupDataDelta.length = 0
    this.clearCaches()
  }

  clearPollingSubscriptions() {
    for (const sub of this._pollingSubscriptionsMap.values()) {
      sub.unsubscribe()
    }

    this._pollingSubscriptionsMap.clear()
  }

  clearCaches() {
    this.promotionGroupIdToBanner.clear()
    this.CODE_TO_AID.clear()
    this.AID_TO_CODE.clear()
    this._activityMap.clear()
    this._groupDataMap.clear()
    this._groupMemberMap.clear()
    this._groupReportsMap.clear()
    this._activityUIConfigMap.clear()
    this._groupFinalReportsMap.clear()
    this._groupDailyStudyStatsMap.clear()
    this._groupChatPermissionsMap.clear()
    this._memberStudySuccessDatesMap.clear()
  }

  isMemberFinishStudy(memberId: number, date: ISODate | Dayjs): boolean {
    const dataSet = this._memberStudySuccessDatesMap.get(memberId)
    if (!dataSet) {
      return false
    }

    const formattedDate = this.getFormattedStudyDay(date)
    return dataSet.has(formattedDate)
  }

  getFormattedStudyDay(date: ISODate | Dayjs): string {
    return toStudyDate(date).format(DATE_FORMAT)
  }

  getMemberInfoById(groupId: string, userId: number): Member | undefined {
    return this._groupMemberMap.get(groupId)?.[userId]
  }

  protected checkActivityUpdates(activity: Activity) {
    const stored = this._activityMap.get(activity.id)
    if (stored) {
      if (isEqual(stored, activity)) {
        return
      }
    }
    this._activityMap.set(activity.id, activity)
  }

  protected checkDeltaGroupDataUpdates(groupData: GroupData, storeKeyType: 'groupId' | 'activityId') {
    const group = GroupStudyStorageService.getGroupFromGroupData(groupData)
    if (!group) {
      return
    }
    const groupData$ = this.getGroupData$(group[storeKeyType])

    if (getCompareFn('groupData')(groupData$.value!, groupData)) {
      return
    }

    this.callbacksForGroupDataDelta.forEach(cb => cb(groupData))
    groupData$.next(groupData)
  }

  checkIsActivityCreator(uid: number | undefined, activityId: string): boolean {
    if (uid === undefined) {
      return false
    }
    return this._activityMap.has(activityId) && this._activityMap.get(activityId)?.creator === uid
  }

  track(eventType: EventPostType, groupId: string, target?: string): Observable<unknown> {
    return this.network.uploadEventTrack({
      event_type: eventType,
      group_id: groupId,
      target,
    })
  }

  generateCalendars(group: Group, activity: Activity): CalendarData {
    if (!group) {
      return {}
    }
    const activityConfig = activity?.config

    const result: CalendarData = {}

    // 确保是自建组队
    if (!activityConfig || 'registration_start_time' in activityConfig) {
      return result
    }
    const firstDate = toStudyDate(group.studyStartTime!).startOf('day')

    const calendars = Array.from({ length: fromSeconds(activityConfig.studyDuration, 'day') + 1 }, (_, i): CalendarDateItem => {
      const date = firstDate.clone().add(i, 'day')
      const formattedDate = date.format(DATE_FORMAT)
      const offsetToToday = Math.ceil(date.diff(toStudyDate(Date.now()), 'd', true))
      if (offsetToToday === 0) {
        result.todayIndex = i
      }

      return {
        date: date.toISOString(),
        formattedDate,
        offsetToToday,
        relativeIndex: i,
        desc: `第 ${i + 1} 天`,
      }
    })

    result.fullCalendars = calendars
    result.datesWithRecord = result.todayIndex !== undefined && result.todayIndex !== -1
      ? calendars.slice(0, result.todayIndex! + 1)
      : calendars

    if (calendars.length) {
      result.summaryDateItem = {
        date: '',
        formattedDate: `${calendars[0].formattedDate} - ${calendars[calendars.length - 1].formattedDate}`,
        offsetToToday: 999,
        relativeIndex: -2,
        desc: '累计获得',
      }
    }

    return result
  }

  generateFinalReport(groupData: GroupData, activity: Activity, reports: Report[]): MemberStudyReport[] {
    const group = GroupStudyStorageService.getGroupFromGroupData(groupData)

    if (!group) {
      return []
    }

    if (this._groupFinalReportsMap.has(group.groupId)) {
      return this._groupFinalReportsMap.get(group.groupId)!
    }

    const finalReports = generateStudyReports(groupData, activity, reports)
    this._groupFinalReportsMap.set(group.groupId, finalReports)

    return finalReports
  }

  generateDailyStudyStatsMap(groupData: GroupData, activity: Activity, preferCache = false): Record<number, MemberStudyStats> {
    const group = GroupStudyStorageService.getGroupFromGroupData(groupData)

    if (!group) {
      return {}
    }

    if (preferCache && this._groupDailyStudyStatsMap.has(group.groupId)) {
      return this._groupDailyStudyStatsMap.get(group.groupId)!
    }

    const statsMap = generateStudyStatsMap(groupData, activity)
    this._groupDailyStudyStatsMap.set(group.groupId, statsMap)

    return statsMap
  }

  checkIsMemberTheCurrentUser(userId: number) {
    return userId === this.user.userInfo?.userId
  }

  navigateByGroupStatus(group: Group, opts: { meta?: GroupMeta; shouldShowGroupDetailFirst?: boolean } = {}) {
    let url = this.routeBase
    const {
      meta,
      shouldShowGroupDetailFirst = false,
    } = opts
    // 当前用户有活跃中的官方队伍时，会从自建组队切换到官方组队
    if (group.type !== 'CUSTOM') {
      const v1Url = this.routeBase.replace('v2', 'v1')
      this.router.navigate([v1Url], {
        queryParamsHandling: 'merge',
        replaceUrl: true,
      })
      return
    }

    const params: Record<string, unknown> = {}
    const currentUserId = this.user.userInfo?.userId
    const isCurrentUserInGroup = group.members.some(member => member.userId === currentUserId)

    if (isCurrentUserInGroup) {
      if (GROUP_STATUS_LATER_LEARNING.includes(group.status)) {
        url = this.routeBase + 'study'
      }

      if (GROUP_STATUS_EARLY_LEARNING.includes(group.status) && group.groupSucceededTime) {
        if (shouldShowGroupDetailFirst) {
          url = this.routeBase
        }
      }
    }

    if (meta && isCurrentUserInGroup) {
      params.activity_id = this.getActivityIdFromMeta(meta)

      // 一般就是在加入队伍的时候会为 true
      if (meta.invitation_code) {
        const activityId = this.CODE_TO_AID.get(meta.invitation_code)
        if (activityId) {
          this.CODE_TO_AID.delete(meta.invitation_code)
          this.AID_TO_CODE.delete(activityId)
          params.invitation_code = null
        }
      } else if (meta.group_id) {
        params.activity_id = group.activityId
        params.group_id = null
      }
    }

    this.router.navigate([url], {
      replaceUrl: true,
      queryParams: params,
      queryParamsHandling: 'merge',
    })
  }

  getActivityIdFromMeta(meta: GroupMeta): string | undefined {
    if (meta.activity_id) {
      return meta.activity_id
    }

    if (meta.invitation_code) {
      return this.CODE_TO_AID.get(meta.invitation_code)
    }

    return undefined
  }

  /**
   * 减少重复代码，根据业务逻辑，两个字段一般不会同时有值
   * @param groupData
   * @returns
   */
  static getGroupFromGroupData(groupData: GroupData): Group | null {
    return groupData
      ? groupData.inviteUserGroup ?? groupData.group
      : null
  }

  static getRefundInfoFromEvent(event: Event): RefundPayload {
    try {
      const refundInfo: RefundPayload | number = JSON.parse(event.comment ?? '0')
      let refundedAmount = 0
      if (typeof refundInfo === 'number') {
        refundedAmount = refundInfo
      } else if (typeof refundInfo === 'object') {
        return refundInfo
      }

      return {
        amount: refundedAmount.toString(),
        members_count: '1',
        refund_status: 'REFUNDED',
      }
    } catch (error) {
      console.error(error)

      return {
        amount: '0',
        members_count: '1',
        refund_status: 'REFUNDED',
      }
    }
  }

  getGroupDataByGroupMeta(meta: GroupMeta, shouldCheckRedirect = false): Observable<GroupData> {
    if (!meta || Object.keys(meta).length === 0) {
      return EMPTY
    }

    const { invitation_code, activity_id, group_id } = meta
    let activityId = activity_id ?? ''
    const isQueryByGroupId = !invitation_code && !activityId && !!group_id

    if (invitation_code) {
      activityId = this.CODE_TO_AID.get(invitation_code) ?? activityId
    }

    const storedId = isQueryByGroupId ? group_id : activityId

    // 非首次加载的话，直接返回保存的轮询 Observable
    if (
      this._groupDataMap.has(storedId)
      && this._groupDataMap.get(storedId)!.getValue() !== null
      && this._pollingSubscriptionsMap.has(storedId)
    ) {
      return this._groupDataMap.get(storedId) as Observable<GroupData>
    }

    const action$ = isQueryByGroupId
      ? this.queryGroupById(group_id)
      : this.queryGroupData(meta)

    return action$
      .pipe(
        map(groupData => {
          const group = GroupStudyStorageService.getGroupFromGroupData(groupData)
          if (group) {
            // 保存邀请码和活动 ID 的映射
            invitation_code && this.storeInvitationCode(invitation_code, group.activityId)

            // 开始轮询
            !INACTIVE_GROUP_STATUS.includes(group.status) && this.registerPollingSubscription(
              isQueryByGroupId ? 'group' : 'activity',
              isQueryByGroupId ? group.groupId : group.activityId,
            )

            // 检查是否需要重定向
            shouldCheckRedirect && this.navigateByGroupStatus(group, { meta, shouldShowGroupDetailFirst: !!groupData.showGroupSucceededAnimation })
          }

          return group
        }),
        switchMap(group =>
          !group
            ? throwError(() => new Error('没有队伍信息'))
            : this.getGroupData$(isQueryByGroupId ? group.groupId : group.activityId)
                .pipe(
                  filter(Boolean),
                ),
        ),
        retry({
          count: 10,
          delay: 2000,
        }),
      )
  }

  filterMembersByConfig(members: Member[], activity: Activity): Member[] {
    // 如果是创建者不在队伍的话，需要从 members 里去掉
    if (activity && activity.config?.creatorAutoJoin === false) {
      return members.filter(v => v.userId !== activity.creator)
    }

    return members
  }

  getRewards(groupId: string) {
    return this.network.getRewards(groupId)
  }

  getDisplayConfigByGroup(group: Group): GroupDisplayConfig {
    if (!group) {
      return {
        statusText: '未有队伍信息',
        statusDesc: '',
      }
    }

    const result: GroupDisplayConfig = {
      statusText: '',
      statusDesc: '',
    }

    if (group.status === 'GROUP_FAILED') {
      result.statusText = '组队失败'
      result.statusDesc = '报名队伍人数不满足组队条件'
    } else if (group.status === 'GROUPING') {
      result.statusText = '组队中'
    } else if (group.status === 'GROUP_SUCCEEDED') {
      result.statusText = '组队成功'
    } else if (group.status === 'TEST_FAILED') {
      result.statusText = '组队失败'
      result.statusDesc = '队伍未通过磨合期'
    } else if (group.status === 'LEARNING' || group.status === 'TESTING') {
      result.statusText = '学习中'
    } else if (group.status === 'GROUP_DISMISSED') {
      result.statusText = '组队失败'
    }

    return result
  }

  static isGroupActive(groupStatus: GroupStatus) {
    return ACTIVE_GROUP_STATUS.includes(groupStatus)
  }

  /**
   * 判断官方活动是否正在开放创建队伍
   */
  static isOfficialActivityOpening(activity: Activity<ActivityType>): boolean {
    if (activity.type === 'GROUP_STUDY_CUSTOM' || !activity.config) {
      return false
    }
    const activityConfig = activity.config as OfficialGroupStudyActivityConfig
    const today = dayjsWithTZ(Date.now())
    const startTime = activityConfig.registrationStartTime
    const endTime = activityConfig.registrationEndTime

    return !today.isBefore(startTime) && today.isBefore(endTime)
  }

  publishGroup(groupId: string): Observable<GroupStudyPublishLog> {
    return this.network.publishGroup(groupId)
  }

  recallGroupPublish(groupId: string): Observable<unknown> {
    return this.network.recallGroupPublish(groupId)
  }

  dismissGroup(groupId: string): Observable<GroupRes> {
    return this.network.dismissGroup(groupId)
  }

  async checkGroupingPermission() {
    try {
      const activeActivities = await lastValueFrom(this.queryActiveActivities().pipe(retry(3)))

      if (activeActivities.length >= 1) {
        return !this.user.checkDebtLearning()
      }

      return true
    } catch (error) {
      console.error(error)
      captureException(error)
      return false
    }
  }

  async checkShouldHidePaymentInfo() {
    if (await this.user.checkShouldHidePaymentInfo()) {
      Modules.common.alert({
        title: '自由组队出错了',
        message: '',
        buttons: [
          {
            id: 'confirm',
            text: '我知道了',
          },
        ],
        button_callback: () => {
          // noop
        },
      })
      return true
    }

    return false
  }

  redirectToGroupListPage() {
    location.replace(`https://www.maimemo.com/pages/event-center/group-study-list?token=${this.user.legacyToken}&ref=activity-center`)
  }

  generateDailyStatsData(members: Member[], histories: History[], calendarItems: CalendarDateItem[]): DailyStatsData {
    /**
   * 记录每天每个用户的学习记录
   *
   * {
   *  [DATE_FORMAT]: {
   *    [userId]: MemberDailyStudyStats
   *  }
   * }
   */
    const statsMap: Record<string, Record<number, MemberDailyStudyStats>> = {}

    let isCurrentUserInGroup = false

    /**
     * 将 histories 用 object 分类储存，以便后面查找，避免多次遍历
     * {
     *  [userId]: {
     *    [DATE_FORMAT]: History
     *  }
     * }
     */
    const historiesGroupByUserIdAndFormattedDate = histories.reduce((acc, record) => {
      const stored: Record<string, History> = acc[record.memberId] || {}
      const formattedDate = toStudyDate(record.createdTime).format(DATE_FORMAT)
      stored[formattedDate] = record
      acc[record.memberId] = stored
      return acc
    }, {} as Record<number, Record<string, History>>)

    const colorifier = new StudyColorifier()

    // 确保每一天每个用户都有值
    for (const calendarDate of calendarItems) {
      const { formattedDate } = calendarDate
      const studyStatsMap = statsMap[formattedDate] || {}
      let isDependingOnRewardedAmount = false

      colorifier.reset()

      // 获取每个用户的学习记录
      members.forEach((member, index) => {
        const memberStudyRecord = historiesGroupByUserIdAndFormattedDate[member.userId]?.[formattedDate]
        const isCurrentUser = member.isCurrentUser || this.checkIsMemberTheCurrentUser(member.userId)
        const studyStats = memberStudyRecord
          ? generateStudyStatsFromRecord(memberStudyRecord, member)
          : {
              avatar: member.avatar,
              userId: member.userId,
              name: member.name,
              hasSigned: false,
              isFinished: false,
              isExited: checkIsMemberExitedAtSomeTime(member, calendarDate.date),
            }

        if (isCurrentUser && !isCurrentUserInGroup) {
          isCurrentUserInGroup = true
        }

        // 已退出的用户会排在队伍最后，因此不加入染色队伍
        !studyStats.isExited && colorifier.registerStats(studyStats)

        if (!isDependingOnRewardedAmount && studyStats.studyLog?.rewardReason === 'CUSTOM_FIRST_DAY') {
          isDependingOnRewardedAmount = true
        }

        studyStatsMap[member.userId] = studyStats
      })

      statsMap[formattedDate] = studyStatsMap

      colorifier.resolvePendingStats(
        isDependingOnRewardedAmount
          ? ColorifyStrategy.REWARD
          : ColorifyStrategy.SIGN_AVATAR,
      )
    }

    return {
      statsMap,
      isCurrentUserInGroup,
    }
  }
}
