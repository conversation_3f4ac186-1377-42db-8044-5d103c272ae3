import { Injectable, inject } from '@angular/core'
import { Modules, isBelowSpecifiedAppVersion } from '@maimemo/client-frontend-bridge'
import { MemoAlertService } from '../../ui/alert/alert.service'

@Injectable({
  providedIn: 'root',
})
export class GroupStudyUIService {
  private alert = inject(MemoAlertService)

  showOutOfLimitModal(opts: { max: number; currentCount: number; title?: string; isCreate?: boolean }) {
    const { max, currentCount, title = '无法加入新队伍', isCreate = false } = opts

    if (isBelowSpecifiedAppVersion('5.2.40')) {
      return this.alert.show({
        header: title,
        message: `你最多只能同时参与 ${max} 个自由组队。`,
        cssClass: 'memo-alert',
        buttons: [{
          text: '确定',
        }],
      })
    }

    return Modules.userLevel.showPrivilegeModal({
      code: isCreate ? 'initiate_group_study' : 'participate_group_study',
      limit: max,
      over_limit: true,
      current_count: currentCount,
    })
  }
}
