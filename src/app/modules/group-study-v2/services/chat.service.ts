import { HttpClient } from '@angular/common/http'
import { Injectable, inject } from '@angular/core'
import { isBelowSpecifiedAppVersion } from '@maimemo/client-frontend-bridge'
import { Observable, map } from 'rxjs'
import { Response } from '../../shared/types'
import { MemoAlertService } from '../../ui/alert/alert.service'
import { ChatPermission, MessagesConfigQueryParams } from '../models/chat.model'
import { Group } from '../models/group.model'

@Injectable({
  providedIn: 'root',
})
export class ChatService {

  private readonly prefix = '/pages/activity-center'

  private http = inject(HttpClient)
  private alert = inject(MemoAlertService)

  queryStudyGroupChatPermissions(groupId: string): Observable<ChatPermission[]> {
    const params: MessagesConfigQueryParams = {
      source_type: 'GROUP_STUDY_GROUP',
      source_id: groupId,
      scope: 'PERMISSIONS',
    }
    return this.http.get<Response<{permissions: ChatPermission[]}>>(`${this.prefix}/api/v1/messages/config`, {
      params: {
        ...params,
      },
    })
      .pipe(
        map(res => res.data.permissions),
      )
  }

  async navigateToClientChat(group: Group, groupSize: number) {
    if (isBelowSpecifiedAppVersion('5.2.0')) {
      return this.alert.show({
        header: '请升级到最新版本',
        message: '请更新到最新版本以使用此功能',
        buttons: [{
          role: 'cancel',
          text: '确定',
        }],
        cssClass: ['memo-alert', 'warning-alert'],
      })
    }

    const pageTitle = `${group.name}(${groupSize})`
    return (location.href = `memo://jump?page=chat_group_study&group_id=${group.groupId}&title=${encodeURIComponent(pageTitle)}`)
  }
}
