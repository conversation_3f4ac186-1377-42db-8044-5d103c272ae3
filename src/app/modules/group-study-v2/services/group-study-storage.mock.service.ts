import { Injectable } from '@angular/core'
import { Observable, of, tap } from 'rxjs'
import { mockActivity, mockGroupData } from '../../group-study/mock'
import { fromResData } from '../../shared/helper/utils'
import { ActivityDetail } from '../models/activity.model'
import { GroupData, GroupReqParams } from '../models/group.model'
import { Report } from '../models/report.model'
import { GroupStudyStorageService } from './group-study-storage.service'

@Injectable({
  providedIn: 'root',
})
export class GroupStudyStorageMockService extends GroupStudyStorageService {

  override queryGroupData(params: GroupReqParams): Observable<GroupData> {
    return of(fromResData(mockGroupData))
      .pipe(
        tap(groupData => this.checkDeltaGroupDataUpdates(groupData, 'groupId')),
      )
  }

  override queryActivityDetail(activityId: string): Observable<ActivityDetail> {
    return of(fromResData(mockActivity))
      .pipe(
        tap(({ activity }) => this.checkActivityUpdates(activity)),
      )
  }

  override queryGroupReports(groupId: string): Observable<Report[]> {
    return of(fromResData(
      mockGroupData.group!.members.map((member, index) => {
        return {
          _id: index + '',
          activity_id: mockActivity.activity._id,
          group_id: mockGroupData.group!.group_id,
          creator: member.user_id,
          familiar_voc_count: Math.floor(member.user_id / 900),
          created_time: '',
          updated_time: '',
        }
      })))
  }

}
