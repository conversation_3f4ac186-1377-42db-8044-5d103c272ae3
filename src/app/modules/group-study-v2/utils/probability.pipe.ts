import { Pipe, PipeTransform } from '@angular/core'
import { Big } from 'big.js'

const PER_DICT = {
  cent: {
    multiplier: 100,
    char: '%',
  },
  mil: {
    multiplier: 1000,
    char: '‰',
  },
  bp: {
    multiplier: 10000,
    char: '‱',
  },
}

@Pipe({
  name: 'probability',
  standalone: false,
})
export class ProbabilityPipe implements PipeTransform {
  transform(value: number): string {
    if (value === undefined) {
      return ''
    }

    const v = Big(value)

    if (v.gte(0.0005)) {
      return this.toText(v, 'cent', 3)
    }

    if (v.lte(0.0000001)) {
      return this.toText(v, 'bp', 4)
    }

    if (v.lte(0.000005)) {
      return this.toText(v, 'bp', 3)
    }

    if (v.lte(0.000001)) {
      return this.toText(v, 'mil', 4)
    }

    return this.toText(v, 'mil', 3)
  }

  private toText(value: number | Big, per: keyof typeof PER_DICT, digit: number) {
    const v = value instanceof Big ? value : Big(value)
    const { multiplier, char } = PER_DICT[per]

    return `${v.mul(multiplier).toFixed(digit, 1)}${char}`
  }
}
