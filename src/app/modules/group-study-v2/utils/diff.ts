import { dequal } from 'dequal'
import { Group } from '../models/group.model'
import { Member } from '../models/member.model'

type ComparableName = 'group' | 'members' | 'groupData' | 'deepEqual'

function deepEqualCompareFn<T extends object>(a: T, b: T): boolean {
  return dequal(a, b)
}

function groupCompareFn(a: Group, b: Group): boolean {
  const { updatedTime: aUT, members: aMembers, ...aRest } = a
  const { updatedTime: bUT, members: bMembers, ...bRest } = b
  return dequal(aRest, bRest)
}

function membersCompareFn(a: Member[], b: Member[]): boolean {
  // lastReadTime 经常会变
  const aList = a.map(({ lastReadTime, ...rest }) => rest)
  const bList = b.map(({ lastReadTime, ...rest }) => rest)
  return dequal(aList, bList)
}

const COMPARE_FN_DICT = {
  group: groupCompareFn,
  groupData: deepEqualCompareFn,
  members: membersCompareFn,
  deepEqual: deepEqualCompareFn,
}

export function getCompareFn<T extends ComparableName>(itemType: T): typeof COMPARE_FN_DICT[T] {
  return COMPARE_FN_DICT[itemType]
}

export const isEqual = getCompareFn('deepEqual')
