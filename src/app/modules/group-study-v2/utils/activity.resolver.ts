import { Injectable } from '@angular/core'
import { ActivatedRouteSnapshot, ParamMap, RouterStateSnapshot } from '@angular/router'
import { GroupMeta } from '../models/group.model'

@Injectable({
  providedIn: 'root',
})
export class ActivityResolver {
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): GroupMeta {
    return resolveGroupMetaFromQueryParams(route.queryParamMap)
  }
}

export function resolveGroupMetaFromQueryParams(paramsMap: ParamMap): GroupMeta {
  const activity_id = paramsMap.get('activityId') || paramsMap.get('activity_id')
  const invitation_code = paramsMap.get('invitationCode') || paramsMap.get('invitation_code')
  const group_id = paramsMap.get('groupId') || paramsMap.get('group_id')

  if (!activity_id && !invitation_code && !group_id) {
    return {}
  }

  return {
    activity_id: activity_id || '',
    invitation_code: invitation_code || '',
    group_id: group_id || '',
  }
}
