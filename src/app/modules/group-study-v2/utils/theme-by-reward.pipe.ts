import { Pipe, PipeTransform } from '@angular/core'

import { Big } from 'big.js'
import { getColorRGBFromColorType } from '../../shared/helper/color'

@Pipe({
  name: 'themeByReward',
  standalone: false,
})
export class ThemeByRewardPipe implements PipeTransform {
  transform(value: number, days: number) {
    if (!value || !days) {
      return getColorRGBFromColorType('grey-2')
    }

    const v = Big(value).div(days)

    if (v.gte(4.5)) {
      return getColorRGBFromColorType('red')
    }

    if (v.lte(3)) {
      return getColorRGBFromColorType('green-1')
    }

    return getColorRGBFromColorType('yellow')
  }
}
