import { Pipe, PipeTransform } from '@angular/core'
import { toStudyDate } from '../../shared/helper/time'

@Pipe({
  name: 'studyDate',
  standalone: false,
})
export class StudyDatePipe implements PipeTransform {
  transform(value: string, ...args: any[]) {
    if (!value) {
      return ''
    }

    const date = toStudyDate(value)
    if (date.isSame(toStudyDate(Date.now()), 'd')) {
      return '今天'
    }

    return date.format('MM.DD')
  }
}
