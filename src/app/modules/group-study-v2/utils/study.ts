import dayjs, { Dayjs, isDayjs } from 'dayjs'
import { toStudyDate } from '../../shared/helper/time'
import { Member } from '../models/member.model'
import { MemberDailyStudyStats } from '../pages/study-report/type'
import { ISODate } from '../../shared/types'

export interface DailyStatsData {
  statsMap: Record<string, Record<number, MemberDailyStudyStats>>
  isCurrentUserInGroup: boolean
}

export interface CalendarDateItem {

  /**
   * 具体某一天的 ISODate
   */
  date: ISODate

  /**
   * 按照 DATE_FORMAT 格式化的字符串
   */
  formattedDate: string

  /**
   * 与今天的偏移
   */
  offsetToToday: number

  /**
   * 开始学习的第几天，总结块的索引为 -2
   */
  relativeIndex: number

  /**
   * 文字描述
   */
  desc: string
}

export interface CalendarData {
  fullCalendars?: CalendarDateItem[]
  datesWithRecord?: CalendarDateItem[]
  summaryDateItem?: CalendarDateItem
  todayIndex?: number
}

export const MAX_REWARD = 9600

/**
 *
 * @param member
 * @param date 确保该参数是 studyDate ，偏移 4 小时的值
 * @returns
 */
export function checkIsMemberExitedAtSomeTime(member: Member, date: string | number | Dayjs) {
  const d = isDayjs(date) ? date : dayjs(date)
  if (!member.exitedTime) {
    return false
  }
  return !d.isBefore(toStudyDate(member.exitedTime), 'd')
}
