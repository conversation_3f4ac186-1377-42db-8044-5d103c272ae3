import { CdkTableModule } from '@angular/cdk/table'
import { CommonModule, Location, NgOptimizedImage } from '@angular/common'
import { CUSTOM_ELEMENTS_SCHEMA, NgModule, inject } from '@angular/core'
import { ReactiveFormsModule } from '@angular/forms'
import { CanActivateFn, RouterModule, Routes } from '@angular/router'
import { Modules } from '@maimemo/client-frontend-bridge'
import { SvgIconComponent } from '@ngneat/svg-icon'
import { RxFor } from '@rx-angular/template/for'
import Swiper, { Controller, Virtual } from 'swiper'
import { SwiperModule } from 'swiper/angular'
import { MemoSpinnerComponent } from 'src/app/modules/ui/spinner/spinner.component'
import { StorageService } from '../core/services/storage.service'
import { ROUTE_BASE_HREF } from '../entry/group-study.module'
import { GroupStudyCommonModule } from '../shared/modules/group-study/group-study-common.module'
import { ListPipe } from '../shared/pipes/list.pipe'
import { OrderBy } from '../shared/pipes/order-by.pipe'
import { UiModule } from '../ui/ui.module'
import { FlatListComponent } from '../ui/flat-list/flat-list.component'
import { PullToRefreshComponent } from '../ui/pull-to-refresh/pull-to-refresh.component'
import { CommercialBannerComponent } from '../shared/components/commercial-banner/commercial-banner.component'
import { ActivityGuideComponent } from './components/activity-guide/activity-guide.component'
import { AwardRulesComponent } from './components/award-rules/award-rules.component'
import { BoardMessageComponent } from './components/board-message/board-message.component'
import { CalendarDateMemberComponent } from './components/calendar-date-member/calendar-date-member.component'
import { CalendarDateComponent } from './components/calendar-date/calendar-date.component'
import { CalendarComponent } from './components/calendar/calendar.component'
import { CouponExchangeComponent } from './components/coupon-exchange/coupon-exchange.component'
import { GroupListItemComponent } from './components/group-list-item/group-list-item.component'
import { InteractiveBoardComponent } from './components/interactive-board/interactive-board.component'
import { MemberAvatarComponent } from './components/member-avatar/member-avatar.component'
import { ShareGroupComponent } from './components/share-group/share-group.component'
import { SignAvatarComponent } from './components/sign-avatar/sign-avatar.component'
import { StudyStatsCardComponent } from './components/study-stats-card/study-stats-card.component'
import { StudyStatsListItemComponent } from './components/study-stats-list-item/study-stats-list-item.component'
import { GroupDetailComponent } from './pages/group-detail/group-detail.component'
import { GroupMembersComponent } from './pages/group-members/group-members.component'
import { GroupSetupComponent } from './pages/group-setup/group-setup.component'
import { GroupSquareComponent } from './pages/group-square/group-square.component'
import { IndexComponent } from './pages/index/index.component'
import { RewardProbabilityComponent } from './pages/reward-probability/reward-probability.component'
import { StudyDetailComponent } from './pages/study-detail/study-detail.component'
import { StudyReportComponent } from './pages/study-report/study-report.component'
import { ActivityResolver } from './utils/activity.resolver'
import { ProbabilityPipe } from './utils/probability.pipe'
import { StudyDatePipe } from './utils/study-date.pipe'
import { ThemeByRewardPipe } from './utils/theme-by-reward.pipe'

Swiper.use([Controller, Virtual])

export interface MemoRouteMeta {

  /**
   * 是否展示页面下方的规则
   */
  showRules: boolean

  /**
   * 是否需要根据队伍状态来做重定向页面
   */
  checkRedirect: boolean

  keepAlive?: boolean
}

const routes: Routes = [
  {
    path: '',
    component: IndexComponent,
    children: [
      {
        path: 'setup',
        component: GroupSetupComponent,
        title: '自建组队',
        data: {
          showRules: true,
        },
      },
      {
        path: 'members',
        component: GroupMembersComponent,
        data: {
          showRules: false,
        },
        resolve: {
          groupMeta: ActivityResolver,
        },
      },
      {
        path: 'study',
        component: StudyDetailComponent,
        data: {
          showRules: true,
          checkRedirect: true,
        },
        resolve: {
          groupMeta: ActivityResolver,
        },
      },
      {
        path: 'report',
        component: StudyReportComponent,
        data: {
          checkRedirect: false,
        },
        resolve: {
          groupMeta: ActivityResolver,
        },
      },
      {
        path: 'square',
        component: GroupSquareComponent,
        title: '自由组队',
        canActivate: [
          /**
           * 业务需求
           * 从广场进入创建队伍页面，创建成功后
           * 点击返回会忽略广场页面
           */
          () => {
            const storage = inject(StorageService)
            const _location = inject(Location)

            if (storage.ignoreSquarePage) {
              storage.updateIsIgnoreSquarePage(false)
              // length === 2 说明 square page 是第一个会话
              if (history.length === 2) {
                Modules.common.finish()
              } else {
                _location.back()
              }
              return false
            }
            return true
          },
        ] as CanActivateFn[],
        data: {
          keepAlive: true,
        },
      },
      {
        path: 'reward-probability',
        component: RewardProbabilityComponent,
        title: '组队概率',
      },
      {
        path: '',
        pathMatch: 'full',
        component: GroupDetailComponent,
        data: {
          showRules: true,
          checkRedirect: true,
        },
        resolve: {
          groupMeta: ActivityResolver,
        },
      },
    ],
  },
]

@NgModule({
  declarations: [
    IndexComponent,
    AwardRulesComponent,
    ActivityGuideComponent,
    GroupSetupComponent,
    GroupDetailComponent,
    MemberAvatarComponent,
    StudyReportComponent,
    StudyDetailComponent,
    CalendarDateComponent,
    SignAvatarComponent,
    StudyStatsCardComponent,
    StudyStatsListItemComponent,
    StudyDatePipe,
    ThemeByRewardPipe,
    ProbabilityPipe,
    CalendarComponent,
    CalendarDateMemberComponent,
    GroupMembersComponent,
    InteractiveBoardComponent,
    BoardMessageComponent,
    RewardProbabilityComponent,
    GroupSquareComponent,
    GroupListItemComponent,
    CouponExchangeComponent,
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    UiModule,
    CdkTableModule,
    SwiperModule,
    RxFor,
    OrderBy,
    ListPipe,
    SvgIconComponent,
    GroupStudyCommonModule,
    FlatListComponent,
    PullToRefreshComponent,
    ShareGroupComponent,
    NgOptimizedImage,
    CommercialBannerComponent,
    MemoSpinnerComponent,
  ],
  providers: [
    {
      provide: ROUTE_BASE_HREF,
      useValue: '/group-study/v2/',
    },
  ],
  schemas: [
    CUSTOM_ELEMENTS_SCHEMA,
  ],
})
export class GroupStudyModule { }
