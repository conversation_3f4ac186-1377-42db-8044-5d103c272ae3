@if (isCommercialGroup()) {
  <memo-commercial-banner
    [withViewTransition]="true"
    [style]="{
      width: 'calc(100% - var(--gap-base) * 6)',
      borderRadius: '10px',
      marginBottom: 'calc(var(--gap-base) * 2)',
    }"
    [src]="commercialBanner()"
    [link]="commercialBannerLink()"
    [groupId]="group()?.id || ''"
    [proposalId]="activity()?.proposalId || ''"
  ></memo-commercial-banner>
}

<app-card
  [showInteractiveButton]="isInteractiveButtonVisible()"
  [hasUnreadMessage]="hasUnreadMessage()"
  [showSwitchGroupButton]="!!nextActiveActivity()"
  [showDismissButton]="!isCommercialGroup() && isGroupDismissable()"
  (clickFloatingButton)="onClickFloatingButton($event)"
  (dismiss)="onClickDismiss()"
>
  @if (isInitialized()) {
    <div class="group-detail-container">
      @if (reasonOfNotOpenForJoin() === '') {
        <section class="group-detail" [class.failed]="isGroupingFailed()">
          <section class="group-tips">
            @if (!isGroupTestFailed()) {
              <header>
                @if (groupDisplayConfig()) {
                  {{ groupDisplayConfig()!.statusText }} ({{ groupMembersRate() }})
                }
              </header>
              @if (group()?.status !== 'GROUP_FAILED') {
                <p class="text-main">{{ countDownText() }}</p>
                <p class="text-info">{{ group()?.status === 'GROUP_SUCCEEDED' ? '后开始组队学习' : '组队倒计时' }}</p>
              } @else {
                @if (groupDisplayConfig(); as displayConfig) {
                  <p class="text-main">
                    {{ countDownText() }}
                  </p>
                  <p class="text-info">
                    {{ displayConfig.statusDesc }}
                    @if (refundDesc$ | async; as refundDesc) {
                      ，<br />
                      {{ refundDesc }}
                    }
                  </p>
                }
              }
            } @else {
              @if (groupDisplayConfig(); as displayConfig) {
                <p class="text-main">
                  {{ displayConfig.statusText }}
                </p>
                <p class="text-info">
                  {{ displayConfig.statusDesc }}
                  @if (refundDesc$ | async; as refundDesc) {
                    ，<br />
                    {{ refundDesc }}
                  }
                </p>
              }
            }
          </section>
          <ng-container [ngTemplateOutlet]="groupConditionsTmpl"></ng-container>
          <div class="members-container">
            @if (activityConfig()?.groupSize; as groupSize) {
              <div class="members-grid" [style.--cols]="membersColCount()">
                @for (item of groupSize | list; track item; let i = $index) {
                  @if (membersSeatingMap().get(item); as member) {
                    <div class="member-info">
                      <app-member-avatar [member]="member"></app-member-avatar>
                      <span class="member-name truncate">{{ member.name }}</span>
                      @if (isGroupTestFailed()) {
                        <span class="member-status" [class.failed]="!getMemberIsFinish(member.userId)">
                          {{ getMemberIsFinish(member.userId) ? '已完成' : '未完成' }}
                        </span>
                      }
                    </div>
                  } @else {
                    <div
                      class="member-info seat-placeholder"
                      [hidden]="!(targetPosition() === i && userInfo()) || isCurrentUserInGroup()"
                    >
                      <app-member-avatar [imgSrc]="userInfo()?.avatar"></app-member-avatar>
                      <span class="member-name truncate">选此位</span>
                    </div>
                    @if (!isGroupingFailed()) {
                      <div
                        [hidden]="targetPosition() === i"
                        class="member-info"
                        (click)="isAbleToAddMember() && onClickEmptyMember(i)"
                      >
                        <app-member-avatar imgSrc="plus"></app-member-avatar>
                        <span class="member-name truncate">{{ '&nbsp;' }}</span>
                      </div>
                    }
                  }
                }
              </div>
            }
          </div>
        </section>
        @if (isAbleToAddMember()) {
          <footer class="group-handles" #footerRef>
            <ng-container
              [ngTemplateOutlet]="isAbleToInviteMember() ? creatorHandlersTmpl : memberHandlersTmpl"
            ></ng-container>
          </footer>
        }
      } @else {
        <section class="group-detail">
          <section class="group-tips">
            <p class="text-main" [style.padding-top.px]="33" [style.padding-bottom.px]="54">
              {{ reasonOfNotOpenForJoin() }}
            </p>
          </section>
        </section>
        @if (canSetupCustomGroup) {
          <footer class="group-handles">
            <button width="80%" memo-button (memoClick)="onClickSetupGroup()">自建组队</button>
          </footer>
        }
      }
    </div>
  } @else {
    <div class="group-detail-container">
      <section class="group-detail">
        <section class="group-tips">
          <header style="font-size: 24px">
            <ion-skeleton-text style="width: 5em; margin: 0 auto" [animated]="true"></ion-skeleton-text>
          </header>
          <p class="text-main">
            <ion-skeleton-text style="width: 5em; margin: 0 auto" [animated]="true"></ion-skeleton-text>
          </p>
          <p style="font-size: 20px">
            <ion-skeleton-text style="width: 5em; margin: 0 auto" [animated]="true"></ion-skeleton-text>
          </p>
        </section>
        <ion-skeleton-text class="group-conditions" [animated]="true"></ion-skeleton-text>
        <div class="members-container">
          <div class="members-grid" [style.--cols]="5">
            @for (item of blankMembers; track $index) {
              <div class="member-info">
                <ion-thumbnail>
                  <ion-skeleton-text [animated]="true"></ion-skeleton-text>
                </ion-thumbnail>
                <span class="member-name">
                  <ion-skeleton-text style="width: 100%" [animated]="true"></ion-skeleton-text>
                </span>
              </div>
            }
          </div>
        </div>
      </section>
    </div>
  }
</app-card>

@if (isAbleToAddMember()) {
  <div class="floating-footer" [class.floating-show]="isFloatingFooterVisible()">
    <footer class="group-handles">
      <ng-container
        [ngTemplateOutlet]="isAbleToInviteMember() ? creatorHandlersTmpl : memberHandlersTmpl"
      ></ng-container>
    </footer>
  </div>
}

<ng-template #groupConditionsTmpl>
  <ul class="group-conditions">
    @for (fn of studyFinishConditions; track $index) {
      <ion-item [style.--background]="'var(--bg-color-2)'">
        <ion-checkbox [checked]="true"></ion-checkbox>
        <ion-label>{{ fn() }}</ion-label>
      </ion-item>
    }
  </ul>
</ng-template>

<ng-template #creatorHandlersTmpl>
  @if (canPublishToSquare()) {
    <button
      memo-button
      class="button-publish"
      skin="light"
      [ngStyle]="{ '--text-color': isGroupPublished() && 'var(--second-font-color)' }"
      (memoClick)="changePublishStatus()"
    >
      {{ isGroupPublished() ? '已发布到广场' : '发布到广场' }}
    </button>
  }
  <button memo-button (memoClick)="shareToOthers()">邀请好友组队</button>
</ng-template>

<ng-template #memberHandlersTmpl>
  <div class="info-wrapper">
    @if (isPayByCreator()) {
      <p class="pay-tips-celebrate">
        <span style="margin-right: 0.3em">🎉🥰</span>
        @if (isActivityPaidWithSpecialCoupon()) {
          <span>此队伍为「寒假组队卷王计划」队伍</span>
        } @else {
          <strong>{{ isCommercialGroup() ? '赞助商' : `&#64;${creatorInfo()?.name}` }}已替全队支付</strong>
        }
        <span style="margin-left: 0.3em">🥳🎉</span>
      </p>
    }
    <button class="button-with-desc" width="95%" memo-button [throttleTime]="2000" (memoClick)="onClickJoinGroup()">
      <span class="btn-text"> {{ joinButtonText() }} </span>
      @if (!isActivityPaidWithSpecialCoupon()) {
        <span class="btn-desc">
          <span>{{ priceDesc() }}</span>
        </span>
      }
    </button>
  </div>
</ng-template>
