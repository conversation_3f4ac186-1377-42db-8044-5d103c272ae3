import { Location } from '@angular/common'
import { Component, DestroyRef, ElementRef, Injector, OnInit, computed, effect, inject, signal, viewChild, type Signal } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { isBelowSpecifiedAppVersion } from '@maimemo/client-frontend-bridge'
import { captureException } from '@sentry/angular'

import { Big } from 'big.js'
import { padStart } from 'lodash-es'
import { NEVER, catchError, debounceTime, distinctUntilChanged, filter, from, lastValueFrom, map, merge, of, shareReplay, switchMap, take, tap, throwError } from 'rxjs'
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop'
import { AppService } from '../../../../app.service'
import { DestroyService } from '../../../core/services/destroy.service'
import { StorageService } from '../../../core/services/storage.service'
import { UserService } from '../../../core/services/user.service'
import { ROUTE_BASE_HREF } from '../../../entry/group-study.module'
import { GroupStudyClientService } from '../../../group-study/service/services/group-study-client.service'
import { capturePaymentEvent } from '../../../shared/helper/sentry'
import { fromSeconds, getTimerToNextStudyDay, toStudyDate } from '../../../shared/helper/time'
import { celebrate, createEmptyArray, wrapLinkToExternal } from '../../../shared/helper/utils'
import { MemoAlertService } from '../../../ui/alert/alert.service'
import { MemoLoadingService } from '../../../ui/loading/loading.service'
import { MemoModalService } from '../../../ui/modal/modal.service'
import { InteractiveBoardService } from '../../components/interactive-board/interactive-board.service'
import { ShareGroupComponent } from '../../components/share-group/share-group.component'
import { Activity, ActivityType, CustomGroupStudyActivityConfig, PayBy } from '../../models/activity.model'
import { VIEWABLE_EVENT_TARGET } from '../../models/event.model'
import { GroupMeta } from '../../models/group.model'
import { Member } from '../../models/member.model'
import { ChatService } from '../../services/chat.service'
import { ActivityUIConfig, GroupStudyStorageService } from '../../services/group-study-storage.service'
import { GroupStudyUIService } from '../../services/group-study-ui.service'
import { JoiningGroupParams } from '../../services/request.type'
import { resolveGroupMetaFromQueryParams } from '../../utils/activity.resolver'
import { MAX_REWARD } from '../../utils/study'
import { GROUP_STATUS_EARLY_LEARNING } from '../study-detail/helper'
import { useElementVisible } from '../../../../hooks/use-element-visible'
import { EventService } from '../../../core/services/event.service'
import { MAX_MEMBER_COL_COUNT } from './config'

@Component({
  selector: 'app-group-detail',
  templateUrl: './group-detail.component.html',
  styleUrls: ['./group-detail.component.scss'],
  providers: [DestroyService],
  standalone: false,
})
export class GroupDetailComponent implements OnInit {
  private footerRef = viewChild<ElementRef<HTMLElement>>('footerRef')

  private _location = inject(Location)
  private router = inject(Router)
  private route = inject(ActivatedRoute)
  private gss = inject(GroupStudyStorageService)
  private gsu = inject(GroupStudyUIService)
  private user = inject(UserService)
  private app = inject(AppService)
  private board = inject(InteractiveBoardService)
  private client = inject(GroupStudyClientService)
  private chat = inject(ChatService)
  private loading = inject(MemoLoadingService)
  private alert = inject(MemoAlertService)
  private modal = inject(MemoModalService)
  private storage = inject(StorageService)
  private destroyRef = inject(DestroyRef)
  private routeBase = inject(ROUTE_BASE_HREF)
  private injector = inject(Injector)
  private eventService = inject(EventService)

  isInitialized = signal(false)
  blankMembers = createEmptyArray(10)
  nextActiveActivity = signal<Activity<ActivityType> | undefined>(undefined)

  private groupMeta = toSignal(
    this.route.queryParamMap.pipe(
      map(paramsMap => resolveGroupMetaFromQueryParams(paramsMap)),
      shareReplay(1),
    ),
  )

  private groupData$ = this.route.queryParamMap.pipe(
    switchMap(paramsMap => {
      const { checkRedirect } = this.route.snapshot.data
      const groupMeta = resolveGroupMetaFromQueryParams(paramsMap)
      return this.gss.getGroupDataByGroupMeta(groupMeta, !!checkRedirect)
    }),
    shareReplay(1),
  )

  private groupWithActivity$ = this.groupData$.pipe(
    switchMap(groupData => {
      const group = GroupStudyStorageService.getGroupFromGroupData(groupData)
      if (!group) {
        return NEVER
      }
      return this.gss.getActivity$(group.activityId)
        .pipe(
          map(activity => [groupData, group, activity] as const),
        )
    }),
    shareReplay(1),
  )

  // 后续都改成基于 Signal
  private groupWithActivity = toSignal(this.groupWithActivity$)
  group = computed(() => {
    const data = this.groupWithActivity()
    return data?.[1]
  })

  private countDown = toSignal(getTimerToNextStudyDay(toStudyDate(Date.now())))

  countDownText = computed(() => {
    const duration = this.countDown()

    if (this.isGroupingFailed() || !duration) {
      return '00:00:00'
    }

    const hour = Math.max(duration.hours(), 0)
    const minute = Math.max(duration.minutes(), 0)
    const second = Math.max(duration.seconds(), 0)
    const result = `${padStart(hour.toString(), 2, '0')}:${padStart(minute.toString(), 2, '0')}:${padStart(second.toString(), 2, '0')}`
    return result
  })

  isGroupDismissable = computed(() => {
    const group = this.group()
    if (!group) return false

    const membersCount = group.members.length ?? 0

    if (!this.isCurrentUserTheCreator() || group.status !== 'GROUPING' || membersCount !== 1) {
      return false
    }

    return this.userId() === group.members[0].userId
  })

  footerVisibleHandle = useElementVisible(this.footerRef)

  isFooterUpon = computed(() => {
    const lastEntry = this.footerVisibleHandle.lastEntry()
    if (!lastEntry) return false
    return lastEntry.boundingClientRect.y < 0
  })

  isGroupPublished = signal(false)
  isCurrentUserInGroup = signal(false)
  isCurrentUserTheCreator = computed(() => {
    const activity = this.activity()
    if (!this.activity) return false
    return activity?.creator === this.userId()
  })

  membersSeatingMap = signal(new Map<number, Member>())

  creatorInfo = computed(() => {
    const data = this.groupWithActivity()
    if (!data) return undefined
    const [,group, activity] = data
    const members = group?.members || []
    return members.find(v => v.userId === activity.creator)
  })

  priceToJoin = computed(() => {
    const config = this.activityConfig()
    if (!config) {
      return 0
    }

    if (config.paymentType === PayBy.CREATOR) {
      return 0
    }

    return config.price ?? this.defaultGroupingUnitPrice
  })

  maxReward = signal(MAX_REWARD)
  targetPosition = signal(-1)
  firstAvailablePosition = -1
  uiConfig = signal<ActivityUIConfig | undefined>(undefined)
  private unitPricePerDay = computed(() => {
    const config = this.activityConfig()
    if (config?.price === undefined) {
      return this.defaultGroupingUnitPrice
    }
    const { paymentType, groupSize, studyDuration, testingDuration } = config

    const price = Big(config.price)
    const unitPrice = paymentType === 'CREATOR'
      ? price.div(groupSize)
      : price

    const days = fromSeconds(studyDuration + testingDuration, 'day')

    return unitPrice.div(days).toNumber()
  })

  /**
   * 该队伍不能被加入的原因
   */
  reasonOfNotOpenForJoin = computed(() => {
    const group = this.group()
    let reason = ''

    if (this.isCurrentUserInGroupOrTheCreator()) {
      reason = ''
    } else if (!group) {
      reason = '队伍已不存在'
    } else if (this.activityConfig() && ((this.members()?.length || 0) >= this.activityConfig()!.groupSize)) {
      reason = '队伍已满员'
    } else if (group.status !== 'GROUPING') {
      reason = '队伍报名已结束'
    }

    return reason
  })

  readonly studyFinishConditions: Array<Signal<string>> = [
    computed(() => `${!this.activityConfig() ? 0 : fromSeconds((this.activityConfig()?.studyDuration || 0) + (this.activityConfig()?.testingDuration || 0), 'day')}天组队`),
    computed(() => `${fromSeconds(this.activityConfig()?.dailyStudyDuration ?? 0, 'min')}分钟/天`),
    computed(() => `${this.activityConfig()?.dailyStudyVoc ?? 0}单词/天`),
  ]

  groupDisplayConfig = computed(() => {
    const group = this.group()
    return group && this.gss.getDisplayConfigByGroup(group)
  })

  refundDesc$ = this.groupData$.pipe(
    debounceTime(300),
    map(groupData => {
      const events = groupData.events ?? []
      return events.find(v => v.eventType === 'REFUND' && v.creator === this.userInfo()?.userId)
    }),
    filter(Boolean),
    map(event => {
      const refundInfo = GroupStudyStorageService.getRefundInfoFromEvent(event)

      return refundInfo.refund_status === 'REFUNDED'
        ? `已原路退款 ¥${refundInfo.amount} 给你`
        : null
    }),
    distinctUntilChanged(),
    shareReplay(1),
  )

  canPublishToSquare = computed(() => {
    const config = this.activityConfig()
    if (!config) return false
    return this.gss.squareTestGroupType === 'PUBLISH_GROUP' && !!config.creatorAutoJoin
  })

  userInfo = this.user.userInfoSignal
  userId = computed(() => this.userInfo()?.userId)

  membersColCount = computed((): number => {
    return this.isGroupingFailed()
      ? this.membersColCountByMembers()
      : this.membersColCountByGroupSize()
  })

  membersColCountByMembers = computed(() => Math.min((this.members()?.length || 0), MAX_MEMBER_COL_COUNT))

  membersColCountByGroupSize = computed((): number => {
    return this.activityConfig() && this.activityConfig()!.groupSize < 5
      ? this.activityConfig()!.groupSize
      : MAX_MEMBER_COL_COUNT
  })

  groupMembersRate = computed((): string => {
    if (!this.group() || !this.activityConfig()) {
      return ''
    }
    const { length } = this.members() || []
    return `${length}/${this.activityConfig()!.groupSize}`
  })

  /**
   * 用户是否可以选择加入队伍
   */
  isAbleToJoin = computed((): boolean => {
    return this.group()?.status === 'GROUPING'
      && ((this.members()?.length || 0) < (this.activityConfig()?.groupSize ?? 12))
      && !this.isCurrentUserTheCreator()
      && !this.isCurrentUserInGroup()
  })

  /**
   * 队长是否可以邀请成员
   */
  isAbleToInviteMember = computed((): boolean => {
    return this.isCurrentUserTheCreator()
      && this.group()?.status === 'GROUPING'
      && !!this.activityConfig()
      && (this.members()?.length || 0) < this.activityConfig()!.groupSize
  })

  isAbleToAddMember = computed(() => this.isAbleToJoin() || this.isAbleToInviteMember())

  isGroupingFailed = computed((): boolean => {
    if (!this.isCurrentUserInGroupOrTheCreator()) {
      return false
    }
    const groupStatus = this.group()?.status
    return !!groupStatus?.includes('FAILED') || groupStatus === 'GROUP_DISMISSED'
  })

  isGroupTestFailed = computed((): boolean => {
    return this.group()?.status === 'TEST_FAILED'
  })

  members = computed(() => {
    const data = this.groupWithActivity()
    if (!data) return []
    const [,group, activity] = data
    return this.gss.filterMembersByConfig(group.members, activity)
  })

  activity = computed(() => {
    const data = this.groupWithActivity()
    return data?.[2]
  })

  activityConfig = computed(() => this.activity()?.config)

  isCurrentUserInGroupOrTheCreator = computed(() => this.isCurrentUserInGroup() || this.isCurrentTheCreatorNotInGroup())
  isCurrentTheCreatorNotInGroup = computed(() => !this.activityConfig()?.creatorAutoJoin && this.isCurrentUserTheCreator())

  isFloatingFooterVisible = computed(() => {
    return this.isInitialized()
      && (this.activityConfig()?.groupSize || 0) >= 10
      && !this.footerVisibleHandle.isVisible()
      && !this.isFooterUpon()
  })

  isInteractiveButtonVisible = computed(() => !!this.uiConfig()?.showInteractiveButton)

  hasUnreadMessage = computed(() => !!this.uiConfig()?.hasUnreadMessage)

  isActivityPaidWithSpecialCoupon = computed(() => {
    const activity = this.activity()
    if (!activity) return false
    const appliedCoupons = activity.extra?.promotionActivities || []
    if (!appliedCoupons.length) return false
    return appliedCoupons.includes('WINTER_VACATION_2025')
  })

  get defaultGroupingUnitPrice(): number {
    return this.gss.defaultGroupingUnitPrice
  }

  isPayByCreator = computed(() => this.activityConfig()?.paymentType === 'CREATOR')

  priceDesc = computed(() => `${this.isCommercialGroup() ? '' : `每人天 ${this.unitPricePerDay()} 元（试运行价格），`}每天有机会获得 1 ~ ${this.maxReward()} 单词上限`)
  joinButtonText = computed(() => {
    if (this.isActivityPaidWithSpecialCoupon()) {
      return '立即免费加入队伍'
    }

    const price = this.priceToJoin()
    return `确认加入（${price ? '¥' + price : '免费'}）`
  })

  get canSetupCustomGroup(): boolean {
    return !!this.gss.maxGroupSizeLimit
  }

  isCommercialGroup = computed(() => !!this.commercialBanner())

  commercialBanner = computed(() => {
    const gIdFromQuery = this.groupMeta()?.group_id
    if (gIdFromQuery && this.gss.promotionGroupIdToBanner.get(gIdFromQuery)) {
      const cachedBanner = this.gss.promotionGroupIdToBanner.get(gIdFromQuery)
      if (cachedBanner) {
        return cachedBanner
      }
    }
    return this.group()?.promotion?.banners?.[0] || ''
  })

  commercialBannerLink = computed(() => wrapLinkToExternal(this.group()?.promotion?.link))

  constructor() {
    this.gss.getSquareTestGroup().subscribe()

    // Update price
    effect(() => {
      const config = this.activityConfig()
      this.updatePriceConfig(config)
    })

    // Update title
    effect(() => {
      const group = this.group()
      group && this.app.setTitle(group.name)
    })
  }

  async ngOnInit() {
    this.groupWithActivity$.pipe(
      takeUntilDestroyed(this.destroyRef),
    )
      .subscribe(([groupData, group, activity]) => {
        const { isPublic = false } = groupData
        const { activityId, members } = group
        this.isGroupPublished.set(isPublic)

        const isCurrentUserInGroupBefore = this.isCurrentUserInGroup()
        this.isCurrentUserInGroup.set(
          !this.isCurrentTheCreatorNotInGroup()
          && members.some(v => v.userId === this.userId() || v.isCurrentUser),
        )

        // update seating map
        this.members().forEach((member, i) => {
          this.membersSeatingMap.update(seatingMap => {
            seatingMap.set(member.position ?? i, member)
            return seatingMap
          })
        })

        // 加入队伍成功
        if (
          this.isInitialized()
          && !isCurrentUserInGroupBefore
          && this.isCurrentUserInGroup()
          && activity.config
          && members.length < activity.config.groupSize
        ) {
          this.firstAvailablePosition = -1
          this.targetPosition.set(-1)
          this.route.queryParamMap
            .pipe(
              take(1),
              map(paramsMap => resolveGroupMetaFromQueryParams(paramsMap)),
            )
            .subscribe(groupMeta => this.gss.navigateByGroupStatus(group, { meta: groupMeta }))

          return
        }
        const uiConfig = this.gss.getActivityUIConfig(activityId)
        if (uiConfig) {
          this.uiConfig = uiConfig
        }

        if (this.isAbleToJoin()) {
          if (!this.isInitialized() || this.membersSeatingMap().has(this.targetPosition())) {
            this.checkAvailablePosition()

            if (this.firstAvailablePosition !== -1) {
              this.targetPosition.set(this.firstAvailablePosition)
            }
          }
        }
        this.isInitialized.set(true)
      })

    // 当前用户所在的队伍组队成功的话
    // 进入页面播放庆祝动画
    this.groupData$.pipe(
      filter(groupData => !!groupData?.group),
      filter(({ group }) => {
        const isCurrentUserInGroup = group!.members.some(v => v.userId === this.userId() || v.isCurrentUser)
        return isCurrentUserInGroup && GROUP_STATUS_EARLY_LEARNING.includes(group!.status)
      }),
      distinctUntilChanged((prev, current) => prev.group?.status === current.group?.status),
      takeUntilDestroyed(this.destroyRef),
    )
      .subscribe(groupData => {
        const group = groupData.group!
        merge(
          this.gss.track('STUDY_RECORD', group!.groupId),
          this.gss.track('VIEW', group!.groupId, VIEWABLE_EVENT_TARGET.GROUP_SUCCEEDED_ANIMATION),
        ).subscribe()

        const beforeAction$ = groupData.showGroupSucceededAnimation === true
          ? from(celebrate())
          : of(undefined)

        beforeAction$
          .pipe(
            take(1),
            filter(() => GROUP_STATUS_EARLY_LEARNING.includes(group.status)),
            switchMap(() => this.route.queryParamMap),
            map(paramsMap => resolveGroupMetaFromQueryParams(paramsMap)),
          )
          .subscribe(groupMeta => {
            this.gss.navigateByGroupStatus(group, {
              meta: groupMeta,
            })
          })
      })

    toObservable(this.group, {
      injector: this.injector,
    })
      .pipe(
        filter(Boolean),
        debounceTime(500),
        filter(group =>
          GroupStudyStorageService.isGroupActive(group.status)
          && group.members.some(v => v.userId === this.userId() || v.isCurrentUser),
        ),
        switchMap(group => this.gss.getNextActiveActivity(group)),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(activity => {
        this.nextActiveActivity.set(activity)
      })
  }

  async changePublishStatus() {
    if (this.isGroupPublished()) {
      return this.navigateToSquarePage()
    }

    const ref = await this.alert.show({
      header: '发布到广场',
      message: '队伍将展示在墨墨组队广场，其他墨墨用户可以更便捷加入你的队伍',
      cssClass: ['memo-alert'],
      buttons: [
        {
          role: 'cancel',
          text: '不发布',
        },
        {
          text: '发布队伍',
          role: 'confirm',
          cssClass: 'green-text',
        },
      ],
    })

    const { role } = await ref.onDidDismiss()

    if (role !== 'confirm') {
      return
    }

    await this.loading.show()

    this.gss.publishGroup(this.group()!.groupId)
      .pipe(
        catchError(err => {
          const msg = err?.error?.errors?.[0]?.message || err?.message || '服务错误'
          this.alert.show({
            header: '发布失败',
            message: msg,
            buttons: [{
              text: '我知道了',
            }],
            cssClass: ['memo-alert'],
          })
          this.loading.dismiss()
          return throwError(() => new Error(msg))
        }),
        switchMap(() => {
          this.isGroupPublished.set(true)
          this.loading.dismiss()

          return this.alert.show({
            header: '发布成功',
            message: '是否现在返回到组队广场？',
            cssClass: ['memo-alert'],
            buttons: [
              {
                text: '先不了',
              },
              {
                text: '回到广场',
                cssClass: 'green-text',
                handler: () => {
                  this.navigateToSquarePage()
                },
              },
            ],
          })
        }),
      )
      .subscribe()
  }

  private navigateToSquarePage() {
    this.storage.updateIsIgnoreSquarePage(false)

    this.isComeFromSquarePage()
      ? this._location.back()
      : this.router.navigate(['square'], {
          relativeTo: this.route,
        })
  }

  private isComeFromSquarePage(): boolean {
    const lastSuccessNavigation = this.router.lastSuccessfulNavigation
    const prevNavigation = lastSuccessNavigation?.previousNavigation

    // trigger !== 'imperative'
    // 说明不是正常的前进 (router.navigate)
    // 而是后退 或者 url 变更
    if (!prevNavigation || prevNavigation.trigger !== 'imperative') {
      return false
    }

    const prevRouteUrl = this.router.serializeUrl(prevNavigation.extractedUrl)
    return !!prevRouteUrl && prevRouteUrl.includes('/square')
  }

  async shareToOthers() {
    const group = this.group()
    if (!group) return
    this.gss.getInvitationCode(group.groupId)
      .subscribe({
        next: ({ invitationCode }) => {
          this.modal.create({
            component: ShareGroupComponent,
            componentProps: {
              shareText: invitationCode,
            },
            cssClass: 'rounded-modal fit-modal ion-align-items-end',
          })
        },
        error: err => {
          this.client.clientToast(err?.error?.errors?.[0]?.message, 'error')
        },
      })
  }

  getMemberIsFinish(memberId: number, date = this.group()?.studyStartTime) {
    if (!date) {
      return false
    }

    return this.gss.isMemberFinishStudy(memberId, date)
  }

  async onClickJoinGroup() {
    const hasPermission = await this.gss.checkGroupingPermission()
    if (!hasPermission) {
      return
    }

    this.onClickConfirm()
  }

  private checkAvailablePosition() {
    const groupSize = this.activityConfig()?.groupSize || 0
    const seatingMap = this.membersSeatingMap()

    for (let i = 0; i <= groupSize; i++) {
      if (!seatingMap.has(i)) {
        this.firstAvailablePosition = i
        break
      }
    }
  }

  async onClickConfirm() {
    this.route.queryParams
      .pipe(
        take(1),
        switchMap((queryParams: GroupMeta) => {
          const invitationCode = queryParams.invitation_code
          const params: JoiningGroupParams = {}

          if (invitationCode) {
            params.invitation_code = invitationCode
          } else {
            params.group_id = this.group()?.groupId
          }

          if (this.targetPosition !== undefined) {
            params.position = this.targetPosition()
          }

          return this.gss.joinTheGroup(params)
            .pipe(
              map(res => {
                return {
                  result: res,
                  params,
                }
              }),
            )
        }),
        switchMap(({ params, result }) => {
          const { group, goods_sn, group_order } = result
          if (!goods_sn && !group_order) {
            return of({
              group,
              params,
            })
          }

          return from(this.client.clientPayForJoiningGroup(
            this.priceToJoin(),
            goods_sn ?? group_order!.product.goods_sn,
            group.activity_id,
            group_order,
          ))
            .pipe(
              switchMap(res => {
                console.debug('Bridge Result', res)

                capturePaymentEvent({
                  type: 'join',
                  activity_id: group.activity_id,
                  group_id: group.group_id,
                  res: res.data,
                })

                if (['success', 'not_received', 'pending'].includes(res.data.status)) {
                  return of({
                    group,
                    params: params,
                  })
                }

                return throwError(() => new Error(res.data.status === 'cancel' ? '支付已取消' : '支付失败'))
              }),
            )
        }),
        // 记录事件
        tap(({ params, group }) => {
          const isFromSquare = !params.invitation_code
          if (isFromSquare) {
            this.eventService.track('group_study_square_events', [
              {
                event: 'join_group_study_group',
                type: 'Click',
                event_time: new Date().toISOString(),
                content: {
                  group_id: group.group_id,
                  source: 'square',
                },
              },
            ])
              .subscribe()
          }
        }),
      )
      .subscribe({
        next: async () => {
          const msg = this.priceToJoin() > 0 ? '支付成功' : '加入成功'
          this.client.clientToast(`${msg}，请稍等`)
          if (!this.isCurrentUserInGroup()) {
            await this.loading.show()
          }
          this.client.clientUploadStudyRecord()
        },
        error: err => {
          const apiError = err?.error?.errors?.[0]
          captureException(err)

          if (apiError && apiError.code === 'custom_group_max_group_exceeded') {
            this.gsu.showOutOfLimitModal({
              max: 3,
              currentCount: 3,
            })
            return
          }

          const msg = err?.error?.errors?.[0]?.message || err?.message || err
          this.alert.show({
            message: msg || '请求失败',
            cssClass: 'memo-alert',
            buttons: [{ text: '确定' }],
          })
        },
      })
  }

  private updatePriceConfig(config: CustomGroupStudyActivityConfig | undefined) {
    if (!config) {
      this.maxReward.set(MAX_REWARD)
      return
    }

    this.gss.getGroupingPrice({
      type: 'GROUP_STUDY_CUSTOM',
      title: '',
      config: {
        creator_auto_join: config.creatorAutoJoin,
        daily_study_duration: config.dailyStudyDuration,
        daily_study_voc: config.dailyStudyVoc,
        group_size: config.groupSize,
        payment_type: config.paymentType,
        study_duration: config.studyDuration,
        testing_duration: config.testingDuration,
      },
    })
      .subscribe(({ unitPrice, maxReward }) => {
        // this.unitPricePerDay = unitPrice
        this.maxReward.set(maxReward || MAX_REWARD)
      })
  }

  onClickSetupGroup() {
    this.router.navigateByUrl(`${this.routeBase}setup`)
  }

  onClickFloatingButton(buttonType: 'left' | 'right') {
    if (buttonType === 'right') {
      this.groupWithActivity$.pipe(
        take(1),
        switchMap(([groupData, group, activity]) => {
          return this.gss.hasPermissionToChat(group.groupId) && !isBelowSpecifiedAppVersion('5.2.0')
            ? this.chat.navigateToClientChat(group, this.gss.filterMembersByConfig(group.members, activity).length)
            : this.board.showInteractiveBoard(group, groupData.events ?? [])
        }),
      )
        .subscribe()
      return
    }

    if (buttonType === 'left') {
      const activity = this.nextActiveActivity()!
      this.resetStates()
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: {
          activity_id: activity.id,
        },
        replaceUrl: true,
      })
    }
  }

  onClickEmptyMember(index?: number) {
    this.isAbleToInviteMember()
      ? this.shareToOthers()
      : (this.targetPosition.set(index ?? this.firstAvailablePosition))
  }

  private resetStates() {
    this.gss.clearPollingSubscriptions()
    this.gss.clearCaches()
    this.membersSeatingMap.update(seatingMap => {
      seatingMap.clear()
      return seatingMap
    })
    this.isInitialized.set(false)
    this.isGroupPublished.set(false)
    this.isCurrentUserInGroup.set(false)
    this.firstAvailablePosition = -1
  }

  onClickDismiss() {
    this.alert.show({
      header: '你确定要解散此队伍吗？',
      message: '解散后，1 个工作日内将退款相应费用给支付人。',
      cssClass: ['memo-alert', 'warning-alert'],
      buttons: [
        {
          text: '再想想',
          cssClass: 'green-text',
        },
        {
          text: '确定',
          handler: async () => {
            const groupId = this.group()?.groupId
            if (!groupId) {
              this.client.clientToast('未找到队伍信息')
              return
            }
            await this.loading.show()
            try {
              await lastValueFrom(this.gss.dismissGroup(this.group()!.groupId))
              this.client.clientToast('解散成功')
              location.replace(`https://www.maimemo.com/pages/event-center/group-study-list?token=${this.user.legacyToken}&ref=activity-center`)
            } catch (error) {
              this.client.clientToast('未知错误')
              captureException(error)
              return
            } finally {
              await this.loading.dismiss()
            }
          },
        },
      ],
    })
  }
}
