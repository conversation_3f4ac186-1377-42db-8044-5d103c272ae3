:host {
  --gap-base: 5px;

  width: 100%;
  padding: 0 var(--page-padding);
  transition: padding 150ms ease;
}

.group-detail-container {
  margin: 0 -4px;
  padding-top: var(--gap-base);
  padding-bottom: calc(var(--gap-base) * 2);
}

.member-info,
.group-detail-container {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.group-detail {
  width: 100%;

  &.failed {
    .text-main,
    .text-info,
    .member-status.failed {
      color: var(--red-bgColor);
    }
  }
}

p {
  margin: 0;
}

.group-tips {
  --color-tips: var(--second-font-color);

  text-align: center;

  header {
    font-weight: 600;
  }

  & > :not(:last-child) {
    margin-bottom: calc(var(--gap-base) * 2);
  }
}

.text-main {
  font-size: 30px;
  font-weight: 900;
}

.text-info {
  font-size: 13px;
  color: var(--color-tips);
}

.members-container {
  margin-top: calc(var(--gap-base) * 5);
  margin-bottom: calc(var(--gap-base) * 4);
}

.members-grid {
  --cols: 2;

  display: grid;
  grid-template-columns: repeat(var(--cols), 1fr);
  row-gap: 18px;
  justify-items: center;
}

.member-info {
  text-align: center;
  max-width: 50px;
  transition: display 150ms ease;
}

.member-name {
  margin-top: 4px;
  font-size: 12px;
  width: 100%;
  color: var(--second-font-color);
  line-height: 1.5;
}

.seat-placeholder app-member-avatar {
  opacity: 0.4;
}

.member-status {
  margin-top: 6px;
  font-size: 14px;
  width: 100%;
  color: var(--theme-green);
}

.group-handles {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;

  & > [memo-button] {
    flex: 1;
    min-width: 0;
    border: none;
    font-weight: 600;

    &:not(:first-child) {
      margin-left: 15px;
    }
  }
}

.group-detail-container .pay-tips-celebrate {
  margin-top: calc(var(--gap-base) * -2);
}

.pay-tips-celebrate {
  font-size: 13px;
  text-align: center;
  margin-bottom: calc(var(--gap-base) * 4);
}

.group-conditions {
  --max-width: 305px;

  width: 100%;
  max-width: var(--max-width);
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  border-radius: 4px;
  border: 1px dashed #e5e5e5;
  margin: 20px auto 25px auto;
  padding: 10px 0;

  ion-item {
    display: inline-flex;
    align-items: center;
    color: #dc663e;
    font-size: 14px;
  }

  ion-checkbox {
    --size: 13px;
    pointer-events: none;
    margin-right: 3px;

    &::part(container) {
      --background: var(--red-bgColor);
      --background-checked: var(--red-bgColor);
      --border-width: 0;
    }
  }
}

@media screen and (min-width: 640px) {
  .group-conditions {
    --max-width: 350px;
  }
}

ion-thumbnail {
  ion-skeleton-text {
    --border-radius: 5px;
  }
}

.info-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

:root.dark :host {
  .button-publish {
    border: 1px solid var(--theme-green) !important;
  }
}

.floating-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1rem 1.5rem 50px;
  background: var(--bg-color-2);
  z-index: 999;
  opacity: 0;
  transform: translateY(150%);
  transition: transform 150ms ease-in-out;
  will-change: transform;
}

.floating-footer.floating-show {
  opacity: 1;
  transform: translateY(0);
}
