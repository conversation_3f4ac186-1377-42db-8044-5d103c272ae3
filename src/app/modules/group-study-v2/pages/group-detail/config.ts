import { GroupStatus } from '../../models/group.model'


/**
 * 默认折叠状态下最多显示 20 个成员
 */
export const MAX_MEMBER_COUNT_FOR_DISPLAY = 20

/**
 * 成员列表最多展示 5 列
 */
export const MAX_MEMBER_COL_COUNT = 5

export type GroupDisplayConfig = {

  /**
   * 组队状态对应的文字
   */
  statusText: string

  /**
   * 补充说明
   */
  statusDesc: string
}

export const configByGroupStatus: Partial<Record<GroupStatus, GroupDisplayConfig>> = {
  GROUPING: {
    statusText: '组队中',
    statusDesc: '',
  },
  GROUP_SUCCEEDED: {
    statusText: '组队成功',
    statusDesc: '',
  },
  GROUP_FAILED: {
    statusText: '组队失败',
    statusDesc: '报名期内不足 2 人',
  },
  TEST_FAILED: {
    statusText: '组队失败',
    statusDesc: '首日磨合期内未足 2 人完成组队要求 ',
  },
  LEARNING: {
    statusText: '学习中',
    statusDesc: '',
  },
}
