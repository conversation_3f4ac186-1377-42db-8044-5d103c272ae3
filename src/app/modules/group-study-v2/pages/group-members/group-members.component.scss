:host {
  --gap-base: 5px;

  display: block;
  width: 100%;
  overflow-x: hidden;
}

app-card {
  padding: calc(var(--gap-base) * 3);
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - (var(--gap-base) * 4));
}

.members-cards-container {
  height: 100%;
  padding: 0 var(--page-padding);
  transition: padding 150ms ease;
}

.card-header {
  padding: 5px 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  h2 {
    margin: 0 0.5em;
    padding: 0;
    font-size: 18px;
    font-weight: bold;
    min-width: 3em;
    text-align: center;
  }
}

.btn-arrow {
  background: none;
  padding: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--default-font-color);
}

.btn-arrow:disabled {
  opacity: 0.3;
  pointer-events: none;
}

.swiper-prev {
  transform: rotate(90deg);
}

.swiper-next {
  transform: rotate(-90deg);
}

.member-list {
  position: relative;
  padding-top: 17px;
  display: flex;
  flex-direction: column;

  & > :not(:last-child) {
    margin-bottom: 4px;
  }
}

app-study-stats-list-item {
  height: fit-content;
}

.virtual__item {
  position: absolute;
  width: 100%;
  height: var(--item-size);
  transform: translateY(var(--item-start));
}
