import { ChangeDetectorRef, Component, DestroyRef, OnInit, ViewChild, computed, inject, signal } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import dayjs from 'dayjs'
import { EMPTY, distinctUntilChanged, filter, map, shareReplay, switchMap, take, tap } from 'rxjs'
import Swiper from 'swiper'
import { SwiperComponent } from 'swiper/angular'
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop'
import { memoize } from 'lodash-es'
import { injectVirtualizer } from '@tanstack/angular-virtual'
import { AppService } from '../../../../app.service'
import { DestroyService } from '../../../core/services/destroy.service'
import { GroupStudyClientService } from '../../../group-study/service/services/group-study-client.service'
import { MemoLoadingService } from '../../../ui/loading/loading.service'
import { Activity, CustomGroupStudyActivityConfig } from '../../models/activity.model'
import { GroupData } from '../../models/group.model'
import { History } from '../../models/history.model'
import { Member } from '../../models/member.model'
import { GroupStudyStorageService } from '../../services/group-study-storage.service'
import { GroupStudyTickleService } from '../../services/group-study-tickle.service'
import { getCompareFn } from '../../utils/diff'
import { MemberDailyStudyStats, MemberStudyStats } from '../study-report/type'
import { UserService } from '../../../core/services/user.service'
import { CalendarDateItem } from '../../utils/study'
import type { VirtualOptions } from 'swiper/types'

@Component({
  selector: 'app-group-members',
  templateUrl: './group-members.component.html',
  styleUrls: ['./group-members.component.scss'],
  providers: [DestroyService],
  standalone: false,
})
export class GroupMembersComponent implements OnInit {
  @ViewChild('dateSwiper', { static: false })
  private dateSwiper!: SwiperComponent

  private userService = inject(UserService)
  private destroyRef = inject(DestroyRef)

  /**
   * 记录每天每个用户的学习记录
   *
   * {
   *  [DATE_FORMAT]: {
   *    [userId]: MemberDailyStudyStats
   *  }
   * }
   */
  memberCompletionMap = signal<Record<string, Record<number, MemberDailyStudyStats>>>({})

  /**
   * 记录每个用户累计的学习数据
   *
   * {
   *  [userId]: MemberStudyStats
   * }
   */
  memberDailySummaryStatsMap = signal<Record<number, MemberStudyStats>>({})

  activityConfig?: CustomGroupStudyActivityConfig

  /**
   * 从学习中进来的话，可能会带有某一天的信息， formatted
   */
  entryDate = this.route.snapshot.queryParams.date

  groupData$ = this.route.data.pipe(
    switchMap(data => this.gss.getGroupDataByGroupMeta(data.groupMeta, data.checkRedirect)),
    shareReplay(1),
  )

  private groupDataWithActivity$ = this.groupData$
    .pipe(
      switchMap(groupData => {
        const group = GroupStudyStorageService.getGroupFromGroupData(groupData)

        if (!group) {
          this.client.clientToast('没有队伍信息')
          return EMPTY
        }

        return this.gss.getActivity$(group.activityId)
          .pipe(
            map(activity => [groupData, group, activity] as const),
          )
      }),
      shareReplay(1),
    )

  studyBadgeText$ = this.groupDataWithActivity$.pipe(
    map(([, group]) => group && group.status !== 'LEARN_SUCCEED' ? '非酋奖提名' : '非酋奖'),
    distinctUntilChanged(),
    shareReplay(1),
  )

  calendars: CalendarDateItem[] = []
  datesWithRecord: CalendarDateItem[] = []

  members$ = this.groupDataWithActivity$.pipe(
    map(([groupData, group, activity]) => this.gss.filterMembersByConfig(group.members, activity)),
    tap(members => {
      this.app.setTitle(`全员概况（${members.length}）`)
    }),
    shareReplay(1),
  )

  members = toSignal(this.members$)

  unluckyMembers$ = this.groupData$.pipe(
    map(groupData => {
      const group = GroupStudyStorageService.getGroupFromGroupData(groupData)
      return group && group.unluckyMembers
        ? group.unluckyMembers
        : []
    }),
    distinctUntilChanged(getCompareFn('deepEqual')),
    shareReplay(1),
  )

  initialIndex = 0
  activeDateIndex = -1
  isInitialized = false

  pagePadding = computed(() => this.app.pageGap())

  get isLanternThemeVisible() {
    return this.gss.isLanternThemeVisible
  }

  userInfo = this.userService.userInfoSignal
  // studyWorker = this.workerService.getWorker('study')

  virtualSwiperOptions: VirtualOptions = {
    enabled: true,
    addSlidesAfter: 2,
    addSlidesBefore: 2,
  }

  virtualizer = injectVirtualizer(() => {
    return {
      count: this.members()?.length ?? 0,
      estimateSize: () => 60,
      overscan: 5,
      scrollElement: document.querySelector('#group-study-v2')!,
      scrollPaddingStart: 70,
    }
  })

  constructor(
    private cdr: ChangeDetectorRef,
    private route: ActivatedRoute,
    private client: GroupStudyClientService,
    private gss: GroupStudyStorageService,
    private app: AppService,
    private tickle: GroupStudyTickleService,
    private loading: MemoLoadingService,
  ) { }

  async ngOnInit() {
    this.groupDataWithActivity$.pipe(
      takeUntilDestroyed(this.destroyRef),
    )
      .subscribe(async ([groupData, group, activity]) => {
        this.activityConfig = activity.config
        await this.updateCalendars(groupData, activity)
        const members = this.members()

        this.storeMemberStudyStats(members || [], groupData.history || [], this.calendars)

        // 组件初始化时优先使用缓存，因为有可能是单纯切换页面了
        const preferCache = Object.keys(this.memberDailySummaryStatsMap()).length === 0
        this.memberDailySummaryStatsMap.set(this.gss.generateDailyStudyStatsMap(groupData, activity, preferCache))

        if (!this.isInitialized) {
          this.isInitialized = true
        }
      })
  }

  onDateSlideChange(swiper: Swiper) {
    this.activeDateIndex = swiper.activeIndex
  }

  onNavigateSlide(dir: 'next' | 'prev') {
    if (dir === 'next') {
      this.dateSwiper.swiperRef.slideNext(100)
      return
    }
    this.dateSwiper.swiperRef.slidePrev(100)
  }

  private async updateCalendars(groupData: GroupData, activity: Activity) {
    const group = GroupStudyStorageService.getGroupFromGroupData(groupData)
    if (!group) {
      return
    }

    const {
      fullCalendars = [],
      datesWithRecord = [],
    } = this.gss.generateCalendars(group, activity)

    this.calendars = fullCalendars
    this.datesWithRecord = datesWithRecord

    if (this.isInitialized && this.calendars.length > 0 && this.datesWithRecord.length > 0) {
      return
    }

    let targetIndex = -1

    if (this.entryDate) {
      targetIndex = datesWithRecord.findIndex(v => v.formattedDate === this.entryDate)
    }

    this.activeDateIndex
    = this.initialIndex
     = targetIndex === -1
          ? datesWithRecord.length
          : targetIndex

    this.cdr.detectChanges()
  }

  private async storeMemberStudyStats(members: Member[], histories: History[], calendarItems: CalendarDateItem[]) {
    const {
      statsMap = {},
    } = this.gss.generateDailyStatsData(members, histories, calendarItems)

    this.memberCompletionMap.set(statsMap)
  }

  onDbclickMember(element: HTMLElement, member: Member) {
    this.groupData$.pipe(
      map(groupData => GroupStudyStorageService.getGroupFromGroupData(groupData)),
      filter(Boolean),
      take(1),
    )
      .subscribe(group => {
        this.tickle.tickle(element, group, member)
      })
  }

  isAlreadyHasResult(date: CalendarDateItem, member: Member): boolean {
    const studyStats: MemberDailyStudyStats | undefined = this.memberCompletionMap()[date.formattedDate]?.[member.userId]
    if (date.offsetToToday < 0) {
      return true
    }

    if (date.offsetToToday === 0) {
      return studyStats?.isFinished || studyStats?.isExited
    }

    return studyStats?.isExited
  }

  checkIsCurrentUser(member: Member): boolean {
    return this.gss.checkIsMemberTheCurrentUser(member.userId)
  }

  createMembersSorting = memoize((formattedDate: string, isSummary = false) => {
    return computed(() => {
      const members = this.members() || []
      const completionMap = this.memberCompletionMap()

      return members
        .slice()
        .sort((a, b) => {
          const isAExited = isSummary
            ? a.exitedTime
            : completionMap?.[formattedDate]?.[a.userId]?.isExited

          const isBExited = isSummary
            ? b.exitedTime
            : completionMap?.[formattedDate]?.[b.userId]?.isExited

          const aExited = isAExited ? 1 : -1
          const bExited = isBExited ? 1 : -1

          // 产品需求：同样是退出的话，先退出的排后面
          if (aExited === 1 && bExited === 1) {
            return dayjs(a.exitedTime).isBefore(b.exitedTime) ? 1 : -1
          }

          return aExited - bExited
        })
    })
  })
}
