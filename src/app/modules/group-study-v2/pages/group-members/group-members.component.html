<div class="members-cards-container">
  @if (isInitialized && activeDateIndex !== -1) {
    <swiper
      #dateSwiper
      [initialSlide]="initialIndex"
      [slidesPerView]="1"
      [spaceBetween]="pagePadding()"
      [virtual]="virtualSwiperOptions"
      (slideChange)="onDateSlideChange($event[0])"
    >
      @for (date of datesWithRecord; track date.date; let i = $index) {
        <ng-template swiperSlide>
          <app-card>
            <header class="card-header">
              <button
                [disabled]="i === 0"
                class="btn-arrow aspect-square swiper-prev"
                (click)="onNavigateSlide('prev')"
              >
                <memo-icon name="arrow" size="12px"></memo-icon>
              </button>
              <h2>第 {{ date.relativeIndex + 1 }} 天</h2>
              <button class="btn-arrow aspect-square swiper-next" (click)="onNavigateSlide('next')">
                <memo-icon name="arrow" size="12px"></memo-icon>
              </button>
            </header>
            <div class="member-list" [style.height.px]="virtualizer.getTotalSize()">
              @for (col of virtualizer.getVirtualItems(); track col.index) {
                @let member = createMembersSorting(date.formattedDate)()[col.index];
                <app-study-stats-list-item
                  memoRipple
                  [memberInfo]="member"
                  [lanternThemeEnable]="isLanternThemeVisible()"
                  [isFailed]="
                    isAlreadyHasResult(date, member) &&
                    !memberCompletionMap()[date.formattedDate]?.[member.userId]?.isFinished
                  "
                  [requiredDuration]="activityConfig?.dailyStudyDuration || 0"
                  [requiredVocCounts]="activityConfig?.dailyStudyVoc || 0"
                  [showRewardBadge]="isAlreadyHasResult(date, member)"
                  [studyStats]="memberCompletionMap()[date.formattedDate]?.[member.userId]"
                  [attr.data-index]="col.index"
                  [style.--item-size.px]="col.size"
                  [style.--item-start.px]="col.start"
                  class="virtual__item"
                  (dbclickAvatar)="onDbclickMember($event, member)"
                ></app-study-stats-list-item>
              }
            </div>
          </app-card>
        </ng-template>
      }
      <ng-template swiperSlide>
        <app-card>
          <header class="card-header">
            <button class="btn-arrow aspect-square swiper-prev" (click)="onNavigateSlide('prev')">
              <memo-icon name="arrow" size="12px"></memo-icon>
            </button>
            <h2>累计</h2>
            <button disabled class="btn-arrow aspect-square swiper-next" (click)="onNavigateSlide('next')">
              <memo-icon name="arrow" size="12px"></memo-icon>
            </button>
          </header>
          <div class="member-list" [style.height.px]="virtualizer.getTotalSize()">
            @for (col of virtualizer.getVirtualItems(); track col.index) {
              @let member = createMembersSorting('', true)()[col.index];
              <app-study-stats-list-item
                [memberInfo]="member"
                [studyReport]="memberDailySummaryStatsMap()?.[member.userId]"
                [showRewardBadge]="true"
                [showStudyBadge]="!!(unluckyMembers$ | async)?.includes(member.userId)"
                [studyBadgeText]="(studyBadgeText$ | async) || '非酋奖'"
                [isCurrentUser]="checkIsCurrentUser(member)"
                [lanternThemeEnable]="isLanternThemeVisible()"
                [attr.data-index]="col.index"
                [style.--item-size.px]="col.size"
                [style.--item-start.px]="col.start"
                class="virtual__item"
                (dbclickAvatar)="onDbclickMember($event, member)"
              ></app-study-stats-list-item>
            }
          </div>
        </app-card>
      </ng-template>
    </swiper>
  }
</div>
