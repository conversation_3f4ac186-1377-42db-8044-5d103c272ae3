import { SvgIcons } from '@ngneat/svg-icon'
import { getColorRGBFromColorType } from '../../../shared/helper/color'

export const ReportTitles = {
  PERFECT: {
    title: 'Perfect!',
    quotes: [
      // '等风来，不如追风去，期待你的下一次坚持！',
      // '优秀的人永远在闪闪发光，墨墨看到了你的闪耀！',
      // '路途虽远，脚步未停，一起迈向下一站。',
      // '好运藏在努力里，你的坚持万分值得。',
      // '让坚持成为一种习惯，你做到了！',
      // '相伴的结束，是旅程新的开始。',
      '你就学吧，谁能学得过你！',
    ],
    theme: getColorRGBFromColorType('red'),
    tag: '全勤猫头鹰',
    icon: 'perfect' as SvgIcons,
  },
  NICE: {
    title: 'Nice!',
    quotes: [
      // '差一点点，心有不甘，下次更要努力。',
      // '路途虽远，脚步未停，一起迈向下一站。',
      // '学习的路很长，庆幸我们能相伴一程。',
      // '并肩而行，让下一次不再遗憾！',
      // '等风来，不如追风去，期待你的下一次坚持！',
      '差点儿鸽了，还好没鸽！',
    ],
    theme: getColorRGBFromColorType('yellow'),
    tag: '好鸽们儿',
    icon: 'nice' as SvgIcons,
  },
  FAILED: {
    title: 'Keep it up!',
    quotes: [
      // '过往皆是序章，未来请继续坚持！',
      // '并肩而行，让下一次不再遗憾！',
      // '不必抱憾而行，挑战接踵而至。',
      '你就咕吧，谁能咕得过你！',
    ],
    theme: getColorRGBFromColorType('green'),
    tag: '咕咕鸽王',
    icon: 'failed' as SvgIcons,
  },
} as const
