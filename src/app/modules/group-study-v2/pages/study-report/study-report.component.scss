:host {
  --header-bg-height: 150px;

  display: block;
  width: 100%;
  padding-bottom: 40px;
}

:host > app-card.only-card {
  margin-top: calc(var(--header-bg-height) / 2 - (var(--gap-base) * 2));
}

:host > app-card {
  margin-top: 7px;
}

.rank-section {
  &:not(:first-child) {
    margin-top: calc(var(--gap-base) * 4);
  }

  h3 {
    font-size: 1rem;
    font-weight: bold;
    margin-top: 0;
    margin-bottom: 9px;
  }
}

cdk-row,
cdk-header-row,
cdk-footer-row,
.rich-cell
{
  display: flex;
  align-items: center;
}

cdk-cell, cdk-header-cell, cdk-footer-cell {
  width: 4em;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;


  &:first-of-type {
    width: 3em;
    text-align: left;
  }

  &:last-of-type {
    text-align: right;
  }

  &:nth-of-type(2) {
    flex: 1;
  }
}

cdk-cell:nth-of-type(2) {
  text-align: left;

  & > * {
    vertical-align: middle;
  }
}


cdk-header-row {
  color: var(--second-font-color);
  font-size: 12px;
  padding: 6px 0;
  border-bottom: 1px dashed var(--border-color);
}

cdk-row:first-of-type {
  margin-top: 6px;
}

cdk-row {
  font-weight: bold;
  font-size: 12px;
  padding: 7px 0;

  &.failed {
    opacity: 0.3;
  }

  &:not(.failed):nth-of-type(1) {
    color: var(--red-bgColor);
  }

  &:not(.failed):nth-of-type(2) {
    color: var(--yellow-bgColor);
  }

  &:not(.failed):nth-of-type(3) {
    color: var(--theme-green);
  }
}

.rich-cell {
  & > :not(:last-child) {
    margin-right: 0.5em;
  }
}
