import { Component, OnInit, computed } from '@angular/core'
import { toSignal } from '@angular/core/rxjs-interop'
import { ActivatedRoute } from '@angular/router'
import { EMPTY, NEVER, distinctUntilChanged, finalize, map, of, shareReplay, switchMap, takeUntil, tap } from 'rxjs'
import { DestroyService } from '../../../core/services/destroy.service'
import { UserService } from '../../../core/services/user.service'
import { GroupStudyClientService } from '../../../group-study/service/services/group-study-client.service'
import { getColorRGBFromColorType } from '../../../shared/helper/color'
import { fromSeconds, toStudyDate } from '../../../shared/helper/time'
import { createEmptyArray } from '../../../shared/helper/utils'
import { MemoLoadingService } from '../../../ui/loading/loading.service'
import { GroupStudyStorageService } from '../../services/group-study-storage.service'
import { getCompareFn } from '../../utils/diff'
import { SimpleDataSource, getMainStatsList, getSubStatsList } from './helper'
import { MemberStudyReport } from './type'

@Component({
  selector: 'app-study-report',
  templateUrl: './study-report.component.html',
  styleUrls: ['./study-report.component.scss'],
  providers: [
    DestroyService,
  ],
  standalone: false,
})
export class StudyReportComponent implements OnInit {
  private groupData$ = this.route.data.pipe(
    switchMap(data => this.gss.getGroupDataByGroupMeta(data.groupMeta, data.checkRedirect)),
    shareReplay(1),
  )

  readonly blankList = createEmptyArray(7)
  isInitialized = false

  private groupDataWithActivity$ = this.groupData$.pipe(
    switchMap(groupData => {
      const group = GroupStudyStorageService.getGroupFromGroupData(groupData)
      if (!group) {
        this.client.clientToast('没有队伍信息')
        return EMPTY
      }

      return this.gss.getActivity$(group.activityId)
        .pipe(
          map(activity => [groupData, group, activity] as const),
        )
    }),
    shareReplay(1),
  )

  private groupDataWithActivity = toSignal(this.groupDataWithActivity$)

  studyStats$ = this.groupDataWithActivity$.pipe(
    switchMap(([groupData, group, activity]) => this.gss.getGroupReports(group.id)
      .pipe(
        map((reports): MemberStudyReport[] => {
          return this.gss.generateFinalReport(groupData, activity, reports)
        }),
      ),
    ),
    tap(() => {
      this.client.clientEndLoading()
    }),
    distinctUntilChanged(),
    shareReplay(1),
  )

  currentUserStats$ = this.studyStats$.pipe(
    switchMap(stats => {
      const result = stats.find(v => v.userId === this.user.userInfo?.userId)
      return result ? of(result) : NEVER
    }),
    shareReplay(1),
  )

  unluckyMembers$ = this.groupData$.pipe(
    map(groupData => {
      const group = GroupStudyStorageService.getGroupFromGroupData(groupData)
      return group?.unluckyMembers
        ? group.unluckyMembers
        : []
    }),
    distinctUntilChanged(getCompareFn('deepEqual')),
    shareReplay(1),
  )

  currentUserNotInGroup$ = this.groupDataWithActivity$.pipe(
    map(([groupData, group, activity]) => {
      return activity.creator === this.userInfo?.userId
        && activity.config?.creatorAutoJoin === false
    }),
    distinctUntilChanged(),
    shareReplay(1),
  )

  groupDateRangeText = computed(() => {
    const data = this.groupDataWithActivity()
    if (!data) return ''
    const [_, group, activity] = data
    if (!group.studyStartTime || !activity.config) return ''
    const { studyDuration } = activity.config
    const studyDays = fromSeconds(studyDuration, 'day')
    const start = toStudyDate(group.studyStartTime)
    const end = start.add(studyDays, 'day')
    return `${start.format('MM.DD')} - ${end.format('MM.DD')}`
  })

  mainStatsItems$ = this.currentUserStats$.pipe(
    map(getMainStatsList),
  )

  subStatsItems$ = this.currentUserStats$.pipe(
    map(getSubStatsList),
  )

  displayCols: (keyof MemberStudyReport)[] = ['rank', 'name', 'totalVocCount', 'totalStudyDuration', 'totalRewarded']

  fieldTexts: Partial<Record<keyof MemberStudyReport, string>> = {
    rank: '排名',
    name: '成员',
    totalVocCount: '学习量',
    totalStudyDuration: '时长',
    totalRewarded: '奖励',
  }

  tableDataSource = new SimpleDataSource(this.studyStats$)

  get userInfo() {
    return this.user.userInfo
  }

  colorRGBMap = {
    'green-1': getColorRGBFromColorType('green-1'),
    yellow: getColorRGBFromColorType('yellow'),
    red: getColorRGBFromColorType('red'),
  }

  constructor(
    private gss: GroupStudyStorageService,
    private user: UserService,
    private route: ActivatedRoute,
    private client: GroupStudyClientService,
    private loading: MemoLoadingService,
    private destroy$: DestroyService,
  ) {
  }

  async ngOnInit(): Promise<void> {
    await this.loading.show()
    this.groupDataWithActivity$.pipe(
      finalize(() => {
        this.loading.dismiss()
        this.client.clientEndLoading()
      }),
      takeUntil(this.destroy$),
    )
      .subscribe(([groupData, group, activity]) => {
        const hasEventTrack = groupData.events?.some(event => event.eventType === 'REPORT' && event.creator === this.user.userInfo?.userId)
        if (!hasEventTrack) {
          this.gss.track('REPORT', group.groupId).subscribe()
          this.gss.clearReportsCache()
        }
        if (!this.isInitialized) {
          this.loading.dismiss()
          this.isInitialized = true
        }
      })
  }

  checkIsCurrentUser(userId: number): boolean {
    return this.gss.checkIsMemberTheCurrentUser(userId)
  }
}
