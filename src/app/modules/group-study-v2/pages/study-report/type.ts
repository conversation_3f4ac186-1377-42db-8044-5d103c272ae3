import { SvgIcons } from '@ngneat/svg-icon'
import { ExColorType } from '../../../group-study/models/model'
import { ISODate } from '../../../shared/types'
import { RewardReason } from '../../models/history.model'
import { UserBase } from '../../models/member.model'

export interface StatsItem {
  name: keyof MemberStudyReport
  text: string
  data: {
    value: string
    unit?: string
  }[]
}

export interface StudyLog {

  /**
   * 完成学习的时间
   */
  succeededTime?: ISODate

  /**
   * 签到头像对应的值
   */
  signAvatar?: number

  /**
   * 签到头像的图片 path
   */
  signAvatarImg: string

  /**
   * 当日实际学习时长，单位：分钟
   */
  studyDuration: number

  /**
   * 当日学习单词数
   */
  studyVocCounts: number

  /**
   * 当日奖励单词上限数
   */
  rewardedAmount: number

  rewardReason?: RewardReason
}

export interface MemberDailyStudyStats extends UserBase {

  /**
   * 是否已完成
   */
  isFinished: boolean

  /**
   * 是否已退出
   */
  isExited: boolean

  /**
   * 是否已签到
   */
  hasSigned: boolean

  /**
   * 已完成才有彩色皮肤
   */
  skin?: ExColorType

  /**
   * 后端返回 history 中的数据
   */
  studyLog?: StudyLog
}

export interface MemberStudyStatsBase extends UserBase {
  rank: number
  totalRewarded: number
  totalStudyDays: number
  /**
   * 总学习时长，单位：分钟
   */
  totalStudyDuration: number
}

export interface MemberStudyStats extends MemberStudyStatsBase {
  totalVocCount: number
  totalNewVocCount: number
  totalReviewedVocCount: number
}

export interface MemberStudyReport extends MemberStudyStatsBase {
  title: MemberStudyTitleConfig

  hardestDay: ISODate

  groupStudyDays: number

  totalFamiliarVocCount: number
  totalVocCount: number
  totalNewVocCount: number

  exitedTime?: ISODate
}

export interface MemberStudyTitleConfig {
  title: string
  quote: string
  /**
   * rgb
   */
  theme: string

  tag: string

  icon: SvgIcons
}
