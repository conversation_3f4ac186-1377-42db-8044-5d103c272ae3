import { DataSource } from '@angular/cdk/collections'
import dayjs from 'dayjs'
import { shuffle } from 'lodash-es'
import { Observable } from 'rxjs'
import { fromSeconds, toStudyDate } from '../../../shared/helper/time'
import { groupByKey } from '../../../shared/helper/utils'
import { ISODate } from '../../../shared/types'
import { Activity, ActivityType } from '../../models/activity.model'
import { GroupData } from '../../models/group.model'
import { Report } from '../../models/report.model'
import { GroupStudyStorageService } from '../../services/group-study-storage.service'
import { ReportTitles } from './constants'
import { MemberStudyReport, MemberStudyStats, StatsItem } from './type'

export function isTheCreatorNotJoiningGroup(activity: Activity, userId: number): boolean {
  return activity.config?.creatorAutoJoin === false && userId === activity.creator
}

export const getMainStatsList = (stats: MemberStudyReport): StatsItem[] => {
  return [
    {
      name: 'totalRewarded',
      text: '奖励单词上限',
      data: [
        {
          value: stats.totalRewarded + '',
          unit: '个',
        },
      ],
    },
    {
      name: 'rank',
      text: '幸运排名',
      data: [
        {
          value: `No.${stats.rank}`,
        },
      ],
    },
  ]
}

export const getSubStatsList = (stats: MemberStudyReport): StatsItem[][] => {
  const hardestDay = toStudyDate(stats.hardestDay)

  const result: StatsItem[][] = [
    [
      {
        name: 'totalStudyDays',
        text: '累计学习天数',
        data: [
          {
            value: stats.totalStudyDays + '',
            unit: `天/${stats.groupStudyDays}天`,
          },
        ],
      },
      {
        name: 'totalStudyDuration',
        text: '总学习时长',
        data: [
          {
            value: stats.totalStudyDuration + '',
            unit: '分钟',
          },
        ],
      },
      {
        name: 'hardestDay',
        text: '最努力那天',
        data: [
          {
            value: hardestDay.month() + 1 + '',
            unit: '月',
          },
          {
            value: hardestDay.date() + '',
            unit: '日',
          },
        ],
      },
    ],
    [
      {
        name: 'totalVocCount',
        text: '总计学习单词数',
        data: [
          {
            value: stats.totalVocCount + '',
            unit: '个',
          },
        ],
      },
      {
        name: 'totalNewVocCount',
        text: '新学单词数(去重)',
        data: [
          {
            value: stats.totalNewVocCount + '',
            unit: '个',
          },
        ],
      },
      {
        name: 'totalFamiliarVocCount',
        text: '复习单词数(去重)',
        data: [
          {
            value: stats.totalFamiliarVocCount + '',
            unit: '次',
          },
        ],
      },
    ],
  ]

  if (stats.totalFamiliarVocCount === 0) {
    result[1].pop()
    return result
  }

  return result
}

export class SimpleDataSource<T> extends DataSource<T> {
  /** Stream of data that is provided to the table. */
  data!: Observable<T[]>

  constructor(source: Observable<T[]>) {
    super()
    this.data = source
  }

  /** Connect function called by the table to retrieve one stream containing the data to render. */
  connect(): Observable<T[]> {
    return this.data
  }

  disconnect() {
    //
  }
}

export function sortByRecords(a: MemberStudyReport, b: MemberStudyReport) {
  const rewardedDelta = b.totalRewarded - a.totalRewarded

  const aExited = a.exitedTime ? 1 : -1
  const bExited = b.exitedTime ? 1 : -1

  if (aExited !== bExited) {
    return aExited - bExited
  }

  const sorted = rewardedDelta !== 0
    ? rewardedDelta
    : (b.totalStudyDuration * b.totalVocCount) - (a.totalStudyDuration * a.totalVocCount)

  if (sorted === 0 && aExited === 1 && bExited === 1) {
    return dayjs(a.exitedTime).isBefore(b.exitedTime) ? 1 : -1
  }

  return sorted
}

export function generateStudyReports<T extends ActivityType>(groupData: GroupData, activity: Activity<T>, reports: Report[]): MemberStudyReport[] {
  const historiesGroupByUserId = groupData.history && groupByKey(groupData.history, 'memberId')
  const activityConfig = activity.config
  const reportMap = reports.reduce((res, report) => {
    res[report.creator] = report
    return res
  }, {} as Record<number, Report>)
  const stats: MemberStudyReport[] = []
  const group = GroupStudyStorageService.getGroupFromGroupData(groupData)

  if (!group) {
    return []
  }

  for (const member of group.members) {
    if (activityConfig && 'creatorAutoJoin' in activityConfig && activityConfig?.creatorAutoJoin === false && member.userId === activity.creator) {
      continue
    }

    const histories = historiesGroupByUserId?.[member.userId] ?? []
    const userReport = reportMap[member.userId]

    let totalRewarded = 0
    let totalStudyDuration = 0
    let totalNewVocCount = 0
    let totalStudyDays = 0
    let hardestDay!: ISODate
    let tempLongestStudyTime = 0
    const totalFamiliarVocCount = userReport.familiarVocCount ?? 0

    for (const history of histories) {
      if (history.rewardStatus !== 'CANCELLED') {
        totalRewarded += history.rewardCount
      }
      if (history.studySucceededTime) {
        totalStudyDays += 1

        if (history.studyTime > tempLongestStudyTime) {
          tempLongestStudyTime = history.studyTime
          hardestDay = history.studySucceededTime
        }
      }
      totalStudyDuration += history.studyTime
      totalNewVocCount += history.learnedNewVocCount
    }

    const groupStudyDays = fromSeconds(activityConfig!.studyDuration + activityConfig!.testingDuration, 'day')
    // 断签天数
    const studyDaysDelta = groupStudyDays - totalStudyDays
    const titleConfig = studyDaysDelta >= (group.type === 'CUSTOM' ? 2 : 3)
      ? ReportTitles.FAILED
      : studyDaysDelta > 0
        ? ReportTitles.NICE
        : ReportTitles.PERFECT

    const result: MemberStudyReport = {
      title: {
        title: titleConfig.title,
        quote: shuffle(titleConfig.quotes)[0],
        theme: titleConfig.theme,
        tag: titleConfig.tag,
        icon: titleConfig.icon,
      },
      userId: member.userId,
      name: member.name,
      avatar: member.avatar,
      rank: 0,
      totalRewarded,
      totalStudyDuration: Math.floor(fromSeconds(totalStudyDuration, 'min')),
      totalVocCount: totalNewVocCount + totalFamiliarVocCount,
      totalNewVocCount,
      totalFamiliarVocCount,
      totalStudyDays,
      hardestDay,
      groupStudyDays,
      exitedTime: member.exitedTime,
    }

    stats.push(result)
  }

  stats.sort(sortByRecords)

  stats.forEach((v, i) => {
    v.rank = i + 1
  })

  return stats
}

export function generateStudyStatsMap(groupData: GroupData, activity: Activity): Record<number, MemberStudyStats> {
  const historiesGroupByUserId = groupData.history && groupByKey(groupData.history, 'memberId')
  const statsMap: Record<number, MemberStudyStats> = {}
  const group = GroupStudyStorageService.getGroupFromGroupData(groupData)

  if (!group) {
    return {}
  }

  for (const member of group.members) {
    if (isTheCreatorNotJoiningGroup(activity, member.userId)) {
      continue
    }

    const histories = historiesGroupByUserId?.[member.userId] ?? []

    let totalRewarded = 0
    let totalStudyDuration = 0
    let totalNewVocCount = 0
    let totalReviewedVocCount = 0
    let totalStudyDays = 0

    for (const history of histories) {
      // 已退出的用户没有奖励
      if (history.rewardStatus !== 'CANCELLED' && member.status === 'NORMAL') {
        totalRewarded += history.rewardCount
      }
      if (history.studySucceededTime) {
        totalStudyDays += 1
      }
      totalStudyDuration += fromSeconds(history.studyTime, 'min')
      totalNewVocCount += history.learnedNewVocCount
      totalReviewedVocCount += history.learnedVocCount
    }

    const result: MemberStudyStats = {
      userId: member.userId,
      name: member.name,
      avatar: member.avatar,
      rank: 0,
      totalRewarded,
      totalStudyDuration: Math.floor(totalStudyDuration),
      totalVocCount: totalNewVocCount + totalReviewedVocCount,
      totalNewVocCount,
      totalReviewedVocCount,
      totalStudyDays,
    }

    statsMap[member.userId] = result
  }

  return statsMap
}
