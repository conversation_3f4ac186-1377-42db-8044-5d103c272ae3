<app-report-page
  [currentUserNotInGroup]="!!(currentUserNotInGroup$ | async)"
  [currentUserStats]="(currentUserStats$ | async) || undefined"
  [mainStatsItems]="(mainStatsItems$ | async) || []"
  [subStatsItemChunks]="(subStatsItems$ | async) || []"
  [unluckyMembers]="(unluckyMembers$ | async) || []"
  [groupDateRangeText]="groupDateRangeText()"
  [userInfo]="userInfo"
></app-report-page>

<app-card [class.only-card]="!!(currentUserNotInGroup$ | async)">
  @if (studyStats$ | async) {
    <section class="rank-section">
      <h3>队伍排名榜</h3>
      <cdk-table class="rank-chart" [dataSource]="tableDataSource">
        @for (item of displayCols; track item) {
          <ng-container [cdkColumnDef]="item">
            <cdk-header-cell *cdkHeaderCellDef>{{ fieldTexts[item] }}</cdk-header-cell>
            <cdk-cell *cdkCellDef="let element" [class.rich-cell]="item === 'name'">
              @if (item === 'name') {
                <app-member-avatar
                  [style.--border-size.px]="1"
                  [imgSrc]="element.avatar"
                  [size]="22"
                ></app-member-avatar>
                <span class="truncate">{{ element[item] }}</span>
                @if ((unluckyMembers$ | async)?.includes(element.userId)) {
                  <memo-study-badge
                    [style.zoom]="0.8"
                    content="非酋奖"
                    [isCurrentUser]="checkIsCurrentUser(element.userId)"
                    [paddingY]="'6px'"
                  ></memo-study-badge>
                }
              } @else {
                {{ element[item] }}
              }
            </cdk-cell>
          </ng-container>
        }

        <cdk-header-row *cdkHeaderRowDef="displayCols"></cdk-header-row>
        <cdk-row *cdkRowDef="let row; columns: displayCols" [class.failed]="!!row.exitedTime"></cdk-row>
      </cdk-table>
    </section>
  } @else {
    <section class="rank-section">
      <ion-skeleton-text [animated]="true" style="width: 5em"></ion-skeleton-text>
      @for (item of blankList; track $index) {
        <ion-skeleton-text
          [animated]="true"
          style="width: 100%; margin-top: 10px; line-height: 1.3"
        ></ion-skeleton-text>
      }
    </section>
  }
</app-card>
