import { findLastIndex } from 'lodash-es'
import { ColorTypeBase, ExColorType } from '../../../group-study/models/model'
import { fromSeconds, toStudyDate } from '../../../shared/helper/time'
import { reArrangeListByItemIndex } from '../../../shared/helper/utils'
import { GroupStatus } from '../../models/group.model'
import { History } from '../../models/history.model'
import { Member } from '../../models/member.model'
import { checkIsMemberExitedAtSomeTime } from '../../utils/study'
import { MemberDailyStudyStats } from '../study-report/type'

interface ColorifierGroup<T> {
  value: number
  color?: ColorTypeBase
  level?: number
  group: T[]
}

export const DATE_FORMAT = 'YYYY.MM.DD'
export const GROUP_STATUS_EARLY_LEARNING: GroupStatus[] = ['GROUP_SUCCEEDED', 'TESTING', 'LEARNING']
export const GROUP_STATUS_DURING_LEARNING: GroupStatus[] = ['TESTING', 'LEARNING']
export const GROUP_STATUS_LATER_LEARNING: GroupStatus[] = ['TESTING', 'LEARNING', 'LEARN_SUCCEED']

export function generateStudyStatsFromRecord(record: History, member: Member): MemberDailyStudyStats {
  const hasSigned = record.isSigned
  const isFinished = !!record.studySucceededTime
  const signAvatarImg = record.signAvatar === undefined || record.signAvatar === null
    ? 'question_mark.png'
    : `img_${record.signAvatar}${(hasSigned && !isFinished) ? '_grey' : ''}.png`
  const isExited = checkIsMemberExitedAtSomeTime(member, toStudyDate(record.createdTime))

  return {
    userId: member.userId,
    avatar: member.avatar,
    name: member.name,
    hasSigned,
    isFinished,
    isExited,
    studyLog: {
      signAvatar: record.signAvatar,
      signAvatarImg,
      studyDuration: fromSeconds(record.studyTime, 'min'),
      studyVocCounts: record.learnedVocCount + record.learnedNewVocCount,
      succeededTime: record.studySucceededTime,
      rewardedAmount: record.rewardStatus !== 'CANCELLED' && record.rewardCount ? record.rewardCount : 0,
      rewardReason: record.rewardReason,
    },
  }
}

/**
 * 根据产品需求，当前用户居中显示，当前用户不在队伍中的话则以将第一个用户放在中间
 * @param list
 * @returns [重新排列后的列表, 居中显示的索引]
 */
export function reArrangeList<T>(list: T[], opts: {
  targetItemIndex?: number
  itemsPerRow?: number
  offset?: number
} = {}): [T[], number] {
  const {
    targetItemIndex = -1,
    offset = 0,
    itemsPerRow = list.length,
  } = opts

  const totalLength = list.length + offset
  const targetIndex = targetItemIndex === -1
    ? 0
    : targetItemIndex

  let finalIndex = Math.floor(list.length / 2)

  // 理论上 calendar 相关的排列会走这里
  // 多行的情况下也要考虑在目标项的那一行里是否在中间靠左
  if (itemsPerRow !== totalLength) {
    const toIndex = finalIndex
    const rowLength = Math.min(itemsPerRow, totalLength)
    const targetIndexInOneRow = toIndex % rowLength
    const trimedCount = toIndex - targetIndexInOneRow

    const medianIndexInOneRow = rowLength / 2
    finalIndex = (targetIndexInOneRow >= medianIndexInOneRow)
      ? trimedCount + Math.floor(medianIndexInOneRow)
      : toIndex
  } else if (list.length % 2 === 0) {
    finalIndex -= 1
  }

  const reArrangedList = reArrangeListByItemIndex(list, targetIndex, finalIndex)

  return [reArrangedList, finalIndex]
}

/**
 * 从起点往右开始染色，依次为红色、黄色、绿色，然后再黄绿黄绿地来
 *
 * 起点左方的会先保存在 pendingStats 中，等第一次遍历完后再操作
 *
 * @deprecated
 */
export class StatsColorifier {
  private lastColor?: ExColorType
  private lastStats?: MemberDailyStudyStats
  private startingColor: ExColorType = 'red'
  pendingStats: MemberDailyStudyStats[] = []

  setStartingColor(color: ExColorType) {
    this.startingColor = color
  }

  colorify(stats: MemberDailyStudyStats, isStartingPoint = false) {
    // ⬇ 标明起点
    if (isStartingPoint) {
      stats.skin = this.startingColor
      this.lastColor = this.startingColor

      // ⬇ 说明未完成任务
    } else if (!stats.isFinished || stats.studyLog?.signAvatar === undefined) {

      // ⬇ 说明在起点左方
    } else if (this.lastStats === undefined || this.lastColor === undefined) {
      this.pendingStats.push(stats)

      // ⬇ 说明是连续签到头像相同，颜色不变
    } else if (stats.studyLog?.signAvatar === this.lastStats.studyLog?.signAvatar) {
      stats.skin = this.lastColor

      // ⬇ 根据上一次的颜色来获取本次颜色
    } else {
      stats.skin = this.lastColor = StatsColorifier.getNextColor(this.lastColor)
    }

    this.lastStats = stats
  }

  resolvePendingStats(startingPointPredicate: (stats: MemberDailyStudyStats, index: number) => boolean) {
    const { pendingStats } = this

    if (pendingStats.length === 0) {
      return
    }

    // [****#***]
    const toResolvedList = pendingStats.filter((v, i) => startingPointPredicate(v, i) || v.studyLog !== undefined)

    // # index
    const startingPointIndex = pendingStats.findIndex(startingPointPredicate)

    // #
    const startingStats = pendingStats[startingPointIndex]
    // toResolvedList: [****]
    // startFromRedList: [#***]
    const startFromRedList = toResolvedList.splice(toResolvedList.indexOf(startingStats))

    // [****] === [***#?]
    const hasSameColorAsStartingPoint = startingStats.studyLog !== undefined && !startingStats.isExited && toResolvedList.length
      ? toResolvedList[toResolvedList.length - 1].studyLog?.signAvatar === startingStats.studyLog?.signAvatar
      : false

    if (hasSameColorAsStartingPoint) {
      // [* * ##]
      const gapIndex = findLastIndex(toResolvedList, item => item.studyLog?.signAvatar !== startingStats.studyLog?.signAvatar)
      // toResolvedList: [**]
      // sameColorList: [##]
      const sameColorList = toResolvedList.splice(gapIndex + 1)
      sameColorList.forEach(stats => stats.skin = this.startingColor)
    }

    // 说明在原本的列表中，startingPoint 前面的都是红色
    // 因此需要判断一下 startFromRedList 的最后是否仍然有相同颜色的元素
    if (
      (hasSameColorAsStartingPoint && toResolvedList.length === 0)
      || startingPointIndex === 0
    ) {
      const gapIndexInTargetList = findLastIndex(startFromRedList, item => item.studyLog?.signAvatar !== startingStats.studyLog?.signAvatar)
      if (gapIndexInTargetList !== startFromRedList.length - 1) {
        startFromRedList.splice(gapIndexInTargetList + 1).forEach(stats => stats.skin = this.startingColor)
      }
    }
    startFromRedList.forEach((stats, i) => this.colorify(stats, i === 0))
    toResolvedList.forEach(stats => this.colorify(stats))
  }

  registerStats(stats: MemberDailyStudyStats, toEnd = true) {
    toEnd
      ? this.pendingStats.push(stats)
      : this.pendingStats.unshift(stats)
  }

  static getNextColor(color: ExColorType): ExColorType {
    return color === 'red' || color === 'green-1'
      ? 'yellow'
      : 'green-1'
  }

  reset(force = false) {
    this.lastColor = this.lastStats = undefined
    this.startingColor = 'red'

    if (force) {
      this.pendingStats.length = 0
    }
  }
}

export const ColorifyStrategy = {
  SIGN_AVATAR: 'SIGN_AVATAR',
  REWARD: 'REWARD',
} as const

export type ColorifyStrategyType = typeof ColorifyStrategy[keyof typeof ColorifyStrategy]

export class StudyColorifier {
  private pendingStats: MemberDailyStudyStats[] = []

  resolvePendingStats(strategy: ColorifyStrategyType = ColorifyStrategy.SIGN_AVATAR) {
    if (this.pendingStats.length === 0) {
      return
    }

    const groupValuePredicate: (stats: MemberDailyStudyStats) => number = strategy === ColorifyStrategy.REWARD
      ? stats => stats.isFinished ? stats.studyLog?.rewardedAmount ?? -1 : -1
      : stats => stats.isFinished ? stats.studyLog?.signAvatar ?? -1 : -1

    const consecutiveGroups: ColorifierGroup<MemberDailyStudyStats>[] = strategy === ColorifyStrategy.REWARD
      ? this.pendingStats.map(stats => ({
          value: groupValuePredicate(stats),
          group: [stats],
        }))
      : getConsecutiveGroup(
          this.pendingStats,
          groupValuePredicate,
        )
    consecutiveGroups.forEach((group, i) => {
      const prevGroup = i === 0 ? undefined : consecutiveGroups[i - 1]
      const groupColorBase = strategy === ColorifyStrategy.REWARD
        ? getColorByRewardCount(group.value)
        : getColorByConsecutiveCount(group.group.length)
      let level = 1
      if (prevGroup && prevGroup.color === groupColorBase && prevGroup.level !== 2) {
        level = 2
      }

      if (i === consecutiveGroups.length - 1) {
        const firstGroup = consecutiveGroups[0]
        if (firstGroup.color === groupColorBase && firstGroup.level === level) {
          level = 3
        }
      }

      group.color = groupColorBase
      group.level = level
      group.group.forEach(stats => {
        stats.skin = `${groupColorBase}/${level}`
      })
    })
  }

  registerStats(stats: MemberDailyStudyStats) {
    this.pendingStats.push(stats)
  }

  reset() {
    this.pendingStats.length = 0
  }
}

function getConsecutiveGroup<T>(
  arr: T[],
  groupValuePredicate: (item: T) => number,
): ColorifierGroup<T>[] {
  // eg. [1, 1, 2, 2, 2, 3, 3, 1]
  const cloneList = arr.slice()
  const lastDifferentIndex = findLastIndex(cloneList, item => {
    const firstOne = cloneList[0]
    return groupValuePredicate(item) !== groupValuePredicate(firstOne)
  })

  // 将队伍最后与队伍第一位成员相同颜色的成员挪到队伍前面
  // eg. [1, 1, 1, 2, 2, 2, 3, 3]
  const newArr = arr.slice()
  newArr.unshift(...newArr.splice(lastDifferentIndex + 1))

  // eg. [[1, 1, 1], [2, 2, 2], [3, 3]]
  return newArr.reduce((acc, v, i, li) => {
    const lastGroup = acc.length ? acc[acc.length - 1] : null
    const groupValue = groupValuePredicate(v)
    if (!lastGroup || lastGroup.value !== groupValue) {
      acc.push({
        value: groupValue,
        group: [v],
      })
    } else {
      lastGroup.group.push(v)
    }
    return acc
  }, [] as ColorifierGroup<T>[])
}

function getColorByRewardCount(reward: number): ColorTypeBase {
  if (reward === 0) {
    return 'grey-2'
  }

  if (reward === 1) {
    return 'green-1'
  }

  if (reward === 3) {
    return 'yellow'
  }

  return 'red'
}

function getColorByConsecutiveCount(consecutiveCount: number): ColorTypeBase {
  if (consecutiveCount === 0) {
    return 'grey-2'
  }

  if (consecutiveCount === 1) {
    return 'green-1'
  }

  if (consecutiveCount === 2) {
    return 'yellow'
  }

  return 'red'
}
