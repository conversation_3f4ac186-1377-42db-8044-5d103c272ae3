:host {
  --gap-base: 5px;

  display: block;
  width: 100%;
  overflow-x: hidden;
}

:host > :not(:last-child) {
  margin-bottom: calc(var(--gap-base) * 2);
}

app-card {
  padding: calc(var(--gap-base) * 3);
}

.calendar-card-container {
  padding: 0 var(--page-padding);
}

.summary-handlers {
  position: relative;
  padding-top: calc(var(--gap-base) * 8);

  button {
    font-size: 14px;
    line-height: 1;
  }
}

.reward-amount {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: inline-block;
  text-align: center;
  color: var(--red-bgColor);
  font-size: 20px;

  animation: slide-in-bottom 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) both;
}

@keyframes slide-in-bottom {
  0% {
    transform: translateY(100%) scale(0);
    opacity: 0;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.refund-msg {
  margin-top: 10px;
  margin-bottom: -20px;
  font-size: 12px;
  color: var(--red-bgColor);
  width: 80%;
}
