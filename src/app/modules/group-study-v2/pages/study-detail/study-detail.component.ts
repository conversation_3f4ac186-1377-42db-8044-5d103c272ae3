import { ChangeDetectorRef, Component, DestroyRef, Inject, OnInit, Signal, computed, inject, signal, viewChild } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { Modules, isBelowSpecifiedAppVersion } from '@maimemo/client-frontend-bridge'
import { EMPTY, debounceTime, distinctUntilChanged, filter, finalize, map, shareReplay, switchMap, take } from 'rxjs'
import Swiper from 'swiper'
import { SwiperComponent } from 'swiper/angular'
import { VirtualOptions } from 'swiper/types'
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop'
import { AppService } from '../../../../app.service'
import { NAME_FIRST_DAY_REWARD_TEASER, StorageService } from '../../../core/services/storage.service'
import { UserService } from '../../../core/services/user.service'
import { ROUTE_BASE_HREF } from '../../../entry/group-study.module'
import { GroupStudyClientService } from '../../../group-study/service/services/group-study-client.service'
import { checkIsFirstDayIntoGroup, recordFirstDayIntoGroup } from '../../../shared/helper/group'
import { getTimerToNextStudyDay, toStudyDate } from '../../../shared/helper/time'
import { celebrate, wrapLinkToExternal } from '../../../shared/helper/utils'
import { MemoModalService } from '../../../ui/modal/modal.service'
import { CouponExchangeComponent } from '../../components/coupon-exchange/coupon-exchange.component'
import { InteractiveBoardService } from '../../components/interactive-board/interactive-board.service'
import { Activity, ActivityType, CustomGroupStudyActivityConfig } from '../../models/activity.model'
import { Coupon } from '../../models/coupon.model'
import { GroupData } from '../../models/group.model'
import { History } from '../../models/history.model'
import { Member } from '../../models/member.model'
import { ChatService } from '../../services/chat.service'
import { ActivityUIConfig, GroupStudyStorageService } from '../../services/group-study-storage.service'
import { GroupStudyTickleService } from '../../services/group-study-tickle.service'
import { resolveGroupMetaFromQueryParams } from '../../utils/activity.resolver'
import { getCompareFn, isEqual } from '../../utils/diff'
import { MemberDailyStudyStats, MemberStudyReport, MemberStudyStats } from '../study-report/type'
import { CalendarDateItem } from '../../utils/study'

@Component({
  selector: 'app-study-detail',
  templateUrl: './study-detail.component.html',
  styleUrls: ['./study-detail.component.scss'],
  standalone: false,
})
export class StudyDetailComponent implements OnInit {
  private dateSwiper = viewChild(SwiperComponent)

  private destroyRef = inject(DestroyRef)

  isInitialized = false

  /**
   * 记录每天每个用户的学习记录
   *
   * {
   *  [DATE_FORMAT]: {
   *    [userId]: MemberDailyStudyStats
   *  }
   * }
   */
  memberCompletionMap = signal<Record<string, Record<number, MemberDailyStudyStats>>>({})

  /**
   * 记录每个用户累计的学习数据
   *
   * {
   *  [userId]: MemberStudyStats
   * }
   */
  memberDailySummaryStatsMap = signal<Record<number, MemberStudyStats>>({})

  memberFinalReportsMap = signal<Record<number, MemberStudyReport>>({})

  initialDateIndex = 0
  activeDateIndex = signal(-1)
  activeDateIndexForCalendar = computed(() => {
    return this.activeDateIndex() === this.datesWithRecord().length ? -2 : this.activeDateIndex()
  })

  currentUserSummaryStats = computed(() => {
    const user = this.currentUserInfo()
    if (!user) return undefined
    return this.memberDailySummaryStatsMap()[user.userId]
  })

  currentUserSFinalReports = computed(() => {
    const user = this.currentUserInfo()
    if (!user) return undefined
    return this.memberFinalReportsMap()[user.userId]
  })

  datesWithRecord = signal<CalendarDateItem[]>([])
  calendars = signal<CalendarDateItem[]>([])
  summaryDateItem: CalendarDateItem = {
    relativeIndex: -2,
    date: '',
    offsetToToday: -2,
    formattedDate: '',
    desc: '累计获得',
  }

  activityConfig?: CustomGroupStudyActivityConfig
  private activityId?: string
  isShowModal = false
  virtualSwiperOptions: VirtualOptions = {
    enabled: true,
    addSlidesAfter: 2,
    addSlidesBefore: 2,
  }

  /**
   * 是否获取过奖励，默认为 true ，不允许点击按钮
   *
   */
  isRewarded = true

  /**
   * 是否正在领取奖励
   */
  isRequestingReward = false

  /**
   * 是否有权利获取奖励，没有加入队伍的队伍创建者为 false，已领取的也为 false
   */
  get isLegitToGetReward(): boolean {
    return !(this.activityConfig?.creatorAutoJoin === false && this.isCurrentUserTheCreator)
      && !this.isRewarded
      && !this.isCurrentUserExited
  }

  /**
   * 当前是否能获取奖励，学习完成才能获取
   */
  get isAbleToGetReward(): boolean {
    return this.isGroupStudyFinished
  }

  isCurrentUserTheCreator = false
  isCurrentUserInGroup = false
  isCurrentUserExited = false
  isGroupStudyFinished = false
  isGroupFull = false
  rewardAmount = -1
  activityUIConfig?: Signal<ActivityUIConfig>
  nextActivity?: Activity<ActivityType>

  isCommercialGroup = computed(() => !!this.commercialBanner())
  commercialBanner = computed(() => this.group()?.promotion?.banners?.[0] || '')
  commercialBannerLink = computed(() => wrapLinkToExternal(this.group()?.promotion?.link))

  private groupData$ = this.route.queryParamMap.pipe(
    switchMap(paramsMap => {
      const { checkRedirect } = this.route.snapshot.data
      const groupMeta = resolveGroupMetaFromQueryParams(paramsMap)
      return this.gss.getGroupDataByGroupMeta(groupMeta, !!checkRedirect)
    }),
    shareReplay(1),
  )

  private group$ = this.groupData$.pipe(
    map(groupData => GroupStudyStorageService.getGroupFromGroupData(groupData)),
    filter(Boolean),
    distinctUntilChanged(getCompareFn('group')),
    shareReplay(1),
  )

  groupDataWithActivity$ = this.groupData$
    .pipe(
      switchMap(groupData => {
        const group = GroupStudyStorageService.getGroupFromGroupData(groupData)

        if (!group) {
          this.client.clientToast('没有队伍信息')
          return EMPTY
        }

        return this.gss.getActivity$(group.activityId)
          .pipe(
            map(activity => [groupData, group, activity] as const),
          )
      }),
      shareReplay(1),
    )

  group = toSignal(this.group$)
  activity = toSignal(this.groupDataWithActivity$.pipe(map(r => r[2])))

  members$ = this.groupDataWithActivity$.pipe(
    map(([groupData, group, activity]) => this.gss.filterMembersByConfig(group.members, activity)),
    distinctUntilChanged(getCompareFn('members')),
    shareReplay(1),
  )

  unluckyMembers$ = this.group$.pipe(
    map(group => group.unluckyMembers ?? []),
    distinctUntilChanged(getCompareFn('deepEqual')),
    shareReplay(1),
  )

  studyBadgeText$ = this.group$.pipe(
    map(group => group && group.status !== 'LEARN_SUCCEED' ? '非酋奖提名' : '非酋奖'),
    distinctUntilChanged(),
    shareReplay(1),
  )

  pagePadding = computed(() => this.app.pageGap())

  currentUserInfo = computed(() => this.user.userInfoSignal())

  get isRefundInfoVisible(): boolean {
    return this.isCurrentUserTheCreator
      && this.activityConfig?.paymentType === 'CREATOR'
      && !this.isGroupFull
  }

  get isLanternThemeVisible() {
    return this.gss.isLanternThemeVisible
  }

  refundMessage$ = this.groupDataWithActivity$.pipe(
    debounceTime(300),
    map(([groupData, group, activity]) => {
      const { events = [] } = groupData
      const refundEvent = events.find(event => event.eventType === 'REFUND' && event.creator === this.currentUserInfo()?.userId)
      if (!refundEvent) {
        return ''
      }

      const refundInfo = GroupStudyStorageService.getRefundInfoFromEvent(refundEvent)
      if (Number(refundInfo.amount) === 0 || refundInfo.refund_status === 'REFUNDING') {
        return ''
      }

      const refundMembersCount = Number(refundInfo.members_count)
      if (
        this.isCurrentUserTheCreator
        && activity.config?.paymentType === 'CREATOR'
        && refundMembersCount > 1
      ) {
        return `你和另外 ${refundMembersCount - 1} 位成员参与组队失败，已原路退款 ${refundInfo.amount} 元给你，其余人将继续组队学习。`
      }

      return `你未通过磨合期，已原路退款 ${refundInfo.amount} 元给你，其余人将继续组队学习。`
    }),
    distinctUntilChanged(),
    shareReplay(1),
  )

  // private studyWorker = this.workerService.getWorker('study')

  constructor(
    @Inject(ROUTE_BASE_HREF) private routeBase: string,
    private router: Router,
    private route: ActivatedRoute,
    private app: AppService,
    private gss: GroupStudyStorageService,
    private chat: ChatService,
    private user: UserService,
    private client: GroupStudyClientService,
    private board: InteractiveBoardService,
    private tickle: GroupStudyTickleService,
    private modal: MemoModalService,
    private cdr: ChangeDetectorRef,
    public storage: StorageService,
  ) {
    this.group$
      .pipe(
        takeUntilDestroyed(),
      )
      .subscribe(group => {
        if (group.status === 'TEST_FAILED') {
          this.gss.navigateByGroupStatus(group)
          return
        }
        this.app.setTitle(group.name)
      })
  }

  isShowBanner() {
    return !this.storage.isNotShow(NAME_FIRST_DAY_REWARD_TEASER)
  }

  closeBanner() {
    this.storage.updateIsNotShow(NAME_FIRST_DAY_REWARD_TEASER)
  }

  async ngOnInit() {
    this.client.clientUploadStudyRecord()

    this.groupDataWithActivity$
      .pipe(
        switchMap(([groupData, group, activity]) => {
          return this.members$.pipe(
            map(members => [groupData, group, activity, members] as const),
          )
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(async ([groupData, group, activity, members]) => {
        await this.updateCalendars(groupData, activity)
        this.activityConfig = activity?.config

        await this.storeMemberStudyStats(members, groupData.history ?? [], this.calendars())

        const currentUserStudyInfo = this.gss.getMemberInfoById(group.groupId, this.currentUserInfo()!.userId)
        this.isCurrentUserExited = currentUserStudyInfo?.status === 'EXITED'
        this.isGroupFull = members.length === this.activityConfig?.groupSize

        // 组件初始化时优先使用缓存，因为有可能是单纯切换页面了
        const preferCache = Object.keys(this.memberDailySummaryStatsMap()).length === 0
        this.memberDailySummaryStatsMap.set(this.gss.generateDailyStudyStatsMap(groupData, activity, preferCache))

        this.isGroupStudyFinished = group.status === 'LEARN_SUCCEED'
        this.isCurrentUserTheCreator = this.gss.checkIsActivityCreator(this.currentUserInfo()?.userId, group.activityId)

        this.activityUIConfig = this.gss.getActivityUIConfig(group.activityId)

        if (group.status === 'TESTING' && checkIsFirstDayIntoGroup(group.groupId)) {
          recordFirstDayIntoGroup(group.groupId)
          Modules.common.alert({
            title: '组队首日随机奖励，次日起开始邻近队员相同头像奖励',
            message: '',
            buttons: [{ id: 'ok', text: '我知道了' }],
            button_callback: () => {
              // noop
            },
          })
        }

        if (!this.isInitialized && this.activityId !== group.activityId) {
          this.activityId = group.activityId
          this.isInitialized = true
        }
        this.cdr.detectChanges()
      })

    this.groupDataWithActivity$.pipe(
      filter(([_, group]) => group.status === 'LEARN_SUCCEED'),
      distinctUntilChanged((a, b) => a[1].groupId === b[1].groupId),
      switchMap(([groupData, group, activity]) =>
        this.gss.getGroupReports(group.id)
          .pipe(
            map((reports = []) => [groupData, activity, reports] as const),
          ),
      ),
      takeUntilDestroyed(this.destroyRef),
    )
      .subscribe(([groupData, activity, reports]) => {
        const myReport = reports.find(v => v.creator === this.currentUserInfo()?.userId)
        this.isRewarded = !!myReport?.rewardedTime

        this.gss.generateFinalReport(groupData, activity, reports).forEach(finalReport => {
          this.memberFinalReportsMap.update(v => {
            v[finalReport.userId] = finalReport
            return v
          })
        })
      })

    this.groupData$
      .pipe(
        switchMap(groupData => {
          const group = GroupStudyStorageService.getGroupFromGroupData(groupData)
          // 非学习期间就不用 subscribe 了
          if (!group || group.status !== 'LEARNING') {
            return EMPTY
          }

          return getTimerToNextStudyDay(toStudyDate(Date.now()))
        }),
        map(duration => duration.as('second') <= 0),
        filter(isNextStudyDay => isNextStudyDay),
        switchMap(() => this.groupDataWithActivity$),
        take(1),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(async ([groupData, group, activity]) => {
        await this.updateCalendars(groupData, activity)
        this.cdr.detectChanges()
      })

    this.group$
      .pipe(
        debounceTime(500),
        filter(group => GroupStudyStorageService.isGroupActive(group.status)),
        switchMap(group => this.gss.getNextActiveActivity(group)),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(activity => {
        this.nextActivity = activity
      })
  }

  onDateSlideChange(swiper: Swiper) {
    this.activeDateIndex.set(swiper.activeIndex)
  }

  onClickCalendarDate([index, item]: [number, CalendarDateItem | undefined]) {
    const canAccessAllDate = this.calendars().length > 0 && this.calendars().length === this.datesWithRecord().length

    if (index === -2 || item === undefined) {
      this.slideToIndex(this.datesWithRecord().length)
      return
    }

    if (!canAccessAllDate && (item.offsetToToday > 0 || index === this.activeDateIndex())) {
      return
    }

    this.slideToIndex(index)
  }

  private slideToIndex(index: number) {
    this.dateSwiper()?.swiperRef.slideTo(index)
    this.activeDateIndex.set(index)
    setTimeout(() => {
      this.dateSwiper()?.swiperRef.updateAutoHeight()
    })
  }

  private async storeMemberStudyStats(members: Member[], histories: History[], calendarItems: CalendarDateItem[]) {
    const {
      statsMap = {},
      isCurrentUserInGroup = false,
    } = this.gss.generateDailyStatsData(members, histories, calendarItems)

    if (!isEqual(this.memberCompletionMap(), statsMap)) {
      this.memberCompletionMap.set(statsMap)
    }
    this.isCurrentUserInGroup = isCurrentUserInGroup
  }

  checkIsMemberTheCurrentUser(userId: number) {
    return userId === this.currentUserInfo()?.userId
  }

  private async updateCalendars(groupData: GroupData, activity: Activity) {
    const group = GroupStudyStorageService.getGroupFromGroupData(groupData)
    if (!group) {
      return
    }

    const {
      fullCalendars = [],
      datesWithRecord = [],
      todayIndex = -1,
      summaryDateItem,
    } = this.gss.generateCalendars(group, activity)

    if (!isEqual(fullCalendars, this.calendars())) {
      this.calendars.set(fullCalendars)
    }
    if (!isEqual(datesWithRecord, this.datesWithRecord())) {
      this.datesWithRecord.set(datesWithRecord)
    }

    if (summaryDateItem) {
      this.summaryDateItem = summaryDateItem
    }

    if (this.isInitialized) {
      return
    }

    const targetIndex = todayIndex === -1 || group.status === 'LEARN_SUCCEED'
      ? datesWithRecord.length + 1
      : todayIndex

    if (targetIndex === datesWithRecord.length + 1) {
      this.gss.track('STUDY_RECORD', group.groupId).subscribe()
    }

    this.initialDateIndex = targetIndex !== -1 ? targetIndex : 0
    this.activeDateIndex.set(this.initialDateIndex)
  }

  onClickGetReward() {
    this.isRequestingReward = true

    this.group$
      .pipe(
        switchMap(group => this.gss.getRewards(group.id)),
        finalize(() => {
          this.isRequestingReward = false
        }),
        take(1),
      )
      .subscribe({
        next: ({ reward_count, coupons }) => {
          this.rewardAmount = reward_count
          this.gss.clearReportsCache()
          celebrate()
          this.isRewarded = true

          setTimeout(() => {
            this.rewardAmount = -1
            if (coupons?.length) {
              this.showCouponsExchangeModal(coupons)
            }
          }, 2000)
        },
        error: err => {
          const msg = err?.error?.errors?.[0]?.message || '未知错误'
          this.client.clientToast(msg)
        },
      })
  }

  handleClick(page: 'members' | 'interaction' | 'switch', params: Record<string, string> = {}) {
    if (page === 'members') {
      const url = this.routeBase + page
      this.router.navigate([url], {
        queryParams: params,
        queryParamsHandling: 'merge',
      })
      return
    }

    if (page === 'interaction') {
      this.groupDataWithActivity$.pipe(
        take(1),
        switchMap(([groupData, group, activity]) => {
          return this.gss.hasPermissionToChat(group.groupId) && !isBelowSpecifiedAppVersion('5.2.0')
            ? this.chat.navigateToClientChat(group, this.gss.filterMembersByConfig(group.members, activity).length)
            : this.board.showInteractiveBoard(group, groupData.events ?? [])
        }),
      )
        .subscribe()
      return
    }

    if (page === 'switch') {
      const activity = this.nextActivity!
      this.resetStates()
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: {
          activity_id: activity.id,
        },
        replaceUrl: true,
      })
    }
  }

  private resetStates() {
    this.gss.clearPollingSubscriptions()
    this.gss.clearCaches()
    this.isInitialized = false
    this.activeDateIndex.set(-1)
    this.isRewarded = true
  }

  onDbClickMember(payload: { targetMember: Member; element: HTMLElement }) {
    const { targetMember, element } = payload
    this.group$.pipe(
      take(1),
    )
      .subscribe(group => {
        this.tickle.tickle(element, group, targetMember)
      })
  }

  isFirstExitDay(prevDate: CalendarDateItem | undefined, date: CalendarDateItem): boolean {
    if (!this.currentUserInfo || !this.memberCompletionMap()) {
      return false
    }

    const { userId } = this.currentUserInfo()!
    const isExitPrev = !prevDate || this.memberCompletionMap()[prevDate?.formattedDate]?.[userId]?.isExited
    const isExit = this.memberCompletionMap()[date?.formattedDate]?.[userId]?.isExited

    return !isExitPrev && isExit
  }

  showCouponsExchangeModal(coupons: Coupon[]) {
    this.modal.create({
      component: CouponExchangeComponent,
      componentProps: {
        coupons,
      },
      backdropDismiss: false,
      cssClass: ['rounded-modal', 'center-modal'],
    })
  }

  onClickRepair(member: Member) {
    // TODO
    console.log('repairing', member)
  }
}
