@if (isCommercialGroup()) {
  <memo-commercial-banner
    [withViewTransition]="true"
    [style.--page-padding.px]="pagePadding()"
    [style]="{
      width: 'calc(100% - var(--page-padding) * 2 - var(--gap-base) * 4)',
      borderRadius: '10px',
      marginBottom: 'calc(var(--gap-base) * 2)',
    }"
    [src]="commercialBanner()"
    [link]="commercialBannerLink()"
    [groupId]="group()?.id || ''"
    [proposalId]="activity()?.proposalId || ''"
  ></memo-commercial-banner>
}
@if (isInitialized) {
  <div class="study-cards-container">
    <swiper
      #dateSwiper
      [style.padding-left.px]="pagePadding()"
      [style.padding-right.px]="pagePadding()"
      [slidesPerView]="1"
      [autoHeight]="true"
      [initialSlide]="initialDateIndex"
      [spaceBetween]="pagePadding()"
      noSwipingClass="swipable"
      [virtual]="virtualSwiperOptions"
      (slideChange)="onDateSlideChange($event[0])"
      (afterInit)="$event[0].updateAutoHeight(100)"
    >
      @for (item of datesWithRecord(); let i = $index; track item.date) {
        <ng-template swiperSlide>
          <app-study-stats-card
            [itemIndex]="i"
            [calendarDateItem]="item"
            [members]="(members$ | async) || undefined"
            [memberStatsMap]="memberCompletionMap()[item.formattedDate]"
            [activityConfig]="activityConfig"
            [activityUIConfig]="activityUIConfig?.()"
            [showSwitchGroupButton]="!!nextActivity"
            [showLanternTheme]="isLanternThemeVisible()"
            [studyBadgeText]="(studyBadgeText$ | async) || '非酋奖'"
            (clickHandle)="handleClick($event, { date: item.formattedDate })"
            (dbClickMember)="onDbClickMember($event)"
            (clickRepair)="onClickRepair($event)"
          >
            <ng-container *ngIf="item.relativeIndex === 0 && isRefundInfoVisible" extra-info-testing-day>
              <br />
              <span [style.color]="'var(--red-bgColor)'">队伍未满员，在磨合期结束后会退相应费用给你。</span>
            </ng-container>
            <ng-container
              *ngIf="isFirstExitDay(i === 0 ? undefined : datesWithRecord()[i - 1], item)"
              extra-info-exit-first
            >
              <br />
              <span class="refund-msg">{{ refundMessage$ | async }}</span>
            </ng-container>
          </app-study-stats-card>
        </ng-template>
      }
      @if (datesWithRecord().length > 0) {
        <ng-template swiperSlide>
          <app-study-stats-card
            [calendarDateItem]="summaryDateItem"
            [members]="(members$ | async) || undefined"
            [memberStatsMap]="{}"
            [memberReportsMap]="memberDailySummaryStatsMap()"
            [unluckyMembers]="(unluckyMembers$ | async) || undefined"
            [activityConfig]="activityConfig"
            [activityUIConfig]="activityUIConfig?.()"
            [showSwitchGroupButton]="!!nextActivity"
            [showLanternTheme]="isLanternThemeVisible()"
            [studyBadgeText]="(studyBadgeText$ | async) || '非酋奖'"
            (clickHandle)="handleClick($event)"
            (dbClickMember)="onDbClickMember($event)"
          >
            <ng-template #opHandlers>
              <div class="summary-handlers">
                @if (rewardAmount > 0) {
                  <span class="reward-amount">+{{ rewardAmount }}</span>
                }
                @if (isLegitToGetReward) {
                  <button
                    memo-button
                    skin="bright"
                    width="200px"
                    [disabled]="!isAbleToGetReward || isRequestingReward"
                    (memoClick)="onClickGetReward()"
                  >
                    @if (isRequestingReward) {
                      <ion-spinner name="crescent"></ion-spinner>
                      <span [style.margin-left.em]="0.5">领取中</span>
                    } @else {
                      领取奖励
                    }
                  </button>
                } @else {
                  <button
                    memo-button
                    width="200px"
                    [disabled]="!isGroupStudyFinished"
                    [routerLink]="['../report']"
                    [queryParamsHandling]="'merge'"
                  >
                    查看组队报告
                  </button>
                }
              </div>
            </ng-template>
          </app-study-stats-card>
        </ng-template>
      }
    </swiper>
  </div>
} @else {
  <app-study-stats-card [blank]="true"></app-study-stats-card>
}
<div class="calendar-card-container">
  <app-card>
    <app-calendar
      [readyToShow]="isInitialized"
      [activeDateIndex]="activeDateIndexForCalendar()"
      [calendars]="calendars()"
      [members]="(members$ | async) || undefined"
      [studyStatsMap]="memberCompletionMap()"
      [currentUserInfo]="currentUserInfo()"
      [currentUserStudySumStats]="currentUserSummaryStats()"
      [currentUserStudyReport]="currentUserSFinalReports()"
      [isCurrentUserInGroup]="isCurrentUserInGroup"
      [isCurrentUserTheCreator]="isCurrentUserTheCreator"
      (clickCalendarDate)="onClickCalendarDate($event)"
    ></app-calendar>
  </app-card>
</div>
