<app-card>
  <div class="form-item" (memoClick)="onClickGroupSize()">
    <span class="form-title">组队人数</span>
    <span>
      <span>{{ groupSize }} 人</span>
      <memo-icon name="arrow" size="1em"></memo-icon>
    </span>
  </div>
  <p class="desc">组队首日按相同头像概率奖励其对应的单词上限</p>

  @if (isInitialized) {
    <table class="probability-chart">
      <thead>
        @for (item of displayCols; track item) {
          <td class="header-cell">{{ fieldTexts[item] }}</td>
        }
      </thead>
      @for (item of tableData; track item.consecutiveAvatar) {
        <tr>
          @for (cell of displayCols; track cell) {
            <td>
              @if (cell === 'probability') {
                {{ item[cell] | probability }}
              }
              @else {
                {{ item[cell] }}
              }
            </td>
          }
        </tr>
      }
    </table>
  }
  @else {
    <ion-skeleton-text class="probability-chart" [style.--border-radius.px]="8" [style.height.px]="180" [animated]="true"></ion-skeleton-text>
  }
  <memo-link-preview-card [style.margin-top.em]="1" url="https://www.xiaohongshu.com/discovery/item/64b733ba000000002301d7b6" [extraParams]="{'__open_mode': '1'}"></memo-link-preview-card>
</app-card>
