import { Component } from '@angular/core'
import { PickerColumnOption } from '@ionic/angular'
import '@maimemo/link-preview/card'
import { finalize } from 'rxjs'
import { GroupStudyClientService } from '../../../group-study/service/services/group-study-client.service'
import { trackBy } from '../../../shared/helper/template'
import { PickerService } from '../../../ui/picker/picker.service'
import { PickerSelectedInfo } from '../../../ui/picker/picker.type'
import { RewardProbability } from '../../models/probability.model'
import { GroupStudyNetworkService } from '../../services/group-study-network.service'

@Component({
  selector: 'app-reward-probability',
  templateUrl: './reward-probability.component.html',
  styleUrls: ['./reward-probability.component.scss'],
  standalone: false,
})
export class RewardProbabilityComponent {
  private readonly optionList: PickerColumnOption[] = Array.from({ length: 11 }, (_, i) => {
    const v = i + 2
    return {
      text: v.toString(),
      value: v,
    }
  })

  groupSize = 12
  isInitialized = false

  private dataSource: Record<number, RewardProbability[]> = {}

  trackByProbability = trackBy<RewardProbability>('consecutiveAvatar')

  displayCols: (keyof RewardProbability)[] = ['reward', 'probability']

  fieldTexts: Partial<Record<keyof RewardProbability, string>> = {
    reward: '获得上限奖励',
    probability: '概率',
  }

  get tableData() {
    return this.dataSource?.[this.groupSize] || []
  }

  constructor(
    private picker: PickerService,
    private network: GroupStudyNetworkService,
    private client: GroupStudyClientService,
  ) {
    this.queryData()
  }

  async onClickGroupSize() {
    const picker = await this.picker.create('设定组队人数', {
      columns: [
        {
          name: 'groupSize',
          options: this.optionList,
          align: 'left',
          selectedIndex: this.optionList.findIndex(v => v.value === this.groupSize),
        },
      ],
    }, [
      {
        text: '确认',
        handler: (pickerResult: Record<'groupSize', PickerSelectedInfo>) => {
          this.groupSize = parseInt(pickerResult.groupSize.value ?? '1')
          return true
        },
      },
    ])

    picker.present()
  }

  queryData() {
    this.network.queryRewardProbabilities()
      .pipe(
        finalize(() => this.isInitialized = true),
      )
      .subscribe({
        next: res => this.dataSource = res,
        error: err => {
          const message = err?.error?.errors?.[0]?.message || '请求失败'
          this.client.clientToast(message)
        },
      })
  }
}
