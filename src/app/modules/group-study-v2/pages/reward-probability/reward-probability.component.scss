:host {
  --gap-base: 5px;

  display: block;
  width: 100%;
  padding: 0 var(--page-padding);
  transition: padding 150ms ease;
}

app-card {
  min-height: calc(100vh - (var(--gap-base) * 4));
}

.desc {
  font-size: 14px;
}

.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 11px 0;

  & > :last-child {

    display: flex;
    align-items: center;

    memo-icon {
      margin-left: 1em;
      margin-right: 0.5em;
      transform: rotateZ(-90deg);
      color: var(--default-font-color);
    }
  }
}

.form-title {
  font-size: 1rem;
  font-weight: 600;
}

.probability-chart {
  min-height: 396px;
  margin-top: 30px;
  width: 100%;
}

thead, tr {
  display: flex;
  align-items: center;
}

td {
  flex: 1;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 6px 0;
}

thead {
  color: var(--default-font-color);
  background-color: var(--bg-color-2);
  font-size: 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px 8px 0 0;
}

tr {
  font-weight: bold;
  font-size: 14px;
  overflow: hidden;
}

tr:last-of-type {
  & > td {
    &:first-of-type {
      border-radius: 0 0 0 8px;
    }

    &:last-of-type {
      border-radius: 0 0 8px 0;
    }
  }
}

tr > td {
  border: 1px solid var(--border-color);
  border-top: none;

  &:not(:first-of-type) {
    border-left: none;
  }
}
