/* eslint-disable max-nested-callbacks */
import { HttpErrorResponse } from '@angular/common/http'
import { ChangeDetectorRef, Component, Inject } from '@angular/core'
import { UntypedFormBuilder, ValidatorFn, Validators } from '@angular/forms'
import { Router } from '@angular/router'
import { CheckboxCustomEvent, PickerColumnOption } from '@ionic/angular'
import { Subject, catchError, debounceTime, delay, distinctUntilChanged, filter, finalize, from, map, of, startWith, switchMap, take, takeUntil, tap, throttleTime, throwError } from 'rxjs'
import { DestroyService } from '../../../core/services/destroy.service'
import { StorageService } from '../../../core/services/storage.service'
import { ROUTE_BASE_HREF } from '../../../entry/group-study.module'
import { GroupStudyClientService } from '../../../group-study/service/services/group-study-client.service'
import { GroupNameGenerator } from '../../../shared/helper/group'
import { capturePaymentEvent } from '../../../shared/helper/sentry'
import { toSeconds } from '../../../shared/helper/time'
import { MemoAlertService } from '../../../ui/alert/alert.service'
import { MemoLoadingService } from '../../../ui/loading/loading.service'
import { PickerService } from '../../../ui/picker/picker.service'
import { PickerSelectedInfo } from '../../../ui/picker/picker.type'
import { PayBy } from '../../models/activity.model'
import { GroupStudyStorageService } from '../../services/group-study-storage.service'
import { GroupStudyUIService } from '../../services/group-study-ui.service'
import { CreateActivityPayload } from '../../services/request.type'
import { MAX_REWARD } from '../../utils/study'
import { isEqual } from '../../utils/diff'
import { GroupSetupForm } from './type'

@Component({
  selector: 'app-group-setup',
  templateUrl: './group-setup.component.html',
  styleUrls: ['./group-setup.component.scss'],
  providers: [DestroyService],
  standalone: false,
})
export class GroupSetupComponent {
  private _groupNameGenerator = new GroupNameGenerator('GROUP_STUDY_CUSTOM')
  readonly throttleTime = 500
  readonly maxGroupNameLength = 20

  iconSpinning = false
  submiting = false
  fetchingPrice = false
  totalPrice = -1
  unitPrice = this.defaultGroupingUnitPrice
  maxReward = MAX_REWARD

  clickToChangeGroupName$ = new Subject<void>()
  extraGroupSize = this.gss.extraGroupSize

  private readonly formPresetConfig: Partial<
    Record<keyof GroupSetupForm, {
      defaultIndex: number
      options: PickerColumnOption[]
    }>
  > = {
    scheduledDays: {
      defaultIndex: 1,
      options: [7, 14, 21, 30].map(v => ({ text: String(v), value: v })),
    },
    dailyVocab: {
      defaultIndex: 2,
      options: Array.from({ length: 50 }, (v, i) => {
        const val = (i + 1) * 10
        return {
          text: val.toString(),
          value: val,
        }
      }),
    },
    dailyTime: {
      defaultIndex: 1,
      options: Array.from({ length: 12 }, (v, i) => {
        const val = (i + 1) * 5
        return {
          text: val.toString(),
          value: val,
        }
      }),
    },
  }

  readonly PAY_BY_OPTIONS = [
    {
      text: '我我我！',
      value: PayBy.CREATOR,
    },
    {
      text: '组员分摊',
      value: PayBy.EACH,
    },
  ]

  private readonly formControlsConfig: Record<keyof GroupSetupForm, [unknown, ValidatorFn[]]> = {
    name: [
      this._groupNameGenerator.getRandomName(),
      [Validators.required, Validators.minLength(1), Validators.maxLength(this.maxGroupNameLength)],
    ],
    groupSize: [
      null,
      [Validators.required, Validators.min(2), Validators.max(100)],
    ],
    scheduledDays: [
      null,
      [Validators.required, Validators.min(7), Validators.max(30)],
    ],
    dailyVocab: [
      this.getDefaultValueByFieldName('dailyVocab', i => this.isNewUserForGroupStudy ? 0 : i),
      [Validators.required, Validators.min(10), Validators.max(500)],
    ],
    dailyTime: [
      this.getDefaultValueByFieldName('dailyTime'),
      [Validators.required, Validators.min(5), Validators.max(60)],
    ],
    payBy: [
      PayBy.CREATOR,
      [Validators.required],
    ],
    excludeCreator: [
      false,
      [Validators.required],
    ],
  }

  readonly formGroup = this.formBuilder.group(this.formControlsConfig)

  get defaultGroupingUnitPrice(): number {
    return this.gss.defaultGroupingUnitPrice
  }

  private get maxGroupSize() {
    return this.gss.maxGroupSizeLimit
  }

  get isExcludeCreator(): boolean {
    return this.formGroup
      ? this.formGroup.controls.excludeCreator.value
      : false
  }

  get isNewUserForGroupStudy(): boolean {
    return this.gss.isNewUserForGroupStudy
  }

  constructor(
    private gsu: GroupStudyUIService,
    private gss: GroupStudyStorageService,
    @Inject(ROUTE_BASE_HREF) private routeBase: string,
    private router: Router,
    private formBuilder: UntypedFormBuilder,
    private picker: PickerService,
    private client: GroupStudyClientService,
    private loading: MemoLoadingService,
    private alert: MemoAlertService,
    private storage: StorageService,
    private cdr: ChangeDetectorRef,
    private destroy$: DestroyService,
  ) {
    this.clickToChangeGroupName$
      .pipe(
        throttleTime(this.throttleTime),
        tap(() => {
          this.iconSpinning = true
        }),
        delay(this.throttleTime / 2),
        tap(() => {
          this.formGroup.get('name')?.setValue(this._groupNameGenerator.getRandomName())
        }),
        takeUntil(this.destroy$),
      )
      .subscribe()

    this.formGroup.valueChanges.pipe(
      debounceTime(300),
      startWith(this.formGroup.value),
      // 改变队名不需要请求价格
      distinctUntilChanged<GroupSetupForm>((a, b) => {
        const { name: nameA, ...restA } = a
        const { name: nameB, ...restB } = b
        return isEqual(restA, restB)
      }),
      switchMap(() => {
        this.fetchingPrice = true
        const payload = this.getFormPayload()
        if (!payload.config.group_size) {
          return of({
            totalPrice: -1,
            unitPrice: 0,
            maxReward: MAX_REWARD,
          })
        }

        return this.gss.getGroupingPrice(payload)
          .pipe(
            catchError(err => {
              const msg = err?.error?.errors?.[0]?.message
              console.error(`Grouping Error: ${msg}`)
              this.client.clientToast('队伍条件有误')
              return of({
                totalPrice: -1,
                unitPrice: this.defaultGroupingUnitPrice,
                maxReward: MAX_REWARD,
              })
            }),
          )
      }),
      takeUntil(this.destroy$),
    )
      .subscribe({
        next: ({ totalPrice, unitPrice, maxReward }) => {
          this.totalPrice = totalPrice
          this.unitPrice = unitPrice
          this.maxReward = maxReward || MAX_REWARD
          this.fetchingPrice = false
          if (this.formGroup.get('groupSize')?.value || this.formGroup.get('scheduledDays')?.value) {
            this.cdr.detectChanges()
          }
        },
        error: () => {
          this.fetchingPrice = false
          this.cdr.detectChanges()
        },
      })

    this.gss.checkHistoryGroups().subscribe(() => {
      if (this.gss.isNewUserForGroupStudy) {
        const control = this.formGroup.get('dailyVocab')
        const defaultValue = this.getDefaultValueByFieldName('dailyVocab', () => 0)
        control?.setValue(defaultValue)
      }
    })
    this.client.clientEndLoading()
  }

  async onClickNumberOfPpl() {
    const options = Array.from({ length: this.maxGroupSize - 1 }, (v, i) => ({ text: String(i + 2), value: i + 2 }))

    const extra = this.extraGroupSize()
    if (extra && this.maxGroupSize !== extra) {
      options.push({
        text: String(extra),
        value: extra,
      })
    }
    const formField = this.formGroup.get('groupSize')?.value
    const picker = await this.picker.create('设定组队人数', {
      columns: [
        {
          name: 'groupSize',
          options: options,
          align: 'left',
          selectedIndex: formField ? options.findIndex(v => v.value === formField) : -1,
        },
      ],
    }, [
      {
        text: '确认',
        handler: (pickerResult: Record<keyof GroupSetupForm, PickerSelectedInfo>) => {
          this.updatePickerResultIfNeeded(pickerResult)
          return true
        },
      },
    ])

    picker.present()
  }

  async onClickScheduledDays() {
    const picker = await this.picker.create('设定组队天数', {
      columns: [
        {
          name: 'scheduledDays',
          options: this.formPresetConfig.scheduledDays?.options ?? [],
          align: 'left',
          selectedIndex: this.getOptionIndexByFieldName('scheduledDays'),
        },
      ],
    }, [
      {
        text: '确认',
        handler: (pickerResult: Record<keyof GroupSetupForm, PickerSelectedInfo>) => {
          this.updatePickerResultIfNeeded(pickerResult)
          return true
        },
      },
    ])

    picker.present()
  }

  private checkIsNewUserForGroupStudy() {
    if (this.gss.isNewUserForGroupStudy) {
      this.client.cfc.clientAlert({
        title: '首次创建组队\n无法修改组队任务选项',
        message: '',
        buttons: [{ id: 'confirm', text: '我知道了' }],
        button_callback: () => {
          // noop
        },
      })
      return true
    }

    return false
  }

  async onClickConditions() {
    if (this.checkIsNewUserForGroupStudy()) {
      return
    }
    const picker = await this.picker.create('设定组队条件', {
      columns: [
        {
          name: 'dailyVocab',
          prefix: '每日单词量',
          options: this.formPresetConfig.dailyVocab?.options ?? [],
          selectedIndex: this.getOptionIndexByFieldName('dailyVocab'),
        },
        {
          name: 'dailyTime',
          prefix: '每日时长<span style="display: inline-block; transform: scale(0.8); transform-origin: 50% 70%">(分钟)</span>',
          options: this.formPresetConfig.dailyTime?.options ?? [],
          selectedIndex: this.getOptionIndexByFieldName('dailyTime'),
        },
      ],
    }, [
      {
        text: '确认',
        handler: (pickerResult: Record<keyof GroupSetupForm, PickerSelectedInfo>) => {
          this.updatePickerResultIfNeeded(pickerResult)
          return true
        },
      },
    ])

    picker.present()
  }

  private updatePickerResultIfNeeded(pickerResult: Record<keyof GroupSetupForm, PickerSelectedInfo>) {
    Object.keys(pickerResult).forEach(k => {
      const key = k as keyof typeof pickerResult
      const formField = this.formGroup.get(key)
      const pickerValue = pickerResult[key].value
      if (formField?.value !== pickerValue) {
        formField?.setValue(pickerValue)
      }
    })
  }

  private getOptionIndexByFieldName(name: keyof GroupSetupForm) {
    const columnConfig = this.formPresetConfig[name]
    const formField = this.formGroup.get(name)
    return columnConfig && formField
      ? columnConfig.options.findIndex(opt => opt.value === formField.value)
      : -1
  }

  private getDefaultValueByFieldName(name: keyof GroupSetupForm, getDefaultOptionIndex?: (defaultIndex: number) => number): number {
    const config = this.formPresetConfig[name]!
    const defaultIndex = getDefaultOptionIndex?.(config.defaultIndex) ?? config.defaultIndex
    return config?.options[defaultIndex].value
  }

  onCheckboxChange(e: Event) {
    const { detail: { checked, value } } = e as CheckboxCustomEvent

    if (!checked) {
      return
    }

    this.formGroup.get('payBy')?.setValue(value)
  }

  private getFormPayload(): CreateActivityPayload {
    const totalStudyDays = this.formGroup.get('scheduledDays')?.value as number
    const payload: CreateActivityPayload = {
      title: this.formGroup.get('name')?.value,
      type: 'GROUP_STUDY_CUSTOM',
      config: {
        payment_type: this.formGroup.get('payBy')?.value,
        group_size: this.formGroup.get('groupSize')?.value,
        daily_study_voc: this.formGroup.get('dailyVocab')?.value,
        daily_study_duration: toSeconds(this.formGroup.get('dailyTime')?.value as number, 'min'),
        study_duration: totalStudyDays ? toSeconds(totalStudyDays - 1, 'day') : toSeconds(6, 'day'),
        testing_duration: toSeconds(1, 'day'),
        creator_auto_join: !this.formGroup.get('excludeCreator')?.value,
      },
    }

    return payload
  }

  async onClickSubmit() {
    if (!(await this.gss.checkGroupingPermission())) {
      return
    }

    const isCreatorExcluded = !!this.formGroup.get('excludeCreator')?.value
    if (!isCreatorExcluded) {
      this.submitGroupSetup()
      return
    }

    this.alert.show({
      header: '你确定不参与此次组队的学习吗？',
      message: '你依然有权查看队伍的学习任务完成情况',
      cssClass: ['memo-alert', 'warning-alert'],
      buttons: [
        {
          role: 'cancel',
          text: '取消',
        },
        {
          role: 'confirm',
          text: '确定',
          handler: () => this.submitGroupSetup(),
        },
      ],
    })
      .catch(error => console.error(error))
  }

  private submitGroupSetup() {
    this.submiting = true

    const payload = this.getFormPayload()

    this.gss.createActivity(payload)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          const errorCode = error.error.errors[0].code
          if (errorCode == 'common_sensitive_word') {
            return throwError(() => new Error('队名不可包含敏感词'))
          }
          if (errorCode == 'common_invalid_param') {
            return throwError(() => new Error('队名限制 20 字以内'))
          }

          return throwError(() => error)
        }),
        switchMap(({ activity, goods_sn, group_order }) => {
          console.debug('groupOrder', group_order)

          if (group_order || goods_sn) {
            return from(this.client.clientPayForJoiningGroup(this.totalPrice, goods_sn!, activity.id, group_order))
              .pipe(
                switchMap(res => {
                  console.debug('bridge res', res)
                  capturePaymentEvent({
                    type: 'setup',
                    activity_id: activity.id,
                    res: res.data,
                  })
                  if (['success', 'not_received', 'pending'].includes(res.data.status)) {
                    return from(this.loading.show())
                      .pipe(
                        switchMap(() => this.gss.getGroupDataByGroupMeta({ activity_id: activity.id }, true)
                          .pipe(
                            map(v => GroupStudyStorageService.getGroupFromGroupData(v)),
                            filter(Boolean),
                            filter(v => v.status === 'GROUPING'),
                            map(v => v.activityId),
                          )),
                      )
                  }

                  console.debug('activity: ', activity.id)
                  console.debug('status', res.data.status)

                  return throwError(() => new Error(res.data.status === 'cancel' ? '支付已取消' : '支付失败'))
                }),
              )
          }

          console.debug('activity', activity)
          if (!activity?.id) {
            return throwError(() => new Error('请求队伍信息有误'))
          }

          return of(activity.id)
        }),
        finalize(() => {
          this.submiting = false
          this.loading.dismiss()
        }),
        take(1),
      )
      .subscribe({
        next: activityId => {
          console.debug('activityId', activityId)
          this.loading.dismiss()
          if (activityId && typeof activityId === 'string') {
            this.client.clientToast('创建队伍成功')
            this.storage.updateIsIgnoreSquarePage(true)
            this.client.clientUploadStudyRecord()
            setTimeout(() => {
              this.router.navigate([this.routeBase], {
                queryParams: {
                  activity_id: activityId,
                },
                queryParamsHandling: 'merge',
                replaceUrl: true,
              })
            }, 2000)
          } else {
            this.client.clientToast('请求队伍信息有误，请联系管理员。')
          }
        },
        error: err => {
          if (err instanceof HttpErrorResponse && err.status.toString().startsWith('5')) {
            return
          }
          const apiError = err?.error?.errors?.[0]

          // TODO: 有时间优化一下错误处理
          if (apiError && apiError.code === 'custom_group_max_group_exceeded') {
            this.gsu.showOutOfLimitModal({
              title: '无法创建新队伍',
              max: 3,
              currentCount: 3,
              isCreate: true,
            })
            return
          }
          const msg = err?.error?.errors?.[0]?.message || err.message || err
          this.alert.show({
            header: '创建失败',
            message: msg || '请求失败',
            cssClass: 'memo-alert',
            buttons: [{
              text: '确定',
            }],
          })
        },
      })
  }
}
