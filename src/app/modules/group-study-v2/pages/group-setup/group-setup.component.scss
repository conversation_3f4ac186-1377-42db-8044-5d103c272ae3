:host {
  --gap-base: 5px;

  display: block;
  width: 100%;
  padding: 0 var(--page-padding);
  transition: padding 150ms ease;
}

form {
  display: flex;
  flex-direction: column;

  & > :not(:last-child) {
    margin-bottom: calc(var(--gap-base) * 2);
  }
}

form section {
  font-size: 1rem;
  margin: 0;
  padding: calc(var(--gap-base) * 2) 0;
  display: flex;
  align-items: center;
  justify-content: space-between;

  label {
    font-weight: 600;
  }

  memo-icon[name='arrow'] {
    margin-left: 10px;
    color: var(--default-font-color);
    transform: rotateZ(-90deg);
  }
}

.input-container {
  max-width: calc(100% - 6em);
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  & > :not(:last-child) {
    margin-right: 0.5em;
  }
}

input {
  user-select: auto;
  width: 100%;
  text-align: end;
  outline: none;
  border: none;
  font-size: 1rem;
  background-color: transparent;
}

input:disabled {
  touch-action: none;
  pointer-events: none;
  user-select: none;
}

.with-separators > span:not(:last-of-type) {
  display: inline-flex;
  align-items: center;

  &::after {
    content: '';

    display: inline-block;
    width: 1px;
    height: 1em;
    background-color: var(--border-color);
    margin-left: 0.5em;
  }
}

.submit-button {
  margin-top: 8px;
  margin-bottom: 10px;
}

.extra-options {
  text-align: center;
}

.extra-options ion-item {
  color: var(--default-font-color);
  font-size: 12px;
  transform: scale(0.9);
  transform-origin: 50% 50%;
  width: fit-content;
  margin: 0 auto;
}

:root.dark :host ion-checkbox {
  --background: var(--gray-level-140);
}

.extra-options ion-checkbox {
  --size: 12px;
  --border-radius: 2px;
}

.spinning {
  --throttle: 500ms;
  animation: spin var(--throttle) ease-in-out forwards;
}

.defalt-option-text {
  color: var(--red-bgColor);
}

.warning-item,
.warning-item ion-label {
  --color: var(--red-bgColor);
}

.warning-item {
  ion-checkbox {
    --red-bgColor: #dc663e;
    --background-checked: var(--red-bgColor);
    --border-color-checked: var(--red-bgColor);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
