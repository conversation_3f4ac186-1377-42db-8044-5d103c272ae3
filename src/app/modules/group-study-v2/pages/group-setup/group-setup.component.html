<app-card>
  <form [formGroup]="formGroup">
    <section>
      <label>队伍名</label>
      <div class="input-container">
        <input
          memoInput
          class="truncate"
          type="text"
          inputmode="text"
          autocomplete="maimemo-group-name"
          formControlName="name"
          [maxLength]="maxGroupNameLength"
          [selectToEndOnFocus]="true"
        />
        <memo-icon
          [style.--throttle.ms]="throttleTime"
          [class.spinning]="iconSpinning"
          (animationend)="iconSpinning = false"
          [style.color]="'var(--theme-green)'"
          name="refresh"
          (click)="clickToChangeGroupName$.next()"
        ></memo-icon>
      </div>
    </section>
    <section (click)="onClickNumberOfPpl()">
      <label>组队人数</label>
      <div class="input-container">
        <span *ngIf="formGroup.get('groupSize')?.value as groupSize; else defaultOptionText">{{ groupSize }}</span>
        <memo-icon name="arrow" size="0.8em"></memo-icon>
      </div>
    </section>
    <section (click)="onClickScheduledDays()">
      <label>组队天数</label>
      <div class="input-container">
        <span *ngIf="formGroup.get('scheduledDays')?.value as scheduledDays; else defaultOptionText"
          >{{ scheduledDays }} 天</span
        >
        <memo-icon name="arrow" size="0.8em"></memo-icon>
      </div>
    </section>
    <section (click)="onClickConditions()">
      <label>任务要求</label>
      <div class="input-container with-separators">
        <span>{{ formGroup.get('dailyVocab')?.value || 0 }} 单词</span>
        <span>{{ formGroup.get('dailyTime')?.value || 0 }} 分钟</span>
        <memo-icon name="arrow" size="0.8em"></memo-icon>
      </div>
    </section>
    <section>
      <label>付款方式</label>
      <div class="input-container checkbox-container" single>
        @for (item of PAY_BY_OPTIONS; track item.value) {
          <ion-item>
            <ion-checkbox
              [value]="item.value"
              [checked]="formGroup.get('payBy')?.value === item.value"
              (ionChange)="onCheckboxChange($event)"
            ></ion-checkbox>
            <ion-label>{{ item.text }}</ion-label>
          </ion-item>
        }
      </div>
    </section>
    <button
      memo-button
      class="submit-button button-with-desc"
      width="95%"
      [disabled]="formGroup.invalid || submiting || totalPrice === -1 || fetchingPrice"
      [throttleTime]="500"
      (memoClick)="onClickSubmit()"
    >
      <span class="btn-text">
        <ng-container *ngIf="submiting || fetchingPrice; else defaultText">
          <ion-spinner name="crescent"></ion-spinner>
          <span [style.margin-left.em]="0.5">{{ submiting ? '创建中' : '计算价格中' }}</span>
        </ng-container>
        <ng-template #defaultText>
          创建队伍<ng-container *ngIf="formGroup.valid && totalPrice > 0">（¥{{ totalPrice }}）</ng-container>
        </ng-template>
      </span>
      <span class="btn-desc">
        <span>
          每人天 {{ unitPrice || defaultGroupingUnitPrice }} 元（试运行价格），每天有机会获得 1 ~
          {{ maxReward }} 单词上限
        </span>
      </span>
    </button>
    <footer class="extra-options">
      <ion-item [class.warning-item]="isExcludeCreator">
        <ion-checkbox
          [checked]="isExcludeCreator"
          (ionChange)="formGroup.get('excludeCreator')?.setValue(!formGroup.get('excludeCreator')?.value)"
        ></ion-checkbox>
        <ion-label>我只创建队伍，我不参与完成学习任务</ion-label>
      </ion-item>
    </footer>
  </form>
</app-card>

<ng-template #defaultOptionText>
  <span class="defalt-option-text">待选</span>
</ng-template>
