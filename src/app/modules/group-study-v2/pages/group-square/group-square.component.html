@if (isInitialized()) {
  <memo-pull-to-refresh
    [(isRefreshing)]="isRefreshing"
    [pullDistance]="80"
    [headHeight]="60"
    [successDuration]="500"
    [animationDuration]="300"
    [disabled]="isFetchingGroups()"
    (refresh)="handleRefresh()"
  >
    <div class="group-list-container" [style.--container-padding.px]="containerPadding">
      <div class="external-links-container" [class.compact]="canLoadPublicGroups">
        @for (item of externalLinks; track item.label; let i = $index) {
          <button class="external-link" [class.card-base]="!canLoadPublicGroups" (memoClick)="item.handler()">
            <img [src]="item.image" [alt]="item.label" />
            <span>{{ item.label }}</span>
          </button>
          @if (canLoadPublicGroups && i !== externalLinks.length - 1) {
            <div class="divider">|</div>
          }
        }
      </div>
      @if (!canLoadPublicGroups) {
        <button class="card-base group-setup-button" (memoClick)="navigateToSetupPage()">
          <svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0.000184741 5.49963L14.0002 5.5006L14 8.5006L-1.52588e-05 8.49963L0.000184741 5.49963Z"
              fill="currentColor"
            />
            <path d="M8.50058 0.00022L8.49957 14.0002L5.49957 14L5.50058 0L8.50058 0.00022Z" fill="currentColor" />
          </svg>
          <span>创建队伍</span>
        </button>
      }
      @if (canLoadPublicGroups) {
        @if (groups().length || !isLoadingInitialGroups()) {
          @for (group of groups(); track group.id + $index) {
            <app-group-list-item
              [hidden]=""
              [style.--group-item-padding.px]="listItemPadding"
              [style.--members-gap.px]="membersGap"
              [attr.data-group-id]="group.id"
              [group]="group"
              [cancelable]="group.creator === userInfo?.userId"
              [maxMembersPerRow]="maxMembersPerRow()"
              [withBannerViewTransition]="group.id === transitingGroupId()"
              (recall)="onRecallGroup(group)"
              (click)="onClickGroup($event, group)"
            ></app-group-list-item>
          }

          @if (!isAllDataLoaded() && groups().length > 0 && !isRefreshing()) {
            <div #infiniteScrollSentinel class="infinite-scroll-sentinel">
              <memo-spinner style="margin: 20px auto"></memo-spinner>
            </div>
          } @else {
            @if (isCommercialEntryVisible()) {
              <p class="text-tips" (memoClick)="onClickCommercialEntry()">{{ '- 组队推广 -' }}</p>
            } @else {
              <p class="text-tips">{{ groups().length > 0 ? '已经到底了' : '没有更多了' }}</p>
            }
          }
        } @else {
          @for (group of 5 | list; track $index) {
            <app-group-list-item
              [style.--group-item-padding.px]="listItemPadding"
              [style.--members-gap.px]="membersGap"
            ></app-group-list-item>
          }
        }
      }
    </div>
  </memo-pull-to-refresh>
}
