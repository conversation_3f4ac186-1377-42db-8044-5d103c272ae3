import { DOCUMENT } from '@angular/common'
import { AfterViewInit, Component, DestroyRef, ElementRef, OnDestroy, OnInit, computed, effect, inject, signal, viewChild, viewChildren } from '@angular/core'
import { ActivatedRoute, Router } from '@angular/router'
import { EMPTY, Observable, catchError, debounceTime, delay, finalize, fromEvent, map, of, startWith, switchMap, tap, throwError, timer } from 'rxjs'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { ClientFrontendCommunicationService } from '../../../core/services/client-frontend-communication.service'
import { UserService } from '../../../core/services/user.service'
import { SocialMediaName, SocialMediaShareConfigs } from '../../../shared/helper/social'
import { fromResData } from '../../../shared/helper/utils'
import { OnReuseRoute } from '../../../shared/types'
import { MemoAlertService } from '../../../ui/alert/alert.service'
import { MemoLoadingService } from '../../../ui/loading/loading.service'
import { MemoModalService } from '../../../ui/modal/modal.service'
import { CouponExchangeComponent } from '../../components/coupon-exchange/coupon-exchange.component'
import { Coupon } from '../../models/coupon.model'
import { GroupMeta, GroupWithConfig, GroupingStatusRes } from '../../models/group.model'
import { GroupStudyNetworkService } from '../../services/group-study-network.service'
import { GroupStudyStorageService } from '../../services/group-study-storage.service'
import { GroupStudyUIService } from '../../services/group-study-ui.service'
import { EventService } from '../../../core/services/event.service'
import { GroupListItemComponent } from '../../components/group-list-item/group-list-item.component'
import { Features } from '../../../shared/helper/env'
import { useInfiniteScroll } from '../../../../hooks/use-infinite-scroll'

interface ExternalLink {
  label: string
  image: string
  handler: () => void
}

const MIN_TRACING_VIEW_TIME_MS = 3000 // 最小浏览时间，单位毫秒
const VIEWABILITY_THRESHOLD = 0.5 // 50% 像素可见

@Component({
  selector: 'app-group-square',
  templateUrl: './group-square.component.html',
  styleUrls: ['./group-square.component.scss'],
  standalone: false,
})
export class GroupSquareComponent implements OnInit, AfterViewInit, OnDestroy, OnReuseRoute {
  private modal = inject(MemoModalService)
  private loading = inject(MemoLoadingService)
  private user = inject(UserService)
  private gss = inject(GroupStudyStorageService)
  private gsu = inject(GroupStudyUIService)
  private network = inject(GroupStudyNetworkService)
  private router = inject(Router)
  private route = inject(ActivatedRoute)
  private elementRef = inject<ElementRef<HTMLElement>>(ElementRef)
  private alert = inject(MemoAlertService)
  private _doc = inject(DOCUMENT)
  private cfc = inject(ClientFrontendCommunicationService)
  private eventService = inject(EventService)
  private destroyRef = inject(DestroyRef)

  private _win = this._doc.defaultView ?? window
  private rewardId = this.route.snapshot.queryParamMap.get('reward_id')

  isAlphaApp = Features.isAlphaVersion

  groupItemList = viewChildren<GroupListItemComponent, ElementRef<HTMLElement>>(GroupListItemComponent, {
    read: ElementRef,
  })

  infiniteScrollSentinel = viewChild<ElementRef<HTMLElement>>('infiniteScrollSentinel')

  groups = signal<GroupWithConfig[]>([])
  maxMembersPerRow = signal(0)
  isAllDataLoaded = signal(false)
  isInitialized = signal(false)
  isLoadingInitialGroups = computed(() => this.isFetchingGroups() && this.groups().length === 0)
  isFetchingGroups = signal(false)
  private isBrowsing = false

  isRefreshing = signal(false)

  transitingGroupId = signal('')

  readonly listItemPadding = 18
  readonly containerPadding = 15
  readonly membersGap = 5
  readonly memberSize = 34
  private readonly socialMediaConfigs: Record<SocialMediaName, { label: string; link: string }> = {
    xiaohongshu: {
      label: '寻队小红书',
      link: 'https://www.xiaohongshu.com/page/topics/628ef564c0892d00016f697c',
    },
    weibo: {
      label: '寻队微博',
      link: 'https://weibo.com/p/100808c5dde57b800f5b1fd0c3057fa51a12e7',
    },
  }

  externalLinks: ExternalLink[] = (['xiaohongshu', 'weibo'] as SocialMediaName[]).map(channel => {
    return {
      label: this.socialMediaConfigs[channel].label,
      image: SocialMediaShareConfigs[channel].image,
      handler: async () => {
        this.goToExternalLinks(this.socialMediaConfigs[channel].link)

        // TODO: 后续不用的话就可以删除了
        // const { handler } = SocialMediaShareConfigs[channel]
        // from(this.cfc.getStudyingBookInfo())
        //   .pipe(
        //     catchError(() => of({ catalog_name: '' })),
        //     switchMap(({ catalog_name }) => this.network.getShareContentPreset({
        //       platform: channel,
        //       book_catalog: catalog_name,
        //     })),
        //     switchMap(config => handler(this.cfc)(config)),
        //   )
        //   .subscribe({
        //     error: err => {
        //       console.error(err)
        //       this.goToExternalLinks(this.socialMediaConfigs[channel].link)
        //     },
        //   })
      },
    }
  })

  private groupingStatus?: GroupingStatusRes
  isCommercialEntryVisible = computed(() => this.commercialTestGroup() === 'B' || this.commercialTestGroup() === 'C')
  canSubmitPromotion = computed(() => this.commercialTestGroup() === 'C')

  get commercialTestGroup() {
    return this.gss.commercialTestGroup
  }

  get userInfo() {
    return this.user.userInfo
  }

  get canLoadPublicGroups(): boolean {
    return this.gss.squareTestGroupType === 'PUBLISH_GROUP' || this.gss.squareTestGroupType === 'SHOW_GROUPING_SQUARE'
  }

  private adTrackerTimers = new Map<string, any>()
  private adObserver = new IntersectionObserver(entries => {
    entries.forEach(item => {
      const target = item.target as HTMLElement
      const groupId = target.dataset.groupId
      if (!groupId) {
        this.adObserver.unobserve(item.target)
        return
      }
      // const groupName = this.groups.find(group => group.groupId === groupId)?.name

      // 元素达到可见性阈值
      if (!item.isIntersecting || item.intersectionRatio < VIEWABILITY_THRESHOLD) {
        if (!this.adTrackerTimers.has(groupId) && !target.dataset.impressionRecorded) {
          const timerId = setTimeout(() => {
            this.recordGroupImpression(groupId)
            // 记录后停止观察此元素
            this.adObserver.unobserve(target)
            target.dataset.impressionRecorded = 'true'
            this.adTrackerTimers.delete(groupId)
            // console.log(`列表项 [${groupName}] 已满足曝光条件，停止观察。`)
          }, MIN_TRACING_VIEW_TIME_MS)

          this.adTrackerTimers.set(groupId, timerId)
        }
      } else {
      // 元素离开可见区域或未达到阈值
        if (this.adTrackerTimers.has(groupId)) {
          clearTimeout(this.adTrackerTimers.get(groupId))
          this.adTrackerTimers.delete(groupId)
          // console.log(`列表项 [${groupName}] 离开可见区域或未达阈值，曝光计时器已清除。`)
        }
      }
    })
  }, {
    threshold: VIEWABILITY_THRESHOLD,
  })

  private recordGroupImpression(groupId: string) {
    this.eventService.track('group_study_square_events', [
      {
        event: 'view_group_study_group',
        type: 'Exposure',
        event_time: new Date().toISOString(),
        content: {
          group_id: groupId,
          source: 'square',
        },
      },
    ])
      .subscribe()
  }

  constructor() {
    this.gss.clearPollingSubscriptions()
    if (this.rewardId) {
      this.router.navigate([], {
        relativeTo: this.route,
        replaceUrl: true,
        queryParamsHandling: 'merge',
        queryParams: {
          reward_id: null,
        },
      })
    }

    effect(() => {
      const groups = this.groupItemList()
      groups.forEach(item => {
        if (!item || !item.nativeElement.dataset.groupId) {
          return
        }
        if (item.nativeElement.dataset.impressionRecorded === 'true') {
          return
        }
        this.adObserver.observe(item.nativeElement)
      })
    })

    // 初始化无限滚动
    useInfiniteScroll(
      this.infiniteScrollSentinel,
      () => {
        this.fetchGroups().subscribe()
      },
      {
        threshold: 1,
        canLoadMore: computed(() => !this.isAllDataLoaded() && !this.isFetchingGroups()),
      },
    )
  }

  async ngOnInit() {
    this.gss.getSquareTestGroup()
      .pipe(
        tap(() => {
          this.isInitialized.set(true)
        }),
        map(squareTestGroupType => squareTestGroupType === 'PUBLISH_GROUP' || squareTestGroupType === 'SHOW_GROUPING_SQUARE'),
        switchMap(canViewPublicGroups => {
          if (canViewPublicGroups) {
            this.setupNavButtons()
            this.trackOnBrowsing()
          }

          return canViewPublicGroups
            ? this.fetchGroups(true)
            : EMPTY
        }),
      )
      .subscribe()

    if (this.rewardId) {
      this.network.getRewardById(this.rewardId)
        .subscribe({
          next: res => {
            const { coupons = [] } = res.data
            if (coupons.length) {
              this.showCouponsExchangeModal(coupons)
            }
          },
          error: err => {
            const msg = err?.error?.errors?.[0]?.message || '未知错误'
            this.alert.show({
              header: '领取奖励',
              message: msg,
              buttons: [{
                text: '我知道了',
              }],
              cssClass: ['memo-alert'],
            })
          },
        })
    }
  }

  onAttach(): void {
    setTimeout(() => {
      this.calcMaxMembersPerRow()
    })
    setTimeout(() => {
      this.transitingGroupId.set('')
    }, 500)
    this.gss.clearPollingSubscriptions()
    this.setupNavButtons()
    this.trackOnBrowsing()
  }

  onDetach(): void {
    this.setupNavButtons(true)
  }

  private trackOnBrowsing() {
    timer(MIN_TRACING_VIEW_TIME_MS)
      .pipe(
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        this.isBrowsing = true
        this.eventService.track('group_study_square_events', [
          {
            event: 'load_group_study_page',
            type: 'PageView',
            event_time: new Date().toISOString(),
            content: {
              page_name: 'square',
            },
          },
        ])
          .subscribe()
      })
  }

  private setupNavButtons(clear = false) {
    clear
      ? this.cfc.setNavButtons({
          buttons: [],
          click_listener: () => {
            //
          },
        })
      : this.cfc.setNavButtons({
          buttons: this.canSubmitPromotion()
            ? [
                {
                  id: 'group-setup',
                  text: '创建队伍',
                  text_color: '#36B59D',
                  folded: true,
                },
                {
                  id: 'commercial-page',
                  text: '组队推广',
                  folded: true,
                  text_color: '#36B59D',
                },
              ]
            : [
                {
                  id: 'group-setup',
                  text: '创建队伍',
                  text_color: '#36B59D',
                  folded: false,
                },
              ],
          click_listener: (id: string) => {
            if (id === 'group-setup') {
              this.navigateToSetupPage()
            } else if (id === 'commercial-page') {
              this.navigateToCommercialPage()
            }
          },
        })
  }

  navigateToSetupPage() {
    this.checkCanJoinOrCreateGroup({ isCreate: true }).subscribe(canSetup => {
      if (!canSetup) {
        return
      }

      this.router.navigate(['../setup'], {
        relativeTo: this.route,
        queryParamsHandling: 'merge',
        replaceUrl: !this.canLoadPublicGroups,
      })
    })
  }

  navigateToCommercialPage() {
    location.href = `https://www.maimemo.com/pages/commercial/group-study/list?token=${this.user.legacyToken}&__open_mode=3`
  }

  private goToExternalLinks(link: string) {
    this._win.open(link + '?__open_mode=1')
  }

  private getGroupingStatus(): Observable<GroupingStatusRes | undefined> {
    return this.network.queryGroupingStatus()
      .pipe(
        tap(data => {
          this.groupingStatus = data
        }),
        catchError(() => of(this.groupingStatus)),
      )
  }

  ngAfterViewInit(): void {
    fromEvent(this._win, 'resize')
      .pipe(
        startWith(0),
        debounceTime(200),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        this.calcMaxMembersPerRow()
      })
  }

  fetchGroups(clean = false): Observable<{ groups: GroupWithConfig[] }> {
    this.isAllDataLoaded.set(false)

    this.isFetchingGroups.set(true)

    const limit = 15

    return this.network.queryPublicGroups({
      offset: clean ? 0 : this.groups().length,
      limit,
    })
      .pipe(
        map(res => fromResData(res)),
        delay(1000),
        tap(({ groups = [] }) => {
          if (clean) {
            this.groups.set([])
          }

          groups.forEach(group => {
            if (group.promotion?.banners?.length) {
              this.gss.promotionGroupIdToBanner.set(group.groupId, group.promotion.banners[0])
            } else {
              this.gss.promotionGroupIdToBanner.has(group.groupId) && this.gss.promotionGroupIdToBanner.delete(group.groupId)
            }
          })

          this.groups.update(arr => {
            arr.push(...groups)
            return arr
          })
          if (groups.length < limit) {
            this.isAllDataLoaded.set(true)
          }
        }),
        finalize(() => {
          this.isFetchingGroups.set(false)
        }),
      )
  }

  ngOnDestroy(): void {
    this.setupNavButtons(true)
  }

  calcMaxMembersPerRow() {
    if (!this.elementRef?.nativeElement) {
      return
    }
    const { nativeElement } = this.elementRef
    const containerPadding = this.containerPadding * 2
    const itemPadding = this.listItemPadding * 2
    const containerWidth = nativeElement.clientWidth
    const membersWidth = containerWidth - containerPadding - itemPadding
    this.maxMembersPerRow.set(Math.floor(membersWidth / (this.memberSize + this.membersGap)))
  }

  async onRecallGroup(group: GroupWithConfig) {
    const ref = await this.alert.show({
      header: '撤回发布队伍',
      message: '撤回后，你所创建的队伍将不再展示在组队广场里',
      cssClass: ['memo-alert'],
      buttons: [
        {
          role: 'cancel',
          text: '不撤回',
        },
        {
          role: 'confirm',
          text: '撤回发布',
          cssClass: 'red-text',
        },
      ],
    })

    const { role } = await ref.onWillDismiss()
    if (role !== 'confirm') {
      return
    }

    const loadingEl = await this.loading.show()

    this.network.recallGroupPublish(group.groupId)
      .pipe(
        catchError(err => {
          this.alert.show({
            message: '撤回失败',
            buttons: [{
              text: '我知道了',
            }],
            cssClass: ['memo-alert'],
          })
          this.loading.dismiss(loadingEl)
          return throwError(() => err)
        }),
        switchMap(() => {
          this.cfc.clientToast('撤回成功')
          return this.fetchGroups(true)
            .pipe(
              tap(() => {
                this.loading.dismiss(loadingEl)
              }),
            )
        }),
      )
      .subscribe()
  }

  onClickGroup(event: MouseEvent, group: GroupWithConfig) {
    const target = event.target as HTMLElement
    if (target && target.nodeType === Node.ELEMENT_NODE && target.tagName === 'BUTTON') {
      return
    }

    this.eventService.track('group_study_square_events', [
      {
        event: 'click_group_study_square_group',
        type: 'Click',
        event_time: new Date().toISOString(),
        content: {
          group_id: group.groupId,
          browsing: this.isBrowsing,
        },
      },
    ])
      .subscribe()

    const isCurrentUserTheCreator = group.creator === this.userInfo?.userId
    const isCurrentUserAlreadyJoined = group.members.some(v => v.userId === this.userInfo?.userId)
    const precheck$: Observable<boolean> = isCurrentUserTheCreator || isCurrentUserAlreadyJoined
      ? of(true)
      : this.checkCanJoinOrCreateGroup()

    precheck$.subscribe(canJoin => {
      if (!canJoin) {
        return
      }

      const urlParams: GroupMeta = {}

      this.transitingGroupId.set(group.groupId)

      if (group.members.some(v => v.userId === this.userInfo?.userId)) {
        urlParams.activity_id = group.activityId
      } else {
        urlParams.group_id = group.groupId
      }

      this.router.navigate(['../'], {
        relativeTo: this.route,
        queryParamsHandling: 'merge',
        queryParams: urlParams,
      })
    })
  }

  private checkCanJoinOrCreateGroup(opts: { isCreate?: boolean } = {}): Observable<boolean> {
    const { isCreate = false } = opts
    return this.getGroupingStatus()
      .pipe(
        map(groupingStatus => {
          if (!groupingStatus) {
            return false
          }
          const { custom_group_count, max_custom_group_count } = groupingStatus
          if (custom_group_count >= max_custom_group_count) {
            this.gsu.showOutOfLimitModal({
              max: max_custom_group_count,
              currentCount: custom_group_count,
              title: '无法加入新队伍',
              isCreate: isCreate,
            })
            return false
          }

          return true
        }),
      )
  }

  handleRefresh() {
    this.fetchGroups(true)
      .pipe(
        finalize(() => {
          this.isRefreshing.set(false)
        }),
      )
      .subscribe()
  }

  showCouponsExchangeModal(coupons: Coupon[]) {
    this.modal.create({
      component: CouponExchangeComponent,
      componentProps: {
        coupons,
      },
      backdropDismiss: false,
      cssClass: ['rounded-modal', 'center-modal'],
    })
  }

  onClickCommercialEntry() {
    if (this.canSubmitPromotion()) {
      this.navigateToCommercialPage()
    } else if (this.commercialTestGroup() === 'B') {
      location.href = 'https://maimemo.feishu.cn/share/base/form/shrcnOXBMIxvCQE2XGIEitE7b9e?__open_mode=2'
    }
  }
}
