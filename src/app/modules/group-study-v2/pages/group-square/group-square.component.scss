:host {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: calc(100vh - (var(--gap-base) * 4));
  padding: 0 var(--page-padding);
  overflow: hidden;
}

.group-list-container {
  --container-padding: 15px;
  --padding-top: var(--gap-base);
  --padding-bottom: var(--gap-base);
  --padding-start: var(--container-padding);
  --padding-end: var(--container-padding);

  padding: var(--padding-top) var(--padding-start) var(--padding-bottom);

  & > :not(:last-child) {
    margin-bottom: calc(var(--gap-base) * 2);
  }
}

.infinite-scroll-sentinel {
  width: 100%;
  pointer-events: none;
}

.text-tips {
  width: 100%;
  margin: 20px 0;
  color: var(--default-font-color);
  font-size: 13px;
  text-align: center;
}

.card-base {
  height: var(--card-height);
  color: inherit;
  background-color: var(--bg-color-2);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  font-weight: bolder;
}

.external-links-container {
  --card-height: 85px;
  --img-size: 28px;
  --content-bg: var(--bg-color-2);

  display: flex;
  align-items: center;
  background-color: var(--bg-color-2);

  .divider {
    color: var(--border-color);
  }
}

.external-links-container.compact {
  --card-height: 44px;
  --img-size: 22px;
  --content-bg: transparent;

  height: var(--card-height);
  border-radius: 10px;

  .external-link {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
  }
}

.external-link {
  min-width: 0;
  flex: 1;
  background-color: var(--content-bg);
  color: inherit;
  font-weight: bolder;

  img {
    height: var(--img-size);
    width: var(--img-size);
    border-radius: 100vh;
  }

  span {
    margin-left: 6px;
    font-size: 16px;
  }

  &:not(:first-child) {
    margin-left: 10px;
  }
}

.group-setup-button {
  --card-height: 85px;
  width: 100%;
  font-size: 18px;

  memo-icon {
    transform: rotate(45deg);
  }

  span {
    margin-left: 6px;
  }
}
