import { ChangeDetectorRef, Component, Inject, OnD<PERSON>roy, OnInit, computed } from '@angular/core'
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router'
import { EMPTY, distinctUntilChanged, filter, map, shareReplay, switchMap, take, takeUntil } from 'rxjs'
import { AppService } from '../../../../app.service'
import { DestroyService } from '../../../core/services/destroy.service'
import { UserService } from '../../../core/services/user.service'
import { ROUTE_BASE_HREF } from '../../../entry/group-study.module'
import { GroupStudyClientService } from '../../../group-study/service/services/group-study-client.service'
import { OnReuseRoute } from '../../../shared/types'
import { MemoToastService } from '../../../ui/toast/toast.service'
import { GroupStudyStorageService } from '../../services/group-study-storage.service'

@Component({
  selector: 'app-index',
  templateUrl: './index.component.html',
  styleUrls: ['./index.component.scss'],
  host: {
    id: 'group-study-v2',
    '[style.--page-padding.px]': 'pagePadding()',
  },
  providers: [DestroyService],
  standalone: false,
})
export class IndexComponent implements OnInit, OnDestroy {
  isRulesVisible = true

  pagePadding = computed(() => {
    return this.app.pageGap()
  })

  toastContent = computed(() => this.toast.toastContent())

  // 进入当前用户所在的队伍时才需要记录
  private currentGroupDataWithCurrentUser$ = this.route.queryParams.pipe(
    map(params => params.activity_id as string),
    filter(v => !!v && v !== ''),
    distinctUntilChanged(),
    switchMap(activityId => this.gss.getGroupData$(activityId)),
    filter(Boolean),
    map(groupData => GroupStudyStorageService.getGroupFromGroupData(groupData)),
    filter(Boolean),
    shareReplay(1),
  )

  constructor(
    private app: AppService,
    private router: Router,
    private route: ActivatedRoute,
    private user: UserService,
    private gss: GroupStudyStorageService,
    private toast: MemoToastService,
    private client: GroupStudyClientService,
    private cdr: ChangeDetectorRef,
    private destroy$: DestroyService,
    @Inject(ROUTE_BASE_HREF) private routeBase: string,
  ) {
    this.client.clientEndLoading()

    this.router.events
      .pipe(
        filter(evt => evt instanceof NavigationEnd),
        map(evt => {
          const { urlAfterRedirects } = evt as NavigationEnd
          const childPath = urlAfterRedirects.replace(this.routeBase, '').split('?')[0].split('/').slice(-1)[0]
          const realRoute = this.route.routeConfig?.children?.find(v => v.path?.split('/').slice(-1)[0] === childPath)
          return realRoute
            ? realRoute?.data?.showRules
            : true
        }),
        takeUntil(this.destroy$),
      )
      .subscribe(showRules => {
        this.isRulesVisible = !!showRules
        this.cdr.detectChanges()
      })
  }

  async ngOnInit() {
    if (await this.gss.checkShouldHidePaymentInfo()) {
      this.gss.redirectToGroupListPage()
      return
    }

    this.recordReadActivity()
  }

  ngOnDestroy(): void {
    this.recordReadActivity()
  }

  private recordReadActivity() {
    this.currentGroupDataWithCurrentUser$.pipe(
      take(1),
      switchMap(group => {
        return group.members.some(v => v.userId === this.user.userInfo?.userId || v.isCurrentUser)
          ? this.gss.recordReadActivity(group.activityId)
          : EMPTY
      }),
      takeUntil(this.destroy$),
    )
      .subscribe()
  }

  onAttach(e: unknown) {
    const instance = e as OnReuseRoute
    if (instance.onAttach && typeof instance.onAttach === 'function') {
      instance.onAttach()
    }
  }

  onDetach(e: unknown) {
    const instance = e as OnReuseRoute
    if (instance.onDetach && typeof instance.onDetach === 'function') {
      instance.onDetach()
    }
  }
}
