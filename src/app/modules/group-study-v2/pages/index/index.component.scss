:host {
  --gap-base: 5px;
  --page-padding: 0;

  width: 100%;
  padding: calc(var(--gap-base) * 2) 0;
  color: var(--title-color);
  background-color: var(--bg-color-1);
  overflow-y: auto;
  display: block;
  height: 100vh;
}

.scroller {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card-container {
  width: calc(100% - calc(var(--gap-base) * 6));
  border-radius: 10px;
  background-color: var(--bg-color-2);
  padding: calc(var(--gap-base) * 4);
}

.rules-container {
  transition: padding 150ms ease;
  padding: 0 var(--page-padding);
}

.rules-container,
.scroller {
  & > :not(:first-child) {
    margin-top: calc(var(--gap-base) * 2);
  }
}
