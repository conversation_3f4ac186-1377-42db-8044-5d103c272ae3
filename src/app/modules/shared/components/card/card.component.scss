:host {
  box-sizing: border-box;
  position: relative;
  display: block;
  width: calc(100% - calc(var(--gap-base) * 6));
  border-radius: 10px;
  background-color: var(--bg-color-2);
  padding: calc(var(--gap-base) * 4);
  margin: 0 auto;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: var(--title-color);
  margin-bottom: calc(var(--gap-base) * 3);

  & > :not(:last-child) {
    margin-right: 10px;
  }

  h2 {
    margin: 0;
    padding: 0;
    font-size: 1rem;
    font-weight: 600;
  }
}

.floating-left {
  left: 0;
  align-items: flex-start;
}

.floating-right {
  right: 0;
  align-items: flex-end;
}

.floating-container {
  position: absolute;
  top: 0;
  display: flex;
  flex-direction: column;
}

.floating-btn {
  --floating-button-gap: 10px;
  --color: var(--theme-green);
  --bg-color: var(--title-font-color);

  margin-top: var(--floating-button-gap);
  padding: 0 var(--floating-button-gap);
  background: none;
  cursor: pointer;

  .floating-btn-display {
    display: inline-flex;
    padding: 4px 7px;
    align-items: center;
    color: var(--color);
    background-color: var(--bg-color);
    border-radius: 4px;
    font-size: 1rem;
    font-weight: bold;
  }

  .red-dot {
    --size: 10px;

    position: absolute;
    top: calc(var(--floating-button-gap) - (var(--size) / 4));
    right: calc(var(--floating-button-gap) - (var(--size) / 4));
    display: inline-block;
    width: var(--size);
    height: var(--size);
    border: 2.5px var(--bg-color-2) solid;
    border-radius: 50%;
    background-color: #ff3b30;
    background-clip: content-box;
    z-index: 2;
  }

  &:active .floating-btn-display {
    filter: brightness(0.9);
  }
}

.button-dismiss {
  --color: var(--color-forget);
}

:root.dark :host {
  .floating-btn .floating-btn-display {
    font-weight: normal;
  }

  .floating-btn {
    --color: #fff;
    --bg-color: var(--color-familiar);
  }

  .floating-btn.button-dismiss {
    --color: #fff;
    --bg-color: var(--color-forget);
  }
}
