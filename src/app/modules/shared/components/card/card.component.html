@if (cardTitle() !== '') {
  <div class="title-container">
    <img
      src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAH4AAAAtCAYAAABoOd/RAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAJ8SURBVHic7ZxtcoQgDIYTzxWm7Mnqnmzp4L3SHxVL7equHwSQPL+cZZU4L4EAQQSlCLz3PQAAIn6MP9mo2IULZr4DABhjHBwAj9ysHMN7349C2x23uyONQIXPwCj451nPY+a7Mabfco8KL8jZgs/Z0gBUeCGGYXjAvi59K46Ibq/+pMInxntvEfEhXS8z39bG/k7QluYYu3Zx0QEAEPERZgpPywVtaYpcnj5nyfPV4xNRgugAy3a85fGzxQUbFTmAKZp0Rwy8EoKB3Lv8C/hWhd86/dgzn7waqadse5lr81T4o8a33ACGYeDcNixBRJPe/8b4M1osIn6uRZRXpfR3ju3DeUHupcRaKSWKf0Xw+snjU4xNrXp+yQQ9JuFTBSQlBjopqOU9w7ZvB5B+bLq613vvLZQ1fVvDAozCp26ttXhDK3jv+25srSKVSdSTgxobdlej0coxEPFDbK0+yiVTCqADuaBEqp4c2NwGbMTq7lyjqPCN0kGUs50YqXpy4HIbsBHXMfNXbisUecS6+pD8f0VqdJ5OavdMM3TKgZnvXbhIXVHK5+emxq3naT8+ZeZInPlxVQrMs1vCEdFtGuNTeeXVvT1Qyzgf9JiEN8b0Z4vUUgZObe/5J6o/U/yWRA+U3rvFafCaZXsy1WbZAvx4PhHh1hbMzHciwlZFByjX6+d2vXuSxkY9gI2KXAhqWhZ7ToER/raTNMp+Suryn02ndXcuEcz88uMEEizZocInwhjjco/3a4dZtatPTKlfxFDhhSjtGzja1QtBRDeJzbB3RAdQj89CCYdTVfhMhIMsYwOwOx7hmPlr7/qJCl8As0YQsNG1A/jdATxjsewbWNxhrNwObGwAAAAASUVORK5CYII="
      width="14px"
    />
    <h2>{{ cardTitle() }}</h2>
    <img
      src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAH4AAAAtCAYAAABoOd/RAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAJsSURBVHic7ZzRdoMgDIYTnouc0SerfbKyg++V3cjGnK5WTUTJd9VTW5L6kxA0FWEHUkodAAAifgxvheJwzC+Y+QEAQEQRjEPBtV9MKXWD0GHF16NNgmN5W/hB8PteDjDzg4i6vcYzlrFY+L0FH2MTQJdFwvd9/4R1Kf1dovf+pmCnef4VPqUUEPGp5UyGmW+29svi5g4MqV1ddAAARHzmnYIhw2TEHxXpYyzy5ZiM+BpEB6jHjyvyJ+IVC7mlWMFXMLpYFopDEeB7dxRfjfNLeOkt21psq/e+Nq/O2S/h+77nDb6J4r1ffZXxzGwNxrkJ8L3G115F1+6fBHtkYES8T507HAxUUcW/oqWol740PruPr5FWol6i1hpHvstv7mlEiuK276WR0qMc16WUAtS1ffuPcLQD0khntTz+qVI9wPXTvXT2zeO7s6T5Fhiyr4ad7nQRf+V1XjMITye8sR1E/HBwvoIpHO2AIEHLjkV8o5jwjeKg6Hs/CfFoBwSJWnYs4iuCmT+1bDlNY0YdMPPjdBGf/4FzRbSaTYgoutY7W2pDemLn8RGgyj67OZrov5PshMo9DQ5At6jYwpXTfInU7yzHdQB6a4uxDCLq9hZ/tgOn9mha2jZ8FfYUf6rh0rpsK0e8yzZ/aK0BSWr1SwMi6rz3+O45YOaH9x7nlnH7J83JGDqicwYIxaGYi/QlNdtk+qwp5bea4qWZvHLHzFVEWC1+XJFJ4YkoHr2utlbFa2NPxGgUewZOoyy6O+e9v2ncPDDR9bDn3DXKqq1SbvwfJkBYMURk5k8T/Dg275FHkyATitcR4OcOoIldB1/uhWGsUAbnWQAAAABJRU5ErkJggg=="
      width="14px"
    />
  </div>
}
<ng-content></ng-content>

<div class="floating-container floating-left">
  @if (showSwitchGroupButton()) {
    <button class="floating-btn floating-left" (memoClick)="clickFloatingButton.emit('left')">
      <span class="floating-btn-display">
        <memo-icon name="switch" [style.margin-right.em]="0.2"></memo-icon>
        队伍
      </span>
    </button>
  }
</div>

<div class="floating-container floating-right">
  @if (showInteractiveButton()) {
    <button class="floating-btn" (memoClick)="clickFloatingButton.emit('right')">
      @if (hasUnreadMessage()) {
        <span class="red-dot"></span>
      }
      <span class="floating-btn-display">互动板</span>
    </button>
  }

  @if (showDismissButton()) {
    <button class="floating-btn floating-right button-dismiss" (memoClick)="dismiss.emit()">
      <span class="floating-btn-display">解散</span>
    </button>
  }

  @if (showRemoveButton()) {
    <button class="floating-btn floating-right button-remove" (memoClick)="remove.emit()">
      <span class="floating-btn-display">踢出成员</span>
    </button>
  }
</div>
