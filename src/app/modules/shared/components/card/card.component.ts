import { ChangeDetectionStrategy, Component, input, output } from '@angular/core'
import { UiModule } from '../../../ui/ui.module'

@Component({
  selector: 'app-card',
  templateUrl: './card.component.html',
  styleUrls: ['./card.component.scss'],
  imports: [
    UiModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CardComponent {
  cardTitle = input('')

  showInteractiveButton = input(false)

  hasUnreadMessage = input(false)

  showSwitchGroupButton = input(false)

  showDismissButton = input(false)

  showRemoveButton = input(false)

  clickFloatingButton = output<'left' | 'right'>()

  dismiss = output()

  remove = output()
}
