import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core'
import { ClientFrontendCommunicationService } from '../../../core/services/client-frontend-communication.service'
import { ExColorType } from '../../../group-study/models/model'
import { getColorRGBFromColorType } from '../../../shared/helper/color'

const ALERT_CONTENT = {
  非酋奖: {
    self: {
      title: '非酋奖',
      message: '你在本次组队运气垫底，恭喜荣获 “非酋奖”！',
    },
    others: {
      title: '非酋奖',
      message: '该组员本次组队运气垫底，荣获 “非酋奖”。',
    },
  },
  非酋奖提名: {
    self: {
      title: '非酋奖提名',
      message: '你在本次组队到目前为止满贯但运气垫底，提名为 “非酋奖” 候选人（ “非酋奖” 将获得一份随机礼物）',
    },
    others: {
      title: '非酋奖提名',
      message: '该组员本次组队到目前为止满贯但运气垫底，提名为 “非酋奖” 候选人（ “非酋奖” 将获得一份随机礼物）',
    },
  },
}

@Component({
  selector: 'memo-study-badge',
  templateUrl: './study-badge.component.html',
  styleUrls: ['./study-badge.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
  host: {
    '(click)': 'onClick($event)',
  },
})
export class StudyBadgeComponent {
  skin = input<ExColorType>('yellow')

  content = input('')

  paddingX = input('6px')

  paddingY = input('4px')

  isCurrentUser = input(false)

  private client = inject(ClientFrontendCommunicationService)

  skinRGBText = computed(() => getColorRGBFromColorType(this.skin() || 'yellow'))

  onClick(event: MouseEvent): void {
    event.stopPropagation()
    const config = ALERT_CONTENT[this.content() as keyof typeof ALERT_CONTENT] || ALERT_CONTENT['非酋奖']
    const exactConfig = this.isCurrentUser() ? config.self : config.others
    this.client.clientAlert({
      title: exactConfig.title,
      message: exactConfig.message,
      buttons: [{
        id: '0',
        text: '我知道了',
      }],
      button_callback: () => {
        //
      },
    } as any)
  }
}
