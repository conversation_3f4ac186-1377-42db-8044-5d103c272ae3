:host {
  display: block;
  position: relative;
  width: 100%;
  min-height: 110px;
  max-height: 260px;
  margin: 0 auto;
  overflow: hidden;
  contain: paint;
  aspect-ratio: 41 / 13;
}

:host.with-view-transition {
  view-transition-name: commercial-banner;
}

.icon-share {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(69, 69, 69, 0.2);
  border-radius: 4px;
  padding: 3px;
  width: 16px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

img {
  object-fit: cover;
  object-position: top;
}

::view-transition-old(commercial-banner),
::view-transition-new(commercial-banner) {
  /* Prevent the default animation,
  so both views remain opacity:1 throughout the transition */
  animation: none;
  /* Use normal blending,
  so the new view sits on top and obscures the old view */
  mix-blend-mode: normal;
  /* Make the height the same as the group,
  meaning the view size might not match its aspect-ratio. */
  height: 100%;
  /* Clip any overflow of the view */
  overflow: clip;
}

/* The old view is the thumbnail */
::view-transition-old(commercial-banner) {
  /* Maintain the aspect ratio of the view,
  by shrinking it to fit within the bounds of the element */
  object-fit: contain;
}

/* The new view is the full image */
::view-transition-new(commercial-banner) {
  /* Maintain the aspect ratio of the view,
  by growing it to cover the bounds of the element */
  object-fit: cover;
}
