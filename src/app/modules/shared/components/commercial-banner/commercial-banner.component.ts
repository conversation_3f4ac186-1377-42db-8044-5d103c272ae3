import { NgOptimizedImage } from '@angular/common'
import { ChangeDetectionStrategy, Component, computed, DestroyRef, ElementRef, inject, input, signal } from '@angular/core'
import { filter, finalize, Subject, switchMap, throttleTime } from 'rxjs'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { SvgIconComponent } from '@ngneat/svg-icon'
import { EventService } from '../../../core/services/event.service'

@Component({
  selector: 'memo-commercial-banner',
  templateUrl: './commercial-banner.component.html',
  styleUrls: ['./commercial-banner.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgOptimizedImage,
    SvgIconComponent,
  ],
  host: {
    '[class.with-view-transition]': 'withViewTransition()',
    '[style.height.px]': 'height()',
    '(click)': 'onClick()',
  },
})
export class CommercialBannerComponent {
  private elementRef = inject<ElementRef<HTMLElement>>(ElementRef)
  private destroyRef = inject(DestroyRef)
  private trackEvents = inject(EventService)

  link = input<string>('')
  src = input<string>('')
  withViewTransition = input(false)
  groupId = input('')
  proposalId = input('')

  private width = signal<number>(0)
  height = computed(() => this.width() * 0.32)

  private bannerClick$ = new Subject<void>()

  private resizeObserver = new ResizeObserver(([entry]) => {
    const containerWidth = entry.contentRect.width
    if (this.width() !== containerWidth) {
      this.width.set(containerWidth)
    }
  })

  constructor() {
    this.resizeObserver.observe(this.elementRef.nativeElement)
    this.destroyRef.onDestroy(() => {
      this.resizeObserver.disconnect()
    })

    this.bannerClick$.pipe(
      filter(() => !!this.link()),
      throttleTime(500),
      switchMap(() =>
        this.trackEvents.track('group_study_square_events', [
          {
            event: 'click_group_study_promotion_banner',
            type: 'Click',
            event_time: new Date().toISOString(),
            content: {
              group_id: this.groupId(),
              proposal_id: this.proposalId(),
            },
          },
        ])
          .pipe(
            finalize(() => {
              const urlObj = new URL(this.link())
              if (!urlObj.searchParams.has('__open_mode')) {
                urlObj.searchParams.set('__open_mode', '1')
              }
              if (!urlObj.searchParams.has('ref')) {
                urlObj.searchParams.set('ref', 'maimemo')
              }

              location.href = urlObj.toString()
            }),
          ),
      ),
      takeUntilDestroyed(),
    )
      .subscribe()
  }

  onClick() {
    this.bannerClick$.next()
  }
}
