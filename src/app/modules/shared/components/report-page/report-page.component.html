@if(!currentUserNotInGroup()) {
  <div class="stats-quote">
    @if(currentUserStats(); as stats) {
      <h3>{{ stats.title.title }}</h3>
      <span class="quote-text">{{ stats.title.quote }}</span>
    }
    @else {
      <h3>
        <ion-skeleton-text style="width: 4em;" [animated]="true"></ion-skeleton-text>
      </h3>
      <span class="quote-text" style="width: 14em;">
        {{'&nbsp;'}}
      </span>
    }
  </div>

<app-card class="user-card">
  @if(currentUserStats(); as stats) {
    <div class="user-info-header">
      <img class="user-avatar" [src]="userInfo()?.avatar || appConfig.defaultAvatarPath" referrerpolicy="no-referrer" [alt]="userInfo()?.userId">
      <div class="user-info">
        <p class="truncate">{{userInfo()?.name || ''}}</p>
        <div class="badges-container">
          @if(userInfo()?.userId && unluckyMembers().includes(userInfo()!.userId)) {
            <memo-study-badge
              [style.zoom]="0.8"
              content="非酋奖"
              [isCurrentUser]="true"
              [paddingY]="'6px'"
            ></memo-study-badge>
          }
          @if(groupDateRangeText(); as rangeText) {
            <memo-badge
              [style.zoom]="0.8"
              [style.--badge-color]="colorRGBMap['green']"
              [style.--badge-bg]="colorRGBMap['green-1']"
              [textContent]="rangeText"
            ></memo-badge>
          }
        </div>
      </div>
      <div
        class="user-title"
        [style.--theme]="stats.title.theme"
      >
        <svg-icon [key]="stats.title.icon"></svg-icon>
        <span>{{stats.title.tag}}</span>
      </div>
    </div>
    <div class="stats-card" [style.--theme]="colorRGBMap['green-1']">
      <div class="stats-grid">
        @for (item of mainStatsItems(); track item.name) {
          <div class="stats-item">
            <span class="stats-info">
              @for(subItem of item.data; track $index) {
                <span class="stats-data">{{ subItem.value }}</span>
                <span>{{ subItem.unit }}</span>
              }
            </span>
            <span class="stats-desc">{{ item.text }}</span>
          </div>
        }
      </div>
    </div>

    @for(chunk of subStatsItemChunks(); track $index) {
      <div class="stats-card" [style.--theme]="colorRGBMap[$index === 0 ? 'yellow': 'red']">
        <div class="stats-grid">
          @for(item of chunk; track item.name) {
            <div class="stats-item">
              <span class="stats-info">
                @for(subItem of item.data; track $index) {
                  <span class="stats-data">{{ subItem.value }}</span>
                  <span>{{ subItem.unit }}</span>
                }
              </span>
              <span class="stats-desc">{{ item.text }}</span>
            </div>
          }
        </div>
      </div>
    }
  }
  @else {
    <section class="user-card" style="--border-radius: 2px">
      <div class="user-info-header">
        <ion-thumbnail class="user-avatar">
          <ion-skeleton-text [animated]="true"></ion-skeleton-text>
        </ion-thumbnail>

        <div class="user-info">
          <ion-skeleton-text [animated]="true"></ion-skeleton-text>
          <ion-skeleton-text style="width: 5em;" [animated]="true"></ion-skeleton-text>
        </div>
        <div class="user-title">
          <ion-skeleton-text style="width: 5em;" [animated]="true"></ion-skeleton-text>
        </div>
      </div>
      <div class="skeleton-card">
        <ion-skeleton-text [animated]="true"></ion-skeleton-text>
      </div>
      <div class="skeleton-card">
        <ion-skeleton-text [animated]="true"></ion-skeleton-text>
      </div>
      <div class="skeleton-card">
        <ion-skeleton-text [animated]="true"></ion-skeleton-text>
      </div>
    </section>
  }
</app-card>

}
