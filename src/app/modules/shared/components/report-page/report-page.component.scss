:host {
  --gap-base: 5px;
  --border-radius: 6px;

  position: relative;
  display: block;
  width: 100%;

  &::before {
    content: "";
    position: absolute;
    top: calc(var(--gap-base) * -2);
    right: 0;
    width: 100%;
    background: var(--theme-green);
    height: var(--header-bg-height);
  }
}

.stats-quote {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  color: var(--white-level-10);
  padding: calc(var(--gap-base) * 2) calc(var(--gap-base) * 3);
}

app-card {
  border-radius: var(--border-radius);
}

.user-card {
  padding-top: 0 !important;

  & > :not(:first-child) {
    margin-top: calc(var(--gap-base) * 4);
  }
}

h3:first-of-type {
  font-size: 35px;
  font-weight: bold;
  margin: var(--gap-base) 0;
}

.user-info-header {
  --avatar-size: 3.5rem;
  --offset: -1rem;

  display: flex;
  align-items: flex-start;
  margin-right: calc(var(--gap-base) * -4);
  margin-bottom: var(--offset);

  & > .user-avatar {
    width: var(--avatar-size);
    height: var(--avatar-size);
    border-radius: 4px;
    transform: translateY(var(--offset));
    border: 2px solid var(--border-color);
  }
}

.user-info {
  min-width: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 4px 8px;
}

.user-title {
  padding: 4px 8px;
  display: flex;
  align-items: center;
  font-size: 12px;
  border-radius: 0 var(--border-radius) 0 var(--border-radius);
  color: rgb(var(--theme));
  background-color: rgba(var(--theme), 0.2);

  & > :not(:last-child) {
    margin-right: 6px;
  }

  span {
    margin-bottom: 1px;
  }
}

p {
  margin: 0;
}

.quote-text {
  padding: 4px 6px 4px 10px;
  background: #30A18C;
  border-radius: 4px;
  transform: scale(0.9);
  transform-origin: right center;
}

.quote-text,
.stats-info,
.stats-desc {
  font-size: 12px;
}

.stats-desc {
  transform: scale(0.9);
  transform-origin: center center;
}

.stats-card {
  padding: calc(var(--gap-base) * 3) calc(var(--gap-base) * 1);
  border-radius: 10px;
  background-color: rgba(var(--theme), 0.15);
}

.stats-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: calc(var(--gap-base) * 3);
}

.stats-grid {
  display: flex;
  align-items: center;
  // display: grid;
  // grid-template-columns: 1fr 1fr;
  // row-gap: 12px;
}

.stats-item {
  flex: 1;
  min-width: 0;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  &:not(:last-child)::after {
    content: "";
    position: absolute;
    bottom: 50%;
    right: 0;
    width: 1px;
    height: 60%;
    transform: translateY(50%);
    background: #b0b0b0;
  }
}

.stats-info {

  display: flex;
  align-items: baseline;

  & > :not(:last-child) {
    margin-right: 5px;
  }
}

.stats-data {
  font-size: 22px;
  font-weight: bold;
  color: rgb(var(--theme));
}


.skeleton-card {
  --border-radius: 10px;
  height: 80px;
}

.badges-container {
  display: flex;
  align-items: center;

  & > :not(:last-child) {
    margin-right: 0.5em;
  }
}

memo-badge {
  --padding-x: 6px;
  --padding-y: 6px;
  --badge-size: 11px;
  --badge-radius: 4px;
  --badge-bg-alpha: 0.2;

}
