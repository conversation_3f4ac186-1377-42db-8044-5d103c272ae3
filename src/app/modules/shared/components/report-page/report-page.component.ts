import { ChangeDetectionStrategy, Component, input } from '@angular/core'
import { AppConfig } from '../../../../configs/app.config'
import { DestroyService } from '../../../core/services/destroy.service'
import { UserInfo } from '../../../group-study-v2/models/member.model'
import { MemberStudyReport, StatsItem } from '../../../group-study-v2/pages/study-report/type'
import { getColorRGBFromColorType } from '../../../shared/helper/color'

@Component({
  selector: 'app-report-page',
  templateUrl: './report-page.component.html',
  styleUrls: ['./report-page.component.scss'],
  providers: [
    DestroyService,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class ReportPageComponent {
  currentUserNotInGroup = input(false)
  currentUserStats = input<MemberStudyReport>()
  unluckyMembers = input<number[]>([])
  mainStatsItems = input<StatsItem[]>([])
  subStatsItemChunks = input<StatsItem[][]>([])
  groupDateRangeText = input('')
  userInfo = input<UserInfo>()

  readonly appConfig = AppConfig

  colorRGBMap = {
    green: getColorRGBFromColorType('green'),
    'green-1': getColorRGBFromColorType('green-1'),
    yellow: getColorRGBFromColorType('yellow'),
    red: getColorRGBFromColorType('red'),
  }
}
