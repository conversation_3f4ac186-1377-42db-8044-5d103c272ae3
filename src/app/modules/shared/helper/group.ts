import { sample } from 'lodash-es'
import { ActivityType } from '../../group-study-v2/models/activity.model'
import { GROUP_NAMES_LATEST } from '../constants'

export class GroupNameGenerator {

  private lastName = ''
  private groupNames: ReadonlyArray<string> = []

  // 后续有需求的话可以根据 activity 来定制默认队伍名
  constructor(activityType: ActivityType, activityId?: string) {
    this.groupNames = GROUP_NAMES_LATEST
  }

  getRandomName(): string {
    const source = this.groupNames

    if (source.length === 0) {
      return ''
    }

    let randomName = sample(source)

    while (randomName === undefined || randomName === this.lastName) {
      randomName = sample(source)
    }

    this.lastName = randomName
    return randomName
  }
}

function genFirstDayAlertKey(groupId: string) {
  return `group-${groupId}-first-day-alert-showed`
}

export function checkIsFirstDayIntoGroup(groupId: string): boolean {
  const key = genFirstDayAlertKey(groupId)
  return localStorage ? !localStorage.getItem(key) : false
}

export function recordFirstDayIntoGroup(groupId: string) {
  const key = genFirstDayAlertKey(groupId)
  localStorage && localStorage.setItem(key, 'true')
}
