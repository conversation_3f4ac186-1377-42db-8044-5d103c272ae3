import { Big } from 'big.js'
import dayjs, { Dayjs, isDayjs } from 'dayjs'
import { Duration } from 'dayjs/plugin/duration'
import { Observable, interval, map, startWith } from 'rxjs'
import { ISODate } from '../types'

/**
 * dayjs 设置了 default timezone 后，默认 dayjs() 还是使用 local timezone 的。
 *
 * @param date
 * @returns
 * @see https://day.js.org/docs/en/plugin/timezone#differences-to-moment
 */
export function dayjsWithTZ(...args: Parameters<typeof dayjs>): Dayjs {
  return dayjs(...args).tz()
}

export function getTimerToNextStudyDay(anchorDate: Dayjs): Observable<Duration> {
  const targetDate = anchorDate.clone().add(1, 'd').startOf('d')
  return interval(1000)
    .pipe(
      startWith(0),
      map(() => {
        const currentTime = toStudyDate(Date.now())
        const diff = targetDate.diff(currentTime, 'millisecond', true)
        return dayjs.duration(diff)
      }),
    )
}

/**
 *
 *
 * @param date 需要判断的日期
 * @param startHour 开始学习的时间
 * @returns
 */
export function getStudyStartDay(date: ISODate | number | Dayjs, forward = true): Dayjs {
  const relativeDate = toStudyDate(date)
  const targetDate = forward
    ? relativeDate.add(1, 'd')
    : relativeDate

  return targetDate.startOf('d')
}

/**
 *
 * 将现实的时间转换为学习的时间，业务需求是凌晨四点开始新一天的学习
 *
 * @param date 需要转换的日期
 * @returns
 */
export function toStudyDate(date: ISODate | number | Dayjs): Dayjs {
  const studyBeginHour = 4
  const d = isDayjs(date) ? date.clone() : dayjs(date)

  return d.tz().subtract(studyBeginHour, 'h')
}

export function fromSeconds(seconds: number, to: 'day' | 'hour' | 'min'): number {
  return Math.floor(Big(seconds).div(getMultiplicandByUnit(to)).toNumber())
}

export function toSeconds(data: number, from: 'day' | 'hour' | 'min'): number {
  return Big(data).mul(getMultiplicandByUnit(from)).toNumber()
}

export function getMultiplicandByUnit(unit: 'day' | 'hour' | 'min'): number {
  return unit === 'day'
    ? 60 * 60 * 24
    : unit === 'hour'
      ? 60 * 60
      : 60
}
