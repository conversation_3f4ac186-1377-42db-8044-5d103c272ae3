export interface ShareCallbackResult {
  success: boolean
  code: number
  desc: string
  sub_code?: number
  channel_info?: string
}

const REGEXP_UNKNOWN_STRING = /"channel_info":"({(.|\s)*?})"/gm

export function parseShareCallback(raw: string): ShareCallbackResult {
  let matched = ''
  const replaced = raw.replace(REGEXP_UNKNOWN_STRING, (...matches) => {
    matched = matches[1]
    return matches[0].replace(matches[1], '')
  })
  const data = JSON.parse(replaced) as ShareCallbackResult
  if (matched.length) {
    data.channel_info = matched
  }
  return data
}
