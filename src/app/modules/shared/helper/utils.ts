import confetti from 'canvas-confetti'
import { camelCase } from 'lodash-es'
import { CamelCasedPropertiesDeep } from 'type-fest'

/**
 * 将后端返回的 snakeCase 数据转为 camelCase 数据
 *
 * @example foo_bar => fooBar
 *
 * @param data
 * @returns
 */
export function fromResData<T extends object, U extends CamelCasedPropertiesDeep<T> = CamelCasedPropertiesDeep<T>>(data: T): U {
  if (typeof data !== 'object' || data === null) {
    return data
  }

  if (Array.isArray(data)) {
    // @ts-ignore
    return data.map(v => fromResData(v))
  }

  return Object.keys(data).reduce((acc, key) => {
    const camelCaseKey = camelCase(key)

    // @ts-ignore
    const val = data[key]

    if (typeof val === 'object' && val !== null) {
      // @ts-ignore
      acc[camelCaseKey] = Array.isArray(val)
        ? val.map(v => fromResData(v))

        // @ts-ignore
        : fromResData(val)
    } else {
      // @ts-ignore
      acc[camelCaseKey] = val
    }

    return acc
  }, {} as U)
}

export function groupBy<T, K extends string | number | symbol>(
  arr: T[],
  keyPredicate: (item: T) => K,
): Record<K, T[]> {
  return arr.reduce((r, v, i, a, k = keyPredicate(v)) => ((r[k] || (r[k] = [])).push(v), r), {} as Record<K, T[]>)
}

export function groupByKey<
  T extends Record<string, unknown>,
  K extends keyof T,
  V extends(T[K] extends string | number | symbol ? T[K] : never),
>(arr: T[], key: K): Record<V, T[]> {
  return arr.reduce((r, v, i, a, k = v[key] as V) => ((r[k] || (r[k] = [])).push(v), r), {} as Record<V, T[]>)
}

export function reArrangeListByItemIndex<T>(list: T[], oldIndex: number, newIndex: number): T[] {
  const clonedList = list.slice()

  if (oldIndex === newIndex) {
    return clonedList
  }

  if (oldIndex < newIndex) {
    const delta = newIndex - oldIndex
    const toMove = clonedList.splice(-1 * delta)
    clonedList.unshift(...toMove)
  } else {
    const delta = oldIndex - newIndex
    const toMove = clonedList.splice(0, delta)
    clonedList.push(...toMove)
  }

  return clonedList
}

export function celebrate() {
  return confetti({
    spread: 70,
    origin: { y: 0.7 },
    colors: ['#EB9E27', '#82C2AB', '#DC663E'],
    particleCount: 99,
  })!
}

export function createEmptyArray(length: number): unknown[] {
  return Array.from({ length }, () => ({}))
}

export function wrapLinkToExternal(link: string | undefined) {
  if (!link) {
    return ''
  }

  const url = new URL(link)
  url.searchParams.set('__open_mode', '1')
  return url.toString()
}
