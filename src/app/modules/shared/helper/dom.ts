export function generateShareContnetElement(content: string): HTMLElement {
  const container = document.createElement('div')
  const { style } = container
  const fragment = document.createDocumentFragment()
  const paragraph = document.createElement('p')
  paragraph.textContent = content
  fragment.appendChild(paragraph)
  container.appendChild(fragment)
  style.setProperty('position', 'fixed')
  style.setProperty('left', '0')
  style.setProperty('top', '0')
  style.setProperty('z-index', '-999')
  style.setProperty('width', '300px')
  style.setProperty('height', '400px')
  style.setProperty('display', 'flex')
  style.setProperty('align-items', 'center')
  style.setProperty('justify-content', 'center')
  style.setProperty('justify-content', 'center')
  style.setProperty('font-family', 'Hiragino Sans GB", system-ui, -apple-system, sans-serif')
  style.setProperty('color', '#5d5d5d')

  paragraph.style.setProperty('margin', '0 auto')
  paragraph.style.setProperty('text-align', 'center')
  paragraph.style.setProperty('font-size', '20px')
  return container
}

export function getScrollTop(el: Element | Window): number {
  const top = 'scrollTop' in el ? el.scrollTop : el.pageYOffset

  // iOS scroll bounce cause minus scrollTop
  return Math.max(top, 0)
}
