import { ColorType, ExColorType } from '../../group-study/models/model'

export interface ColorRGBA {
  r: string
  g: string
  b: string
  a: number
}

const colorDict: Partial<Record<ColorType, string>> = {
  yellow: '#EB9E27',
  red: '#DC663E',
  green: '#36B59D',
}

/**
 * 方便结合透明度使用
 */
const colorRGBDict: Record<ExColorType, string> = {

  // eb9e27
  yellow: '235, 158, 39',

  // dc663e
  red: '220, 102, 62',

  // 36b59d
  green: '54, 181, 157',

  // 82C2AB
  'green-1': '130, 194, 171',

  // e5e5e5
  'grey-1': '229, 229, 229',

  // aaa
  'grey-2': '170, 170, 170',

}

export function getColorFromColorType(color: ColorType): string | undefined {
  return colorDict[color]
}

export function getColorRGBFromColorType(color: ExColorType): string {
  return colorRGBDict[color]
}

export function getRGBAFromColorType(color: ExColorType): ColorRGBA {

  const parsed = color.split('/')
  const colorBase = parsed[0] as ExColorType
  const alphaNum = parsed.length > 1 ? parsed[parsed.length - 1] : '100'
  let alphaVal = 1
  const colorRGB = colorRGBDict[colorBase].split(',')
  if (alphaNum && alphaNum.length > 0) {
    alphaVal = Number(alphaNum)
  }

  return {
    r: colorRGB[0].trim(),
    g: colorRGB[1].trim(),
    b: colorRGB[2].trim(),
    a: alphaVal,
  }
}
