import { ClientFrontendCommunicationService, ShareResult } from '../../core/services/client-frontend-communication.service'

export type SocialMediaName = 'xiaohongshu' | 'weibo'
export type ShareContentConfig = {
  title: string
  content: string
  tags: string[]
}

export type SocialMediaShareConfig<T extends SocialMediaName = SocialMediaName> = {
  name: T
  image: string
  handler: (client: ClientFrontendCommunicationService) => (config: ShareContentConfig) => Promise<ShareResult>
}

export const SocialMediaShareConfigs: Record<SocialMediaName, SocialMediaShareConfig<SocialMediaName>> = {
  xiaohongshu: {
    name: 'xiaoh<PERSON><PERSON>',
    image: 'assets/images/social-media/xiaohongshu.png',
    handler: client => async config => {
      const { title, content, tags } = config
      const desc = content + '\n\t\n' + tags.join(' ')

      return new Promise<ShareResult>((resolve, reject) => {
        client.share({
          type: 'text',
          channel: 'xhs',
          share_content: {
            title,
            link: desc,
            desc,
          },
          callback_listener: (res: string) => {
            const result = JSON.parse(res) as ShareResult
            result && result.success
              ? resolve(result)
              : reject(result)
          },
        })
      })
    },
  },
  weibo: {
    name: 'weibo',
    image: 'assets/images/social-media/weibo.png',
    handler: client => config => {
      const { title, content, tags } = config
      const desc = content + '\n\t\n' + tags.join(' ')
      return new Promise((resolve, reject) => {
        client.share({
          type: 'text',
          channel: 'weibo',
          share_content: {
            title,
            link: desc,
            desc,
          },
          callback_listener: (res: string) => {
            const result = JSON.parse(res) as ShareResult
            result && result.success
              ? resolve(result)
              : reject(result)
          },
        })
      })
    },
  },
}
