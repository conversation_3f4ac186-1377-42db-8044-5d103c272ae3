export interface TrackEvents<T extends EventType, K extends keyof EventMap[T]> {
  events: Record<T, TrackEvent<T, K>[]>
}

export type ISODate = string

export type TrackEvent<T extends keyof EventMap, K extends keyof EventMap[T], Y = EventMap[T][K]> = {
  // @ts-expect-error
  type: Y['type']
  event: K
  event_time: ISODate
  user?: any
} & (
  // @ts-expect-error
  Y['content'] extends { [string | number | symbol]: any }
    ? {
      // @ts-expect-error
        content: Y['content']
      }
    : object
  )

export type EventType = keyof EventMap

export interface EventMap {
  group_study_square_events: {
    load_group_study_page: {
      content: {
        page_name: 'square'
      }
      type: 'PageView'
    }
    click_group_study_square_button: {
      type: 'Click'
    }
    click_group_study_square_group: {
      type: 'Click'
      content: {
        group_id: string
        browsing: boolean
      }
    }
    join_group_study_group: {
      type: 'Click'
      content: {
        group_id: string
        source: 'square'
      }
    }
    view_group_study_group: {
      type: 'Exposure'
      content: {
        group_id: string
        source: 'square'
      }
    }
    click_group_study_promotion_banner: {
      type: 'Click'
      content: {
        group_id: string
        proposal_id: string
      }
    }
  }
}
