import { GroupStatus } from '../group-study-v2/models/group.model'

export const GROUP_NAMES_LATEST: readonly string[] = [
  '百年修得同一队',
  '如何表白才对',
  '是王维诗里的组队',
  '不如咱俩/仨组个队',
  '喜欢我就要和我组队',
  '好想和你凑一对',
  '定不负此队',
  '🐘🐘队',
  '整个宇宙换颗红豆队',
  '数落叶摘星星队',
  '故事的小黄花队',
  '狗粮专卖店',
  '单身狗自然保护区',
  '回家的诱惑队',
  '快乐不落单队',
  '酸柠檬队',
  '百年修得同一队',
  '深情凝视队',
  '执左右之手队',
  '形影不离队',
  '如何表白才对',
  '除却巫山不是队',
  '风雨夜留人队',
  '左右采之队',
  '柔情似队',
  '佳期如队',
  '为伊消得组个队',
  '情何以队',
  '此情可待组个队？',
  '相见时易完成难队',
  '此队胜却人间无数队',
  '忍顾鹊桥归队',
  '山盟虽在，锦书难托，队队队！',
  '定不负此队',
  '日日思君记得签到队',
  '冬雷震震夏雨雪队',
  '好想和你凑一队',
  '有请下一组',
  '路见不平拔刀相队',
  '搓圆揉扁小面团',
  '墨墨队队队',
]

export const GROUP_NAMES_LEGACY: readonly string[] = [
  '百年修得同一队',
  '如何表白才对',
  '是王维诗里的组队',
  '不如咱俩/仨组个队',
  '喜欢我就要和我组队',
  '此队胜却人间无数队',
  '好想和你凑一对',
  '定不负此队',
  '日日思君记得签到队',
  '立刻有队',
  '🐘🐘队',
  '整个宇宙换颗红豆队',
  '数落叶摘星星队',
  '故事的小黄花队',
  '今晚月色真美队',
  '第二份半价队',
  '狗粮专卖店队',
  '相亲相爱一家人队',
  '单身狗自然保护区',
  '回家的诱惑队',
  '共度一年四季队',
  '快乐不落单队',
  '酸柠檬队',
  '孤寡队',
]

export const ACTIVE_GROUP_STATUS: readonly GroupStatus[] = [
  'GROUPING',
  'GROUP_SUCCEEDED',
  'LEARNING',
  'TESTING',
  'LEARN_SUCCEED',
]

export const INACTIVE_GROUP_STATUS: readonly GroupStatus[] = [
  'GROUP_DISMISSED',
  'TEST_FAILED',
  'GROUP_FAILED',
]

export const TEST_GROUP_NATIONAL_DAY_2024 = 'NATIONAL_DAY_2024_UI'
export const TEST_GROUP_LANTERN_2024 = 'LANTERN_FESTIVAL_2025'
