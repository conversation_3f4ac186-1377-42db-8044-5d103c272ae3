import { Directive, ElementRef, Input, OnChanges, SecurityContext, SimpleChanges } from '@angular/core'
import { DomSanitizer } from '@angular/platform-browser'

@Directive({
  selector: '[appToast]',
  standalone: false,
})
export class ToastDirective implements OnChanges {
  emphasisStyle = {
    fontWeight: '900',
  }

  _content = ''
  get content() {
    return this._content
  }

  @Input() set content(content: string) {
    this._content = content.replace(/\*{2}(.+)\*{2}/g, `<span style="font-weight: ${this.emphasisStyle.fontWeight}">$1</span>`)
  }

  constructor(
    private el: ElementRef,
    public sanitizer: DomSanitizer,
  ) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (!this.content) {
      this.el.nativeElement.style.display = 'none'
    } else {
      this.el.nativeElement.style.display = 'block'
      this.el.nativeElement.innerHTML = this.sanitizer.sanitize(SecurityContext.STYLE, this.content)
    }
  }
}
