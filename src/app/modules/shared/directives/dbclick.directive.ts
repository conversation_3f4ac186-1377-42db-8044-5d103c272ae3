import { Directive, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output } from '@angular/core'
import { Subject, Subscription, buffer, filter, map } from 'rxjs'
import { debounceTime } from 'rxjs/operators'

@Directive({
  selector: '[appDbclick]',
  standalone: false,
})
export class DbclickDirective implements OnInit, OnDestroy {
  @Input() debounceTime = 250
  @Output() doubleClick = new EventEmitter()
  private clicks = new Subject<any>()
  private subscription?: Subscription

  ngOnInit() {
    const buff = this.clicks.pipe(
      debounceTime(this.debounceTime),
    )
    this.subscription = this.clicks.pipe(
      buffer(buff),
      map(list => list.length),
      filter(x => x === 2),
    ).subscribe(e => {
      this.doubleClick.emit(e)
    },
    )
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe()
  }

  @HostListener('click', ['$event']) onclick(event: MouseEvent) {
    this.clicks.next(event)
    event.preventDefault()
  }
}
