import { Directive, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output } from '@angular/core'
import { Subject, Subscription } from 'rxjs'
import { debounceTime } from 'rxjs/operators'

@Directive({
  selector: '[appClick]',
  standalone: false,
})
export class ClickDirective implements OnInit, OnDestroy {
  @Input() debounceTime = 1000
  @Output() debounceClick = new EventEmitter()
  private clicks = new Subject<any>()
  private subscription?: Subscription
  ngOnInit() {
    this.subscription = this.clicks.pipe(
      debounceTime(this.debounceTime),
    ).subscribe(e => this.debounceClick.emit(e))
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe()
  }

  @HostListener('click', ['$event']) onclick(event: MouseEvent) {
    this.clicks.next(event)
  }
}
