import { CommonModule } from '@angular/common'
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core'
import { SvgIconComponent } from '@ngneat/svg-icon'
import { UiModule } from '../../../ui/ui.module'
import { CardComponent } from '../../components/card/card.component'
import { ReportPageComponent } from '../../components/report-page/report-page.component'
import { StudyBadgeComponent } from '../../components/study-badge/study-badge.component'

const COMPONENTS = [
  ReportPageComponent,
  StudyBadgeComponent,
]

@NgModule({
  declarations: [
    COMPONENTS,
  ],
  imports: [
    CommonModule,
    UiModule,
    CardComponent,
    SvgIconComponent,
  ],
  exports: [
    COMPONENTS,
    CardComponent,
  ],
  schemas: [
    CUSTOM_ELEMENTS_SCHEMA,
  ],
})
export class GroupStudyCommonModule {}
