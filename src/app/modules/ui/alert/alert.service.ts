import { Injectable } from '@angular/core'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AlertOptions } from '@ionic/angular'

@Injectable({
  providedIn: 'root',
})
export class MemoAlertService {
  constructor(
    private alertCtrl: AlertController,
  ) {}

  async show(opts?: AlertOptions) {
    const ctrl = await this.alertCtrl.create(opts)

    ctrl.present()

    return ctrl
  }

  async dismiss(instance?: HTMLIonAlertElement) {
    const target = instance ?? this.alertCtrl
    try {
      await target.dismiss()
    } catch (error) {
      console.error(error)
    }
  }
}
