import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core'

@Component({
  selector: 'memo-button, button[memo-button]',
  template: `<ng-content></ng-content>
`,
  styleUrls: ['./button.component.scss'],
  host: {
    '[style.--width]': 'width()',
    '[style.--text-color]': 'textColor()',
    '[style.--bg-color]': 'backgroundColor()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MemoButtonComponent {
  private readonly skins = {
    theme: {
      textColor: '#fff',
      bgColor: 'var(--theme-green)',
    },
    clear: {
      textColor: 'var(--title-color)',
      bgColor: 'none',
    },
    light: {
      textColor: 'var(--theme-green)',
      bgColor: 'var(--title-font-color)',
    },
    bright: {
      textColor: '#fff',
      bgColor: 'var(--yellow-bgColor)',
    },
  }

  width = input('fit-content')

  skin = input<keyof typeof this.skins>('theme')

  textColor = computed(() => {
    return this.skins[this.skin()]?.textColor || 'var(--title-color)'
  })

  backgroundColor = computed(() => {
    return this.skins[this.skin()]?.bgColor || 'var(--theme-green)'
  })
}
