:host {
  --width: fit-content;
  --bg-color: var(--theme-green);

  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1em;
  border-radius: 4px;
  margin: 0 auto;
  outline: none;
  border: none;
  padding: 10px 0;
  line-height: 1.25;
  min-width: 150px;
  width: var(--width);
  color: var(--text-color);
  background-color: var(--bg-color);
  cursor: pointer;

  &:not(:disabled):active {
    filter: brightness(0.9);
  }

  &:disabled {
    color: #fff !important;
    background-color: var(--disabled-color) !important;
  }

  & > :not(:last-child) {
    margin-right: 0.5em;
  }
}
