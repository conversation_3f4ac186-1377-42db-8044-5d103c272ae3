import { Injectable } from '@angular/core'
import { ModalController } from '@ionic/angular'

@Injectable({
  providedIn: 'root',
})
export class MemoModalService {
  constructor(
    private modal: ModalController,
  ) {}

  private fixScrollHandler = (e: TouchEvent) => {
    e.stopPropagation()
    e.preventDefault()
  }

  async create(opts: Parameters<ModalController['create']>[0]) {
    const ref = await this.modal.create(opts)

    ref.addEventListener('touchmove', this.fixScrollHandler)
    ref.onDidDismiss().then(() => {
      ref.removeEventListener('touchmove', this.fixScrollHandler)
    })

    ref.present()
    return ref
  }

  dismiss() {
    return this.modal.dismiss()
  }
}
