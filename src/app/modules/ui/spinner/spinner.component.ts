import { Component, input } from '@angular/core'

@Component({
  selector: 'memo-spinner',
  template: `<svg viewBox="0 0 24 24">
  <circle
    cx="12"
    cy="12"
    r="10"
    fill="none"
    stroke="currentColor"
    stroke-width="2.5"
    stroke-linecap="round"
    [style.stroke-dasharray]="dashArray"
    [style.stroke-dashoffset]="dashArray - spinningProgress() * dashArray"
  />
</svg>
`,
  host: {
    '[class.spinning]': 'spinning()',
  },
  styleUrl: './spinner.component.css',
})
export class MemoSpinnerComponent {
  readonly dashArray = 60

  spinning = input(true)
  spinningProgress = input(this.dashArray)
}
