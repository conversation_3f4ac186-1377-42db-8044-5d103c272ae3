:host {
  --pull-refresh-spinner-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--pull-refresh-spinner-size);
  height: var(--pull-refresh-spinner-size);
}

:host svg {
  width: 100%;
  height: 100%;
}

:host.spinning svg {
  animation: pull-refresh-spin 1s linear infinite;
}

:host.spinning circle {
  animation: pull-refresh-dash 1.5s ease-in-out infinite;
}

@keyframes pull-refresh-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pull-refresh-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 90, 200;
    stroke-dashoffset: -124px;
  }
}
