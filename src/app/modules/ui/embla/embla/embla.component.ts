import { ChangeDetectionStrategy, Component, input } from '@angular/core'
import { EmblaCarouselDirective, EmblaCarouselType } from 'embla-carousel-angular'

@Component({
  selector: 'memo-embla',
  templateUrl: './embla.component.html',
  host: {
    '[class.embla]': 'true',
    '[class.centered-slides]': 'centeredSlides()',
    '[attr.direction]': 'options().axis === "y" ? "vertical" : "horizontal"',
  },
  styleUrls: ['./embla.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EmblaComponent extends EmblaCarouselDirective {
  centeredSlides = input(false)

  sync(target: EmblaCarouselType) {
    target.on('select', e => {
      this.scrollTo(e.selectedScrollSnap())
    })
  }
}
