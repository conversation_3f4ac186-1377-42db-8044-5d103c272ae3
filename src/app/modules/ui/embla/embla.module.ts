import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { EmblaCarouselDirective } from 'embla-carousel-angular'
import { EmblaSlideComponent } from './embla-slide/embla-slide.component'
import { EmblaComponent } from './embla/embla.component'

const COMPONENTS = [
  EmblaComponent,
  EmblaSlideComponent,
]

@NgModule({
  declarations: [COMPONENTS],
  imports: [
    CommonModule,
    EmblaCarouselDirective,
  ],
  exports: [COMPONENTS],
  providers: [],
})
export class EmblaModule {}
