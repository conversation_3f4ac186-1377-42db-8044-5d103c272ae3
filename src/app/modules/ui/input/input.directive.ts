import { Directive, ElementRef, HostListener, inject, input } from '@angular/core'

@Directive({
  selector: 'input[memoInput]',
  standalone: false,
})
export class MemoInputDirective {
  selectToEndOnFocus = input(false)
  disabled = input(false)

  private elementRef = inject<ElementRef<HTMLInputElement>>(ElementRef)

  private _isBlur = true

  @HostListener('touchstart', ['$event'])
  onFocus(event: TouchEvent) {
    if (!this.selectToEndOnFocus() || !this._isBlur || this.disabled()) {
      return
    }

    event.preventDefault()
    const { nativeElement: inputEl } = this.elementRef
    const valueLength = inputEl.value.length

    inputEl.setSelectionRange(valueLength, valueLength)
    inputEl.focus()
    this._isBlur = false
  }

  @HostListener('blur')
  private onBlur() {
    this._isBlur = true
  }
}
