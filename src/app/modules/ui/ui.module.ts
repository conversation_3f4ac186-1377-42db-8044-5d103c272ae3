import { CommonModule } from '@angular/common'
import { NgModule } from '@angular/core'
import { BadgeComponent } from './badge/badge.component'
import { MemoBannerComponent } from './banner/banner.component'
import { MemoButtonComponent } from './button/button.component'
import { MemoIconComponent } from './icon/icon.component'
import { MemoInputDirective } from './input/input.directive'
import { PickerFooterComponent } from './picker/picker-footer/picker-footer.component'
import { MemoToastComponent } from './toast/toast.component'
import { MemoClickDirective } from './utils/memo-click.directive'
import { MemoRippleDirective } from './utils/memo-ripple.directive'
import { MemoMarqueeComponent } from './marquee/marquee.component'

const COMPONENTS = [
  PickerFooterComponent,
  BadgeComponent,
  MemoToastComponent,
  MemoBannerComponent,
  MemoClickDirective,
  MemoRippleDirective,
  MemoInputDirective,
]

const BUNDLE = [
  MemoButtonComponent,
  MemoIconComponent,
  MemoMarqueeComponent,
]

@NgModule({
  declarations: [
    COMPONENTS,
  ],
  imports: [
    CommonModule,
    BUNDLE,
  ],
  exports: [
    COMPONENTS,
    BUNDLE,
  ],
})
export class UiModule { }
