import { ChangeDetectionStrategy, Component, ElementRef, effect, inject, input } from '@angular/core'

@Component({
  selector: 'memo-toast',
  template: '',
  styleUrls: ['./toast.component.scss'],
  host: {
    '[class.hidden]': 'content() === ""',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class MemoToastComponent {
  private elementRef = inject<ElementRef<HTMLElement>>(ElementRef)
  content = input('')

  constructor() {
    effect(() => {
      const el = this.elementRef.nativeElement
      const content = this.content()
      if (content && el) {
        el.innerHTML = content
      }
    })
  }
}
