import { Injectable, SecurityContext, computed, signal } from '@angular/core'
import { DomSanitizer } from '@angular/platform-browser'

@Injectable({
  providedIn: 'root',
})
export class MemoToastService {
  private _toastContent = signal('')
  private toastTimer: any

  toastContent = computed(() => this._toastContent())

  constructor(
    private sanitizer: DomSanitizer,
  ) {}

  showToast(content: string, wait = 2500) {
    if (this.toastTimer) {
      this._toastContent.set('')
      clearTimeout(this.toastTimer)
    }

    this._toastContent.set(this.parseContent(content))

    this.toastTimer = setTimeout(() => {
      this._toastContent.set('')
      this.toastTimer = null
    }, wait)
  }

  private parseContent(content: string): string {
    return content
      .replace(/\*{2}(.+)\*{2}/g, (_, trimed) => {
        const sanitized = this.sanitizer.sanitize(SecurityContext.HTML, trimed)
        return `<span style="font-weight: 900">${sanitized}</span>`
      })
  }
}
