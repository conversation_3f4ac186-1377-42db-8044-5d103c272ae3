:host {
  --background-color: 220, 102, 62;

  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 5px 15px;
  color: #fff;
  background-color: rgba(var(--background-color), 0.4);
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: first baseline;
  z-index: 99999;
}

.memo-banner-content-container {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
}

.memo-banner-content {
  padding: 0;
  margin: 0;
}

memo-icon {
  box-sizing: content-box !important;
  padding: 4px;
  padding-left: 8px;
}
