import { AnimationBuilder, AnimationFactory, AnimationPlayer, animate, keyframes, style } from '@angular/animations'
import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, OnDestroy, Output, ViewChild } from '@angular/core'

@Component({
  selector: 'memo-banner',
  templateUrl: './banner.component.html',
  styleUrls: ['./banner.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class MemoBannerComponent implements AfterViewInit, OnDestroy {
  @Input()
  content = ''

  @Output()
  iconClick = new EventEmitter<void>()

  private animation?: AnimationPlayer

  @ViewChild('bannerContent')
  private bannerContentRef!: ElementRef<HTMLElement>

  private animationBuilder?: AnimationFactory

  constructor(
    private builder: AnimationBuilder,
  ) {}

  ngAfterViewInit(): void {
    const { nativeElement } = this.bannerContentRef
    const overFlowWidth = this.content.length * 14 - nativeElement.clientWidth
    if (overFlowWidth <= 0) {
      return
    }
    this.animationBuilder = this.builder.build([
      style({ transofrm: 'translateX(0px)' }),
      animate(this.content.length * 200, keyframes([
        style({ transform: 'translateX(0px)', offset: 0.1 }),
        style({ transform: `translateX(-${overFlowWidth}px)`, offset: 1 }),
      ])),
    ])

    this.animation = this.animationBuilder.create(nativeElement)
    this.animation?.play()
    this.animation.onDone(() => {
      this.animation?.restart()
    })
  }

  ngOnDestroy(): void {
    if (this.animation) {
      this.animation.destroy()
    }
  }
}
