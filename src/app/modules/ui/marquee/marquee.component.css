:host {
  --gap: 3px;
  --duration: 40s;

  display: flex;
  overflow: hidden;
}

:host > *:not(:last-child),
.memo-marquee-item > *:not(:last-child) {
  margin-right: var(--gap);
}

.memo-marquee-item {
  display: flex;
  flex-shrink: 0;
  justify-content: space-around;

}
.animate-marquee {
  animation: marquee var(--duration) linear infinite;
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-100% - var(--gap)));
  }
}

@keyframes marquee-vertical {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(calc(-100% - var(--gap)));
  }
}
