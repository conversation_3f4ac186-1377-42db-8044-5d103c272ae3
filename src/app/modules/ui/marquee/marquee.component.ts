import { ChangeDetectionStrategy, Component, TemplateRef, contentChild, input } from '@angular/core'
import { NgTemplateOutlet } from '@angular/common'
import { ListPipe } from '../../shared/pipes/list.pipe'

@Component({
  selector: 'memo-marquee',
  templateUrl: './marquee.component.html',
  styleUrls: ['./marquee.component.css'],
  imports: [
    NgTemplateOutlet,
    ListPipe,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MemoMarqueeComponent {
  contentTemplateRef = contentChild<TemplateRef<any>>('content')

  /**
   * @default 4
   */
  repeat = input(4)
}
