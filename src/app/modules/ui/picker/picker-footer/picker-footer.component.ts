import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { PickerButton } from '@ionic/angular'

@Component({
  selector: 'app-picker-footer',
  templateUrl: './picker-footer.component.html',
  styleUrls: ['./picker-footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class PickerFooterComponent {
  @Input() buttons: PickerButton[] = []

  onButtonClick(button: PickerButton) {
    //
  }
}
