import { DOCUMENT } from '@angular/common'
import { ApplicationRef, Inject, Injectable, Renderer2, RendererFactory2, TemplateRef, createComponent } from '@angular/core'
import { PickerButton, PickerColumn, PickerController, PickerOptions } from '@ionic/angular'
import { fromEvent, takeUntil } from 'rxjs'
import { PickerFooterComponent } from './picker-footer/picker-footer.component'
import { PickerSelectedInfo } from './picker.type'

@Injectable({
  providedIn: 'root',
})
export class PickerService {

  private readonly DEFAULT_CLASS = 'memo-picker'
  private rd2!: Renderer2

  constructor(
    @Inject(DOCUMENT) private _doc: Document,
    private pickerCtrl: PickerController,
    private rdFactory: RendererFactory2,
    private appRef: ApplicationRef,
  ) {
    this.rd2 = this.rdFactory.createRenderer(this._doc, null)
  }

  async create(title: string, opts?: PickerOptions, footer?: TemplateRef<HTMLElement> | PickerButton[]) {

    if (opts) {
      const cssClasses = Array.isArray(opts.cssClass) ? opts.cssClass : [opts.cssClass || '']
      cssClasses.unshift(this.DEFAULT_CLASS)
      opts.cssClass = cssClasses
    }

    const picker = await this.pickerCtrl.create(opts)
    const wrapper = picker.querySelector('.picker-wrapper') as HTMLElement
    const columnsContainer = wrapper.querySelector('.picker-columns') as HTMLElement

    // title
    const titleEl = this.createElement('h3')
    this.rd2.addClass(titleEl, `${this.DEFAULT_CLASS}-title`)
    titleEl.textContent = title
    this.rd2.insertBefore(wrapper, titleEl, columnsContainer)

    // prefix
    {
      const prefixes: string[] = picker.columns.map(col => col.prefix || '')

      if (prefixes.some(prefix => prefix.length > 0)) {
        const prefixContainer = this.createElement('div')
        this.rd2.addClass(prefixContainer, `${this.DEFAULT_CLASS}-prefixes`)
        prefixes.forEach(prefix => {
          const prefixElement = this.createElement('span')
          prefixElement.innerHTML = prefix
          this.rd2.appendChild(prefixContainer, prefixElement)
        })
        this.rd2.insertBefore(wrapper, prefixContainer, columnsContainer)
      }

    }

    // footer
    if (footer) {

      const footerComponentRef = createComponent(PickerFooterComponent, {
        environmentInjector: this.appRef.injector,
      })

      if (Array.isArray(footer)) {

        footerComponentRef.instance.buttons = footer
        footerComponentRef.instance.onButtonClick = button => {
          this.onPickerButtonClick(picker, button)
        }
        footerComponentRef.changeDetectorRef.detectChanges()
        this.rd2.appendChild(wrapper, footerComponentRef.location.nativeElement)

      } else {
        const embeddedView = footer.createEmbeddedView(wrapper)
        embeddedView.rootNodes.forEach(node => {
          this.rd2.appendChild(wrapper, node)
        })
        embeddedView.detectChanges()
      }
    }

    // 解决 Android 上的滚动穿透问题
    fromEvent(picker, 'touchmove')
      .pipe(takeUntil(picker.onWillDismiss()))
      .subscribe(e => e.preventDefault())

    return picker
  }

  getOptionIndexByValue<T>(column: PickerColumn, value: T): number {
    return column.options.findIndex(opt => opt.value === value)
  }


  /**
   * https://github.com/ionic-team/ionic-framework/blob/70d9854d8df5259ed715e282a6ca40ca3bea6192/core/src/components/picker/picker.tsx#L282-L293
   * @param button
   * @returns
   */
  private async onPickerButtonClick(picker: HTMLIonPickerElement, button: PickerButton) {
    const role = button.role
    if (role === 'cancel' || role === 'backdrop') {
      return picker.dismiss(undefined, role)
    }
    const selectedValue = this.getSelected(picker)
    const shouldDismiss = button.handler?.(selectedValue)
    if (shouldDismiss) {
      return picker.dismiss(selectedValue, button.role)
    }
    return Promise.resolve()
  }


  /**
   * https://github.com/ionic-team/ionic-framework/blob/70d9854d8df5259ed715e282a6ca40ca3bea6192/core/src/components/picker/picker.tsx#L307-L318
   * @param picker
   * @returns
   */
  private getSelected(picker: HTMLIonPickerElement) {
    const selected: Record<string, PickerSelectedInfo> = {}
    picker.columns.forEach((col, index) => {
      const selectedColumn = col.selectedIndex !== undefined ? col.options[col.selectedIndex] : undefined
      selected[col.name] = {
        text: selectedColumn ? selectedColumn.text : undefined,
        value: selectedColumn ? selectedColumn.value : undefined,
        columnIndex: index,
      }
    })
    return selected
  }

  private createElement<T extends HTMLElement = HTMLElement>(name: string): T {
    return this.rd2.createElement(name)
  }
}
