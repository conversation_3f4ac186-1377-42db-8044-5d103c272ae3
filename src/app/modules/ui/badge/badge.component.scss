:host {
  --badge-size: 18px;
  --badge-color: 130, 194, 171;
  --badge-bg: 255, 255, 255;
  --badge-bg-alpha: 1;
  --badge-radius: 100vh;
  --badge-font-weight: normal;
  --padding-x: 0;
  --padding-y: 4px;

  box-sizing: border-box;
  padding: var(--padding-y) var(--padding-x);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: 100vh;
  height: var(--badge-size);
  min-width: var(--badge-size);
  font-weight: bold;
  font-size: calc(var(--badge-size) - 8px);
  contain: content;
  border-radius: var(--badge-radius);
  height: calc(var(--badge-size) + var(--padding-y) * 2);
  min-width: calc(var(--badge-size) + var(--padding-y) * 2);
  font-weight: var(--badge-font-weight);
  font-size: var(--badge-size);
  color: rgb(var(--badge-color));
  background-color: rgba(var(--badge-bg), var(--badge-bg-alpha));
}

:host.wider {
  --padding-x: 8px;
}

:host.with-border {
  border: 2px solid rgb(var(--badge-color));
}

:root.dark :host {
  --badge-bg: 39, 40, 41;
}
