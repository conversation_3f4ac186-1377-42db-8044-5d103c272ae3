import { ChangeDetectionStrategy, Component, input } from '@angular/core'

@Component({
  selector: 'memo-badge',
  template: `{{ value() }}
`,
  styleUrls: ['./badge.component.scss'],
  host: {
    '[class.wider]': 'value() > 9',
    '[class.with-border]': 'withBorder()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class BadgeComponent {
  value = input<number>()
  withBorder = input(false)
}
