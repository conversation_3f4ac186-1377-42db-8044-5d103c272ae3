import { ChangeDetectionStrategy, Component, ElementRef, NgZone, effect, inject, input } from '@angular/core'
import { ICONS } from './icons'

@Component({
  selector: 'memo-icon',
  template: `<ng-content></ng-content>
`,
  styles: [`
    :host {
      display: inline-flex;
    }
  `],
  host: {
    '[style.height]': 'size()',
    '[style.width]': 'size()',
  },
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MemoIconComponent {
  private ngZone = inject(NgZone)
  private elementRef = inject<ElementRef<HTMLElement>>(ElementRef)

  name = input<keyof typeof this.defaultIcons>()

  size = input('1rem')

  private defaultIcons = ICONS

  constructor() {
    effect(() => {
      const name = this.name()
      if (!name) return

      this.ngZone.runOutsideAngular(() => {
        const svgData = this.defaultIcons[name]
        this.elementRef.nativeElement.innerHTML = svgData
      })
    })
  }
}
