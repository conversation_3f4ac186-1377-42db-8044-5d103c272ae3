export const ICONS = {
  refresh: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 16 16" fill="none"><path id="路径 1" fill-rule="evenodd" style="fill:currentColor" opacity="1" d="M14.07,0.59l1.04,3.26c0,0.3 -0.3,0.74 -0.59,0.74l-3.41,-0.15c-0.44,0 -0.74,-0.59 -0.44,-0.88l0.74,-0.89v-0.15c-0.89,-0.74 -2.22,-1.04 -3.41,-1.04c-3.56,0 -6.52,2.96 -6.52,6.52c0,3.56 2.96,6.52 6.52,6.52c3.56,0 6.52,-2.96 6.52,-6.52c0,-0.44 0.29,-0.74 0.74,-0.74c0.44,0 0.74,0.3 0.74,0.74c0,4.44 -3.56,8 -8,8c-4.44,0 -8,-3.56 -8,-8c0,-4.44 3.56,-8 8,-8c1.63,0 3.11,0.44 4.44,1.33l0.6,-0.89c0.29,-0.44 0.89,-0.29 1.03,0.15z"></path></svg>
  `,
  add: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 16 16" fill="none"><g opacity="1"  transform="translate(0 0)  rotate(0)"><path id="直线 1" fill-rule="evenodd" style="fill:currentColor" opacity="1" d="M0 8.99951L16 8.99951L16 6.99951L0 6.99951L0 8.99951Z"></path><path id="直线 1" fill-rule="evenodd" style="fill:currentColor" opacity="1" d="M7.00006 0L7.00006 16L9.00006 16L9.00006 0L7.00006 0Z"></path></g></svg>
  `,
  arrow: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 16 16" fill="none"><g opacity="1"  transform="translate(0 0)  rotate(0)"><mask id="bg-mask-0" fill="white"><use xlink:href="#path_0"></use></mask><g mask="url(#bg-mask-0)" ><path id="icn_arrow (轮廓)" fill-rule="evenodd" style="fill:currentColor" opacity="1" d="M15.7,5.76l-6.95,6.93c-0.06,0.06 -0.11,0.09 -0.16,0.13c-0.07,0.04 -0.13,0.07 -0.2,0.11c-0.05,0.02 -0.12,0.03 -0.19,0.05c-0.06,0.02 -0.13,0.02 -0.2,0.02c-0.07,0 -0.14,0 -0.2,-0.02c-0.07,-0.02 -0.14,-0.03 -0.19,-0.05c-0.07,-0.04 -0.13,-0.07 -0.2,-0.11c-0.05,-0.04 -0.1,-0.07 -0.16,-0.13l-6.95,-6.93c-0.05,-0.03 -0.09,-0.09 -0.12,-0.14c-0.04,-0.05 -0.07,-0.13 -0.11,-0.18c-0.02,-0.07 -0.03,-0.13 -0.05,-0.2c-0.02,-0.07 -0.02,-0.14 -0.02,-0.2c0,-0.07 0,-0.14 0.02,-0.21c0.02,-0.06 0.03,-0.13 0.05,-0.18c0.04,-0.07 0.07,-0.13 0.11,-0.18c0.03,-0.07 0.07,-0.11 0.12,-0.16c0.11,-0.11 0.22,-0.18 0.34,-0.24c0.12,-0.05 0.27,-0.07 0.41,-0.07c0.12,0 0.27,0.02 0.39,0.07c0.12,0.06 0.25,0.13 0.34,0.24l6.22,6.19l6.22,-6.19c0.06,-0.06 0.09,-0.09 0.16,-0.13c0.06,-0.04 0.11,-0.07 0.18,-0.11c0.05,-0.02 0.12,-0.03 0.2,-0.05c0.07,-0.02 0.12,-0.02 0.19,-0.02c0.07,0 0.14,0 0.21,0.02c0.08,0.02 0.13,0.03 0.2,0.05c0.05,0.04 0.12,0.07 0.18,0.11c0.05,0.04 0.1,0.07 0.16,0.13c0.05,0.05 0.09,0.09 0.12,0.16c0.04,0.05 0.07,0.11 0.11,0.18c0.02,0.05 0.03,0.12 0.05,0.18c0.02,0.07 0.02,0.14 0.02,0.21c0,0.06 0,0.13 -0.02,0.2c-0.02,0.07 -0.03,0.13 -0.05,0.2c-0.04,0.05 -0.07,0.13 -0.11,0.18c-0.03,0.05 -0.07,0.11 -0.12,0.14z"></path></g></g><defs><rect id="path_0" x="0" y="0" width="100%" height="100%" /></defs></svg>
  `,
  cross: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 16 16" fill="none"><g opacity="1"  transform="translate(0 0)  rotate(0)"><mask id="bg-mask-0" fill="white"><use xlink:href="#path_0"></use></mask><g mask="url(#bg-mask-0)" ><path id="路径 2" fill-rule="evenodd" style="fill:currentColor" opacity="1" d="M1.45 0C0.65 0 0 0.65 0 1.45C0.01 1.79 0.15 2.11 0.39 2.34L6.05 8L0.39 13.58C0.14 13.84 -0.01 14.18 0 14.54C0 15.35 0.65 16 1.45 16C1.81 15.99 2.15 15.85 2.4 15.59L8 9.99L13.54 15.61C13.81 15.87 14.17 16.01 14.55 16C15.35 16 16 15.35 16 14.54C15.99 14.21 15.85 13.89 15.61 13.66L9.95 8L15.61 2.41C15.86 2.16 16.01 1.81 16 1.45C16 0.65 15.35 0 14.55 0C14.18 0 13.83 0.16 13.59 0.42L8 6.01L2.39 0.39C2.14 0.15 1.8 0 1.45 0Z"></path></g></g><defs><rect id="path_0" x="0" y="0" width="100%" height="100%" /></defs></svg>
  `,
  checkBadge: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 16 16" fill="none"><g opacity="1"  transform="translate(0 0)  rotate(0)"><mask id="bg-mask-0" fill="white"><use xlink:href="#path_0"></use></mask><g mask="url(#bg-mask-0)" ><path id="矩形 9" fill-rule="evenodd" style="fill:currentColor" opacity="1" d="M8,16c-4.42,0 -8,-3.58 -8,-8c0,-4.42 3.58,-8 8,-8c4.42,0 8,3.58 8,8c0,4.42 -3.58,8 -8,8z"></path><path id="路径 9" fill-rule="evenodd" style="fill:#FFFFFF" opacity="1" d="M11.7237 5.19503C11.6937 5.18503 11.6537 5.16503 11.6237 5.15503C11.5837 5.14503 11.5537 5.13503 11.5137 5.12503C11.4737 5.12503 11.4437 5.12503 11.4037 5.12503C11.3637 5.12503 11.3237 5.13503 11.2937 5.14503C11.2537 5.15503 11.2237 5.16503 11.1837 5.18503C11.1537 5.20503 11.1237 5.22503 11.0937 5.24503C11.0637 5.26503 11.0337 5.29503 11.0137 5.32503C11.0037 5.32503 11.0037 5.33503 10.9937 5.34503L9.66374 6.88503L7.24374 9.67503L5.60374 8.15503C5.57374 8.13503 5.54374 8.10503 5.51374 8.08503C5.48374 8.06503 5.44374 8.05503 5.41374 8.04503C5.37374 8.02503 5.34374 8.01503 5.30374 8.01503C5.26374 8.00503 5.23374 8.00503 5.19374 8.00503C5.15374 8.00503 5.11374 8.01503 5.08374 8.02503C5.04374 8.02503 5.01374 8.04503 4.97374 8.05503C4.94374 8.07503 4.91374 8.09503 4.88374 8.11503C4.85374 8.13503 4.82374 8.16503 4.79374 8.18503C4.77374 8.21503 4.74374 8.24503 4.72374 8.27503C4.70374 8.30503 4.69374 8.34503 4.68374 8.37503C4.66374 8.41503 4.65374 8.44503 4.65374 8.48503C4.64374 8.52503 4.64374 8.56503 4.64374 8.59503C4.64374 8.63503 4.65374 8.67503 4.66374 8.70503C4.66374 8.74503 4.68374 8.78503 4.69374 8.81503C4.71374 8.84503 4.73374 8.88503 4.75374 8.91503C4.77374 8.94503 4.80374 8.97503 4.82374 8.99503C4.83374 8.99503 4.83374 9.00503 4.83374 9.00503L6.71374 10.745C6.88374 10.895 7.08374 10.975 7.30374 10.965C7.54374 10.955 7.74374 10.855 7.89374 10.665L10.5237 7.62503L11.8737 6.07503C11.9037 6.04503 11.9237 6.01503 11.9437 5.97503C11.9537 5.94503 11.9737 5.91503 11.9837 5.87503C11.9937 5.84503 12.0037 5.80503 12.0137 5.76503C12.0137 5.72503 12.0137 5.69503 12.0137 5.65503C12.0137 5.61503 12.0037 5.58503 11.9937 5.54503C11.9837 5.50503 11.9737 5.47503 11.9537 5.44503C11.9337 5.40503 11.9137 5.37503 11.8937 5.34503C11.8737 5.31503 11.8437 5.28503 11.8137 5.26503C11.7837 5.23503 11.7537 5.21503 11.7237 5.19503Z"></path></g></g><defs><rect id="path_0" x="0" y="0" width="100%" height="100%" /></defs></svg>
  `,
  shrink: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 16 16" fill="none"><g opacity="1"  transform="translate(1.0606536865234375 1.060546875)  rotate(0)"><path  id="路径 1" style="fill:currentColor; opacity:1;" d="M11,4.25c0.04927,0 0.09803,0.0048 0.1463,0.01441c0.04833,0.00961 0.09523,0.02383 0.1407,0.04268c0.04553,0.01885 0.08877,0.04195 0.1297,0.06931c0.04093,0.02736 0.0788,0.05845 0.1136,0.09327c0.03487,0.03482 0.06597,0.0727 0.0933,0.11365c0.02733,0.04095 0.05043,0.08417 0.0693,0.12967c0.01887,0.04549 0.0331,0.09239 0.0427,0.14069c0.0096,0.0483 0.0144,0.09707 0.0144,0.14632c0,0.04925 -0.0048,0.09802 -0.0144,0.14632c-0.0096,0.0483 -0.02383,0.0952 -0.0427,0.14069c-0.01887,0.0455 -0.04197,0.08872 -0.0693,0.12967c-0.02733,0.04095 -0.05843,0.07883 -0.0933,0.11365c-0.0348,0.03482 -0.07267,0.06591 -0.1136,0.09327c-0.04093,0.02736 -0.08417,0.05046 -0.1297,0.06931c-0.04547,0.01885 -0.09237,0.03307 -0.1407,0.04268c-0.04827,0.00961 -0.09703,0.01441 -0.1463,0.01441h-4c-0.04925,0 -0.09802,-0.0048 -0.14632,-0.01441c-0.0483,-0.00961 -0.0952,-0.02383 -0.14069,-0.04268c-0.0455,-0.01885 -0.08872,-0.04195 -0.12967,-0.06931c-0.04095,-0.02736 -0.07883,-0.05845 -0.11365,-0.09327c-0.03482,-0.03482 -0.06591,-0.0727 -0.09327,-0.11365c-0.02736,-0.04095 -0.05046,-0.08417 -0.06931,-0.12967c-0.01885,-0.04549 -0.03307,-0.09239 -0.04268,-0.14069c-0.00961,-0.0483 -0.01441,-0.09707 -0.01441,-0.14632v-4c0,-0.04925 0.0048,-0.09802 0.01441,-0.14632c0.00961,-0.0483 0.02383,-0.0952 0.04268,-0.1407c0.01885,-0.0455 0.04195,-0.08872 0.06931,-0.12967c0.02736,-0.04095 0.05845,-0.07883 0.09327,-0.11365c0.03482,-0.03482 0.0727,-0.06591 0.11365,-0.09327c0.04095,-0.02736 0.08417,-0.05046 0.12967,-0.06931c0.04549,-0.01885 0.09239,-0.03307 0.14069,-0.04268c0.0483,-0.00961 0.09707,-0.01441 0.14632,-0.01441c0.04925,0 0.09802,0.0048 0.14632,0.01441c0.0483,0.00961 0.0952,0.02383 0.14069,0.04268c0.0455,0.01885 0.08872,0.04195 0.12967,0.06931c0.04095,0.02736 0.07883,0.05845 0.11365,0.09327c0.03482,0.03482 0.06591,0.07271 0.09327,0.11365c0.02736,0.04095 0.05046,0.08417 0.06931,0.12967c0.01885,0.0455 0.03307,0.0924 0.04268,0.1407c0.00961,0.0483 0.01441,0.09707 0.01441,0.14632v3.25z"></path><path  id="路径 2" style="fill:currentColor; opacity:1;" d="M1,7.75c-0.04925,0 -0.09802,-0.0048 -0.14632,-0.01441c-0.0483,-0.00961 -0.0952,-0.02383 -0.1407,-0.04268c-0.0455,-0.01885 -0.08872,-0.04195 -0.12967,-0.06931c-0.04095,-0.02736 -0.07883,-0.05845 -0.11365,-0.09327c-0.03482,-0.03482 -0.06591,-0.0727 -0.09327,-0.11365c-0.02736,-0.04095 -0.05046,-0.08417 -0.06931,-0.12967c-0.01885,-0.04549 -0.03307,-0.09239 -0.04268,-0.14069c-0.00961,-0.0483 -0.01441,-0.09707 -0.01441,-0.14632c0,-0.04925 0.0048,-0.09802 0.01441,-0.14632c0.00961,-0.0483 0.02383,-0.0952 0.04268,-0.14069c0.01885,-0.0455 0.04195,-0.08872 0.06931,-0.12967c0.02736,-0.04095 0.05845,-0.07883 0.09327,-0.11365c0.03482,-0.03482 0.07271,-0.06591 0.11365,-0.09327c0.04095,-0.02736 0.08417,-0.05046 0.12967,-0.06931c0.0455,-0.01885 0.0924,-0.03307 0.1407,-0.04268c0.0483,-0.00961 0.09707,-0.01441 0.14632,-0.01441h4c0.04925,0 0.09802,0.0048 0.14632,0.01441c0.0483,0.00961 0.0952,0.02383 0.14069,0.04268c0.0455,0.01885 0.08872,0.04195 0.12967,0.06931c0.04095,0.02736 0.07883,0.05845 0.11365,0.09327c0.03482,0.03482 0.06591,0.0727 0.09327,0.11365c0.02736,0.04095 0.05046,0.08417 0.06931,0.12967c0.01885,0.04549 0.03307,0.09239 0.04268,0.14069c0.00961,0.0483 0.01441,0.09707 0.01441,0.14632v4c0,0.04927 -0.0048,0.09803 -0.01441,0.1463c-0.00961,0.04833 -0.02383,0.09523 -0.04268,0.1407c-0.01885,0.04553 -0.04195,0.08877 -0.06931,0.1297c-0.02736,0.04093 -0.05845,0.0788 -0.09327,0.1136c-0.03482,0.03487 -0.0727,0.06597 -0.11365,0.0933c-0.04095,0.02733 -0.08417,0.05043 -0.12967,0.0693c-0.04549,0.01887 -0.09239,0.0331 -0.14069,0.0427c-0.0483,0.0096 -0.09707,0.0144 -0.14632,0.0144c-0.04925,0 -0.09802,-0.0048 -0.14632,-0.0144c-0.0483,-0.0096 -0.0952,-0.02383 -0.14069,-0.0427c-0.0455,-0.01887 -0.08872,-0.04197 -0.12967,-0.0693c-0.04095,-0.02733 -0.07883,-0.05843 -0.11365,-0.0933c-0.03482,-0.0348 -0.06591,-0.07267 -0.09327,-0.1136c-0.02736,-0.04093 -0.05046,-0.08417 -0.06931,-0.1297c-0.01885,-0.04547 -0.03307,-0.09237 -0.04268,-0.1407c-0.00961,-0.04827 -0.01441,-0.09703 -0.01441,-0.1463v-3.25z"></path><path  id="路径 3" style="fill:currentColor; opacity:1;" d="M6.46967,4.46967l5.00003,-5c0.0348,-0.03482 0.07267,-0.06591 0.1136,-0.09327c0.04093,-0.02736 0.08417,-0.05046 0.1297,-0.06931c0.04547,-0.01885 0.09237,-0.03307 0.1407,-0.04268c0.04827,-0.00961 0.09703,-0.01441 0.1463,-0.01441c0.04927,0 0.09803,0.0048 0.1463,0.01441c0.04833,0.00961 0.09523,0.02383 0.1407,0.04268c0.04553,0.01885 0.08877,0.04195 0.1297,0.06931c0.04093,0.02736 0.0788,0.05845 0.1136,0.09327c0.03487,0.03482 0.06597,0.07271 0.0933,0.11365c0.02733,0.04095 0.05043,0.08417 0.0693,0.12966c0.01887,0.0455 0.0331,0.0924 0.0427,0.1407c0.0096,0.0483 0.0144,0.09707 0.0144,0.14632c0,0.04925 -0.0048,0.09802 -0.0144,0.14632c-0.0096,0.0483 -0.02383,0.0952 -0.0427,0.1407c-0.01887,0.0455 -0.04197,0.08872 -0.0693,0.12966c-0.02733,0.04095 -0.05843,0.07883 -0.0933,0.11365l-4.99997,5c-0.03482,0.03482 -0.0727,0.06591 -0.11365,0.09327c-0.04095,0.02736 -0.08417,0.05046 -0.12967,0.06931c-0.04549,0.01885 -0.09239,0.03307 -0.14069,0.04268c-0.0483,0.00961 -0.09707,0.01441 -0.14632,0.01441c-0.04925,0 -0.09802,-0.0048 -0.14632,-0.01441c-0.0483,-0.00961 -0.0952,-0.02383 -0.14069,-0.04268c-0.0455,-0.01885 -0.08872,-0.04195 -0.12967,-0.06931c-0.04095,-0.02736 -0.07883,-0.05845 -0.11365,-0.09327c-0.03482,-0.03482 -0.06591,-0.0727 -0.09327,-0.11365c-0.02736,-0.04095 -0.05046,-0.08417 -0.06931,-0.12967c-0.01885,-0.04549 -0.03307,-0.09239 -0.04268,-0.14069c-0.00961,-0.0483 -0.01441,-0.09707 -0.01441,-0.14632c0,-0.04925 0.0048,-0.09802 0.01441,-0.14632c0.00961,-0.0483 0.02383,-0.0952 0.04268,-0.14069c0.01885,-0.0455 0.04195,-0.08872 0.06931,-0.12967c0.02736,-0.04095 0.05845,-0.07883 0.09327,-0.11365z"></path><path  id="路径 4" style="fill:currentColor; opacity:1;" d="M5.53033,7.53033l-5,4.99997c-0.03482,0.03487 -0.07271,0.06597 -0.11365,0.0933c-0.04095,0.02733 -0.08417,0.05043 -0.12966,0.0693c-0.0455,0.01887 -0.0924,0.0331 -0.1407,0.0427c-0.0483,0.0096 -0.09707,0.0144 -0.14632,0.0144c-0.04925,0 -0.09802,-0.0048 -0.14632,-0.0144c-0.0483,-0.0096 -0.0952,-0.02383 -0.1407,-0.0427c-0.0455,-0.01887 -0.08872,-0.04197 -0.12966,-0.0693c-0.04095,-0.02733 -0.07883,-0.05843 -0.11365,-0.0933c-0.03482,-0.0348 -0.06591,-0.07267 -0.09327,-0.1136c-0.02736,-0.04093 -0.05046,-0.08417 -0.06931,-0.1297c-0.01885,-0.04547 -0.03307,-0.09237 -0.04268,-0.1407c-0.00961,-0.04827 -0.01441,-0.09703 -0.01441,-0.1463c0,-0.04927 0.0048,-0.09803 0.01441,-0.1463c0.00961,-0.04833 0.02383,-0.09523 0.04268,-0.1407c0.01885,-0.04553 0.04195,-0.08877 0.06931,-0.1297c0.02736,-0.04093 0.05845,-0.0788 0.09327,-0.1136l5,-5.00003c0.03482,-0.03482 0.0727,-0.06591 0.11365,-0.09327c0.04095,-0.02736 0.08417,-0.05046 0.12967,-0.06931c0.04549,-0.01885 0.09239,-0.03307 0.14069,-0.04268c0.0483,-0.00961 0.09707,-0.01441 0.14632,-0.01441c0.04925,0 0.09802,0.0048 0.14632,0.01441c0.0483,0.00961 0.0952,0.02383 0.14069,0.04268c0.0455,0.01885 0.08872,0.04195 0.12967,0.06931c0.04095,0.02736 0.07883,0.05845 0.11365,0.09327c0.03482,0.03482 0.06591,0.0727 0.09327,0.11365c0.02736,0.04095 0.05046,0.08417 0.06931,0.12967c0.01885,0.04549 0.03307,0.09239 0.04268,0.14069c0.00961,0.0483 0.01441,0.09707 0.01441,0.14632c0,0.04925 -0.0048,0.09802 -0.01441,0.14632c-0.00961,0.0483 -0.02383,0.0952 -0.04268,0.14069c-0.01885,0.0455 -0.04195,0.08872 -0.06931,0.12967c-0.02736,0.04095 -0.05845,0.07883 -0.09327,0.11365z"></path></g></svg>
  `,
  switch: `
  <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 16 16" fill="none"><g opacity="1"  transform="translate(0 0)  rotate(0)"><path id="分组 1" fill-rule="evenodd" style="fill:currentColor" opacity="1" d="M4.2959,3.17822c0.08,-0.08 0.2,-0.11 0.31,-0.07c0.1,0.04 0.17,0.14 0.17,0.25v1.47999h8.18c0.16,0 0.28,0.11 0.28,0.27v0.8c0,0.16 -0.12,0.27 -0.28,0.27h-10.88c-0.11,0 -0.21,-0.06 -0.26,-0.16c-0.04,-0.1 -0.02,-0.21 0.06,-0.29l0.87,-0.89zM13.1251,8.38905l-0.87,0.89l-1.55,1.65995c-0.08,0.08 -0.19,0.11 -0.3,0.07c-0.11,-0.04 -0.18,-0.14 -0.18,-0.24v-1.48995h-8.17996c-0.16,0 -0.28,-0.11 -0.28,-0.26v-0.81c0,-0.15 0.12,-0.27 0.28,-0.27h10.87996c0.11,0 0.21,0.06 0.26,0.16c0.04,0.1 0.02,0.21 -0.06,0.29z"></path></g></svg>
  `,
  attention: `
  <svg width="100%" height="100%" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_502_2)">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M10.97 6C10.97 3.26 8.74 1.03 6 1.03C3.26 1.03 1.03 3.26 1.03 6C1.03 8.74 3.26 10.97 6 10.97C8.74 10.97 10.97 8.74 10.97 6ZM6 12C2.69 12 0 9.31 0 6C0 2.69 2.69 0 6 0C9.31 0 12 2.69 12 6C12 9.31 9.31 12 6 12Z" fill="currentColor"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M6.52 3.00436V6.97437C6.52 7.26438 6.28 7.49438 6 7.49438C5.72 7.49438 5.48 7.26438 5.48 6.97437V3.00436C5.48 2.71437 5.72 2.48438 6 2.48438C6.28 2.48438 6.52 2.71437 6.52 3.00436ZM6 8.01438C6.41 8.01438 6.75 8.35437 6.75 8.76438C6.75 9.18437 6.41 9.51438 6 9.51438C5.59 9.51438 5.25 9.18437 5.25 8.76438C5.25 8.35437 5.59 8.01438 6 8.01438Z" fill="currentColor"/>
    </g>
    <defs>
      <clipPath id="clip0_502_2">
        <rect width="100%" height="100%" fill="white"/>
      </clipPath>
    </defs>
  </svg>
  `,
}
