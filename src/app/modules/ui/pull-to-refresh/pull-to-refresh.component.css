/* Pull to Refresh Component Styles */

:host {
  /* CSS Custom Properties for theming */
  --pull-refresh-head-height: 50px;
  --pull-refresh-head-font-size: 14px;
  --pull-refresh-text-color: var(--title-color);
  --pull-refresh-bg-color: transparent;
  --pull-refresh-success-color: var(--title-color);
  --pull-refresh-icon-size: 20px;
  --pull-refresh-transition-duration: 300ms;
  --pull-refresh-transition-easing: cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* Component dimensions */
  display: block;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* Main container */
.pull-refresh {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: var(--pull-refresh-bg-color);
  user-select: none;
  -webkit-user-select: none;
  touch-action: pan-y;
}

.pull-refresh--disabled {
  touch-action: auto;
}

/* Track container - moves with pull gesture */
.pull-refresh__track {
  position: relative;
  width: 100%;
  height: 100%;
  transition-property: transform;
  transition-timing-function: var(--pull-refresh-transition-easing);
  will-change: transform;
}

/* Header area */
.pull-refresh__head {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: var(--pull-refresh-head-height);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--pull-refresh-bg-color);
  color: var(--pull-refresh-text-color);
  font-size: var(--pull-refresh-head-font-size);
  overflow: hidden;
  z-index: 1;
  opacity: 0;
  transition: opacity var(--pull-refresh-transition-duration) var(--pull-refresh-transition-easing);
}

.pull-refresh__head--visible {
  opacity: 1;
}

/* Content area */
.pull-refresh__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 8px 16px;
  box-sizing: border-box;
}

/* Default indicator */
.pull-refresh__indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Icons */
.pull-refresh__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--pull-refresh-icon-size);
  height: var(--pull-refresh-icon-size);
  transition: transform var(--pull-refresh-transition-duration) var(--pull-refresh-transition-easing);
  will-change: transform;
}

.pull-refresh__icon--success {
  height: 1.5rem;
  color: var(--pull-refresh-success-color);
}

.pull-refresh__arrow {
  width: 100%;
  height: 100%;
  transition: inherit;
}

/* Success checkmark */
.pull-refresh__check {
  width: 100%;
  height: 100%;
  animation: pull-refresh-check-in 0.3s ease-out;
}

/* Text styles */
.pull-refresh__text {
  font-size: var(--pull-refresh-head-font-size);
  color: var(--pull-refresh-text-color);
  text-align: center;
  line-height: 1.2;
  transition: color var(--pull-refresh-transition-duration) var(--pull-refresh-transition-easing);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.pull-refresh__text--loading {
  font-weight: 500;
}

.pull-refresh__text--success {
  color: var(--pull-refresh-success-color);
  font-weight: 500;
}

/* Main content body */
.pull-refresh__body {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior-y: contain;
}

/* Animations */

@keyframes pull-refresh-check-in {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@media (prefers-reduced-motion: reduce) {
  .pull-refresh__track {
    transition: none;
  }

  .pull-refresh__head {
    transition: none;
  }

  .pull-refresh__icon {
    transition: none;
  }

  .pull-refresh__text {
    transition: none;
  }

  .pull-refresh__check {
    animation: none;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  :host {
    --pull-refresh-head-height: 60px;
    --pull-refresh-head-font-size: 16px;
    --pull-refresh-icon-size: 24px;
  }

  .pull-refresh__content {
    padding: 12px 20px;
  }

  .pull-refresh__text {
    max-width: 280px;
  }
}

/* Touch device optimizations */
@media (pointer: coarse) {
  .pull-refresh {
    -webkit-tap-highlight-color: transparent;
  }

  .pull-refresh__track {
    -webkit-touch-callout: none;
  }
}

/* Focus styles for accessibility */
.pull-refresh:focus-within .pull-refresh__head {
  outline: 2px solid var(--pull-refresh-progress-color);
  outline-offset: 2px;
}

/* Custom scrollbar for webkit browsers */
.pull-refresh__body::-webkit-scrollbar {
  width: 4px;
}

.pull-refresh__body::-webkit-scrollbar-track {
  background: transparent;
}

.pull-refresh__body::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.pull-refresh__body::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
