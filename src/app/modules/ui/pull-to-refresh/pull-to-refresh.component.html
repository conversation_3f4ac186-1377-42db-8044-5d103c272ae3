<div #root class="pull-refresh" [class.pull-refresh--disabled]="disabled()">
  <div
    #track
    class="pull-refresh__track"
    [style]="trackStyle()"
    [attr.aria-label]="statusText()"
    role="region"
    aria-live="polite"
  >
    <div #head class="pull-refresh__head" [style]="headStyle()" [class.pull-refresh__head--visible]="!isNormalStatus()">
      <div class="pull-refresh__content">
        @if (isPullingStatus()) {
          <ng-content select="[slot=pulling]"></ng-content>
        }
        @if (isLoosingStatus()) {
          <ng-content select="[slot=loosing]"></ng-content>
        }
        @if (isLoadingStatus()) {
          <ng-content select="[slot=loading]"></ng-content>
        }
        @if (isSuccessStatus()) {
          <ng-content select="[slot=success]"></ng-content>
        }

        <ng-content>
          <div class="pull-refresh__indicator">
            @switch (currentStatus()) {
              @case ('loading') {
                <memo-spinner></memo-spinner>
              }
              @case ('success') {
                <div class="pull-refresh__icon pull-refresh__icon--success">
                  <svg class="pull-refresh__check" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
                  </svg>
                </div>
              }

              @default {
                <memo-spinner [spinning]="false" [spinningProgress]="pullProgress()"></memo-spinner>
              }
            }

            @if (statusText()) {
              <span
                class="pull-refresh__text"
                [class.pull-refresh__text--loading]="isLoadingStatus()"
                [class.pull-refresh__text--success]="isSuccessStatus()"
              >
                {{ statusText() }}
              </span>
            }
          </div>
        </ng-content>
      </div>
    </div>

    <div #body class="pull-refresh__body">
      <ng-content></ng-content>
    </div>
  </div>
</div>
