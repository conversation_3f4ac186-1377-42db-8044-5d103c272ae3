import {
  Component,
  ElementRef,
  computed,
  effect,
  input,
  model,
  output,
  signal,
  viewChild,
  inject,
  DestroyRef,
} from '@angular/core'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { debounceTime, distinctUntilChanged, filter } from 'rxjs/operators'
import { useTouch } from '../../../hooks/use-touch'
import { useEventListener } from '../../../hooks/use-event-listener'
import { MemoSpinnerComponent } from '../spinner/spinner.component'

const DEFAULT_HEAD_HEIGHT = 50
const DEFAULT_PULL_DISTANCE = 50
const DEFAULT_SUCCESS_DURATION = 500
const DEFAULT_ANIMATION_DURATION = 300

type PullRefreshStatus = 'normal' | 'pulling' | 'loosing' | 'loading' | 'success' | 'error'

interface PullRefreshState {
  status: PullRefreshStatus
  distance: number
  duration: number
  progress: number
}

interface PullRefreshConfig {
  headHeight: number
  pullDistance: number
  successDuration: number
  animationDuration: number
  disabled: boolean
  damping: number
  threshold: number
}

@Component({
  selector: 'memo-pull-to-refresh',
  templateUrl: './pull-to-refresh.component.html',
  styleUrls: ['./pull-to-refresh.component.css'],
  standalone: true,
  imports: [MemoSpinnerComponent],
})
export class PullToRefreshComponent {
  readonly isRefreshing = model<boolean>(false)
  private destroyRef = inject(DestroyRef)

  readonly disabled = input<boolean>(false)
  readonly headHeight = input<number>(DEFAULT_HEAD_HEIGHT)
  readonly pullDistance = input<number>(DEFAULT_PULL_DISTANCE)
  readonly successDuration = input<number>(DEFAULT_SUCCESS_DURATION)
  readonly animationDuration = input<number>(DEFAULT_ANIMATION_DURATION)
  readonly damping = input<number>(0.6)
  readonly threshold = input<number>(5)

  readonly successText = input<string>('')
  readonly pullingText = input<string>('')
  readonly loosingText = input<string>('')
  readonly loadingText = input<string>('')

  readonly refresh = output()
  readonly statusChange = output<{ status: PullRefreshStatus; distance: number; progress: number }>()

  readonly root = viewChild<ElementRef<HTMLElement>>('root')
  readonly track = viewChild<ElementRef<HTMLElement>>('track')
  readonly head = viewChild<ElementRef<HTMLElement>>('head')
  readonly body = viewChild<ElementRef<HTMLElement>>('body')

  private readonly config = computed<PullRefreshConfig>(() => ({
    headHeight: this.headHeight(),
    pullDistance: this.pullDistance(),
    successDuration: this.successDuration(),
    animationDuration: this.animationDuration(),
    disabled: this.disabled(),
    damping: this.damping(),
    threshold: this.threshold(),
  }))

  // Core state management
  private readonly state = signal<PullRefreshState>({
    status: 'normal',
    distance: 0,
    duration: 0,
    progress: 0,
  })

  private readonly touch = useTouch()
  private readonly reachTop = signal<boolean>(false)
  private readonly isTouching = signal<boolean>(false)

  readonly currentStatus = computed(() => this.state().status)
  readonly pullProgress = computed(() => this.state().progress)
  readonly isNormalStatus = computed(() => this.state().status === 'normal')
  readonly isPullingStatus = computed(() => this.state().status === 'pulling')
  readonly isLoosingStatus = computed(() => this.state().status === 'loosing')
  readonly isLoadingStatus = computed(() => this.state().status === 'loading')
  readonly isSuccessStatus = computed(() => this.state().status === 'success')

  readonly statusText = computed(() => {
    const status = this.state().status
    switch (status) {
      case 'pulling':
        return this.pullingText()
      case 'loosing':
        return this.loosingText()
      case 'loading':
        return this.loadingText()
      case 'success':
        return this.successText()
      default:
        return ''
    }
  })

  readonly headStyle = computed(() => {
    const config = this.config()
    const progress = this.pullProgress()
    return {
      height: `${config.headHeight}px`,
      transform: `translateY(-${100 * progress + 20}%)`,
      zIndex: -1,
    }
  })

  readonly trackStyle = computed(() => {
    const { distance, duration } = this.state()
    return {
      transitionDuration: `${duration}ms`,
      transform: distance ? `translate3d(0, ${distance}px, 0)` : '',
    }
  })

  constructor() {
    this.setupEventListeners()
    this.setupRefreshingWatcher()
    this.setupStatusEmitter()
  }

  private setupEventListeners(): void {
    useEventListener(this.track, 'touchstart', this.onTouchStart.bind(this), {
      passive: true,
    })
    useEventListener(this.track, 'touchmove', this.onTouchMove.bind(this), {
      passive: false, // 需要能够调用 preventDefault
    })
    useEventListener(this.track, ['touchend', 'touchcancel'], this.onTouchEnd.bind(this))
  }

  private setupRefreshingWatcher(): void {
    effect(() => {
      const isRefreshing = this.isRefreshing()
      const config = this.config()

      if (isRefreshing) {
        this.setStatus('loading', config.headHeight)
      } else {
        const currentStatus = this.state().status
        if (currentStatus === 'loading') {
          this.showSuccessTip()
        }
      }
    })
  }

  private setupStatusEmitter(): void {
    toObservable(this.state)
      .pipe(
        debounceTime(16), // Throttle to 60fps
        distinctUntilChanged(),
        filter(() => !this.config().disabled),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(state => {
        this.statusChange.emit({
          status: state.status,
          distance: state.distance,
          progress: state.progress,
        })
      })
  }

  private readonly isTouchable = computed(() => {
    const status = this.state().status
    const disabled = this.config().disabled
    return !disabled && status !== 'loading' && status !== 'success'
  })

  private calculateDistance(deltaY: number): number {
    const config = this.config()
    const { pullDistance, damping } = config

    if (deltaY <= 0) return 0

    let distance = deltaY

    if (distance > pullDistance) {
      const excess = distance - pullDistance
      distance = pullDistance + excess * damping
    }

    return Math.round(Math.max(0, distance))
  }

  private calculateProgress(distance: number): number {
    const pullDistance = this.config().pullDistance
    return Math.min(distance / pullDistance, 1)
  }

  private setStatus(
    status: PullRefreshStatus,
    distance: number,
    duration?: number,
  ): void {
    const config = this.config()
    const progress = this.calculateProgress(distance)

    this.state.set({
      status,
      distance,
      progress,
      duration: duration ?? config.animationDuration,
    })
  }

  private showSuccessTip(): void {
    const config = this.config()
    this.setStatus('success', config.headHeight)

    setTimeout(() => {
      this.setStatus('normal', 0)
    }, config.successDuration)
  }

  private checkScrollPosition(event: TouchEvent): void {
    const bodyEl = this.body()?.nativeElement
    if (!bodyEl) return

    const isAtTop = bodyEl.scrollTop <= 1 // 允许 1px 的误差
    this.reachTop.set(isAtTop)

    if (isAtTop) {
      this.state.update(state => ({ ...state, duration: 0 }))
      this.touch.start(event)
    } else {
      // 如果不在顶部，确保重置状态
      if (this.state().status !== 'normal') {
        this.setStatus('normal', 0, 0)
      }
    }
  }

  private onTouchStart = (event: TouchEvent): void => {
    if (!this.isTouchable()) return

    // 首先检查是否在顶部
    this.checkScrollPosition(event)

    // 只有在顶部时才设置为正在触摸状态
    if (this.reachTop()) {
      this.isTouching.set(true)
    }
  }

  private onTouchMove = (event: TouchEvent): void => {
    if (!this.isTouchable() || !this.isTouching()) return

    this.touch.move(event)
    const { deltaY } = this.touch

    // 首先检查滚动位置
    const bodyEl = this.body()?.nativeElement
    if (!bodyEl) return

    const scrollTop = bodyEl.scrollTop
    const isAtTop = scrollTop <= 1
    this.reachTop.set(isAtTop)

    // 如果不在顶部，不处理下拉刷新
    if (!isAtTop) {
      // 确保重置状态
      if (this.state().status !== 'normal') {
        this.setStatus('normal', 0, 0)
      }
      return
    }

    // 只有在顶部且是向下拉的手势时才处理
    if (deltaY() >= this.config().threshold && this.touch.isVertical()) {
      // 阻止默认滚动行为
      event.preventDefault()

      const distance = this.calculateDistance(deltaY())
      const pullDistance = this.config().pullDistance

      const status: PullRefreshStatus = distance >= pullDistance ? 'loosing' : 'pulling'
      this.setStatus(status, distance, 0)
    } else if (deltaY() < 0) {
      // 如果是向上滑动，不阻止默认行为，让列表正常滚动
      return
    }
  }

  private onTouchEnd = (): void => {
    if (!this.isTouchable() || !this.isTouching()) return

    this.isTouching.set(false)

    // 只有在真正在顶部且有有效下拉手势时才处理
    if (this.reachTop() && this.touch.deltaY() > this.config().threshold) {
      const config = this.config()
      const currentStatus = this.state().status

      if (currentStatus === 'loosing') {
        this.setStatus('loading', config.headHeight)
        this.isRefreshing.set(true)
        this.refresh.emit()
      } else {
        this.setStatus('normal', 0)
      }
    } else {
      // 重置状态
      this.setStatus('normal', 0)
    }
  }

  public triggerRefresh(): void {
    if (this.config().disabled || this.state().status === 'loading') return

    const config = this.config()
    this.setStatus('loading', config.headHeight)
    this.isRefreshing.set(true)
    this.refresh.emit()
  }

  public completeRefresh(showSuccess = true): void {
    if (showSuccess && this.successText()) {
      this.showSuccessTip()
    } else {
      this.setStatus('normal', 0)
    }
    this.isRefreshing.set(false)
  }
}
