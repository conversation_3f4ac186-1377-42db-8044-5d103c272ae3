import { Injectable } from '@angular/core'
import { LoadingController } from '@ionic/angular'

@Injectable({
  providedIn: 'root',
})
export class MemoLoadingService {
  constructor(
    private loadingCtrl: LoadingController,
  ) {}

  async show() {
    const ctrl = await this.loadingCtrl.create({
      spinner: 'crescent',
      cssClass: 'memo-loading',
      duration: 10 * 1000,
    })

    ctrl.present()

    return ctrl
  }

  async dismiss(instance?: HTMLIonLoadingElement) {
    const target = instance ?? this.loadingCtrl
    try {
      await target.dismiss()
    } catch (error) {
      console.error(error)
    }
  }
}
