import { Ng<PERSON>ty<PERSON>, NgTemplateOutlet } from '@angular/common'
import { ChangeDetectionStrategy, Component, ElementRef, TemplateRef, computed, contentChild, effect, input, output, signal, viewChild } from '@angular/core'
import { injectVirtualizer } from '@tanstack/angular-virtual'
import { EmblaCarouselDirective, EmblaEventType, EmblaOptionsType, EmblaPluginType } from 'embla-carousel-angular'

@Component({
  selector: 'memo-flat-list',
  templateUrl: './flat-list.component.html',
  styleUrl: './flat-list.component.css',
  imports: [
    NgStyle,
    NgTemplateOutlet,
    EmblaCarouselDirective,
  ],
  host: {
    '[class.centered-slides]': 'centeredSlides()',
    '[attr.virtual-mode]': 'isVirtualMode()',
    '[attr.direction]': '!horizontal() ? "vertical" : "horizontal"',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FlatListComponent {
  private scrollElement = viewChild<ElementRef<HTMLElement>>('scroller')
  private emblaRef = viewChild(EmblaCarouselDirective)

  private _init = false

  itemStyle = input<Partial<CSSStyleDeclaration>>()
  isVirtualMode = input(true)
  horizontal = input(true)
  centeredSlides = input(false)
  data = input.required<unknown[]>()
  itemSize = input.required<number>()
  emblaOptions = input<EmblaOptionsType>({})
  emblaPlugins = input<EmblaPluginType[]>([])
  startIndex = input(0)
  containerStyle = input<Partial<CSSStyleDeclaration>>()

  afterInit = output()
  /**
   * 不可靠，目前只有传递 embla 的 event
   */
  internalChange = output<{ type: 'slidechange' }>()

  slideTemplate = contentChild('slide', {
    read: TemplateRef,
  })

  computedContainerStyle = computed(() => {
    const virtualizer = this.virtualizer
    if (!virtualizer) return {}
    const props = this.containerStyle()
    if (this.horizontal()) {
      return {
        width: virtualizer.getTotalSize() + 'px',
        height: this.itemSize() + 6 + 'px',
        ...props,
      }
    }
    return {
      width: '100%',
      height: virtualizer.getTotalSize() + 'px',
      ...props,
    }
  })

  virtualItemStyle = computed(() => {
    const virtualizer = this.virtualizer
    if (!virtualizer) return {}
    if (this.horizontal()) {
      return {
        height: 'fit-content',
        width: 'var(--item-size)',
        transform: 'translateX(var(--item-start))',
      }
    }

    return {
      width: '100%',
      height: 'var(--item-size)',
      transform: 'translateY(var(--item-start))',
    }
  })

  isNearStart = signal(false)
  isNearEnd = signal(false)

  virtualizer = injectVirtualizer(() => ({
    enabled: this.isVirtualMode(),
    horizontal: this.horizontal(),
    scrollElement: this.scrollElement(),
    count: this.data().length,
    estimateSize: index => this.itemSize(),
    overscan: 5,
    onChange: () => {
      if (this._init) {
        return
      }
      this.afterInit.emit()
      this._init = true
    },
  }))

  constructor() {
    const ref = effect(() => {
      const index = this.startIndex()
      const el = this.scrollElement()?.nativeElement
      if (this.isVirtualMode() && el && index !== 0) {
        setTimeout(() => {
          this.scrollToIndex(index, true)
        })
        ref.destroy()
      }
    })
  }

  scrollToIndex(index: number, jump = false) {
    if (this.isVirtualMode()) {
      const virtualizer = this.virtualizer
      if (!virtualizer) {
        throw new Error('Virtualizer not found')
      }
      virtualizer.scrollToIndex(index, {
        align: 'center',
        behavior: jump ? undefined : 'smooth',
      })

      return
    }

    const embla = this.emblaRef()
    if (!embla) {
      throw new Error('Embla Carousel not found')
    }
    embla.scrollTo(index, jump)
  }

  onEmblaChange(type: EmblaEventType) {
    if (this.isVirtualMode()) return

    if (type === 'init') {
      this.afterInit.emit()
      this._init = true
      return
    }

    if (type === 'select') {
      this.internalChange.emit({ type: 'slidechange' })
      this.isNearStart.set(this.getIsNearEdge('start'))
      this.isNearEnd.set(this.getIsNearEdge('end'))
      return
    }
  }

  sync(target: FlatListComponent) {
    target.internalChange.subscribe(({ type }) => {
      if (type !== 'slidechange' || this.getIsScrolling()) {
        return
      }

      const targetIndex = target.getCurrentIndex()
      if (targetIndex !== -1) {
        this.scrollToIndex(targetIndex)
      }
    })
  }

  /**
   * 目前只支持 embla-carousel
   * @returns number
   */
  getCurrentIndex() {
    if (this.isVirtualMode()) return -1
    return this.emblaRef()?.emblaApi?.selectedScrollSnap() ?? -1
  }

  getIsScrolling() {
    return this.isVirtualMode()
      ? this.virtualizer.isScrolling()
      : (this.emblaRef()?.emblaApi?.internalEngine().scrollBody.velocity() ?? 0) > 120
  }

  /**
   * 不传的话，满足其中一个条件就返回 true
   * @param edge
   */
  getIsNearEdge(edge: 'start' | 'end') {
    if (this.isVirtualMode()) {
      const items = this.virtualizer.getVirtualItems()
      if (items.length < 2) return false

      const offset = this.virtualizer.scrollOffset() ?? 0
      const isNearStart = offset < items[1].start
      if (edge === 'start' || (!edge && isNearStart)) return isNearStart

      const scrollRect = this.virtualizer.scrollRect()!
      const scrollSize = this.horizontal() ? scrollRect.width : scrollRect.height
      const isNearEnd = offset + scrollSize > items[items.length - 1].start
      return isNearEnd
    }

    const embla = this.emblaRef()?.emblaApi
    if (!embla) return false
    const targetIndex = embla.selectedScrollSnap()
    const slideCount = embla.slideNodes().length
    if (slideCount < 2) return false

    const isNearStart = targetIndex === 0
    if (edge === 'start' || (!edge && isNearStart)) return isNearStart

    const isNearEnd = targetIndex === slideCount - 1
    return isNearEnd
  }
}
