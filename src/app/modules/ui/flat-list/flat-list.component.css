:host {
  --embla-slide-size: auto;
  --embla-slide-flex: 0 0 var(--embla-slide-size);
  --embla-container-height: 100%;
  --embla-slides-gap: 0;

  display: block;
}

.flat-list__scroller {
  overflow: auto;
  overflow-y: hidden;
  display: block;
  position: relative;
}

.flat-list__container {
  display: flex;
  position: relative;
}

:host.centered-slides .flat-list__container {
  justify-content: center;
}

:host[direction='vertical'] {
  .flat-list__scroller {
    overflow-y: auto;
  }

  .flat-list__container {
    flex-direction: column;
  }
}

.flat-list__item {
  position: relative;
  flex: var(--embla-slide-flex);
  min-width: 0;

  &:not(:last-of-type) {
    margin-right: var(--embla-slides-gap);
  }
}

.flat-list__scroller[direction='vertical'] .flat-list__item {
  margin-right: 0;

  &:not(:last-of-type) {
    margin-bottom: var(--embla-slides-gap);
  }
}

.virtual__item {
  position: absolute;
  top: 0;
  left: 0;
}

:host[virtual-mode='true'] {
  .flat-list__scroller {
    height: var(--embla-container-height);
  }
}

:host[virtual-mode='false'] {
  .flat-list__container {
    height: var(--embla-container-height);
  }
}
