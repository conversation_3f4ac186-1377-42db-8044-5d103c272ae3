@if (isVirtualMode()) {
  <div class="flat-list__scroller scrollbar-none" #scroller>
    <div class="flat-list__container" [style]="computedContainerStyle()">
      @for (col of virtualizer.getVirtualItems(); track col.index) {
        <div
          class="flat-list__item virtual__item"
          [attr.data-index]="col.index"
          [class.list-item-even]="col.index % 2 === 0"
          [class.list-item-odd]="col.index % 2 !== 0"
          [style]="itemStyle()"
          [ngStyle]="virtualItemStyle()"
          [style.--item-size.px]="col.size"
          [style.--item-start.px]="col.start"
        >
          @if (slideTemplate(); as template) {
            <ng-container
              [ngTemplateOutlet]="template"
              [ngTemplateOutletContext]="{ index: col.index, item: data()[col.index] }"
            ></ng-container>
          }
        </div>
      }
    </div>
  </div>
} @else {
  <div
    class="flat-list__scroller scrollbar-none embla"
    #scroller
    emblaCarousel
    [options]="emblaOptions()"
    [plugins]="emblaPlugins()"
    [subscribeToEvents]="['init', 'select']"
    (emblaChange)="onEmblaChange($event)"
  >
    <div class="flat-list__container embla__container">
      @for (item of data(); track $index) {
        <div class="flat-list__item embla__slide" [style]="itemStyle()">
          @if (slideTemplate(); as template) {
            <ng-container
              [ngTemplateOutlet]="template"
              [ngTemplateOutletContext]="{ index: $index, item: item }"
            ></ng-container>
          }
        </div>
      }
    </div>
  </div>
}

<ng-template #render let-context>
  @if (slideTemplate(); as template) {
    <span>{{ context }}</span>
    <ng-container [ngTemplateOutlet]="template" [ngTemplateOutletContext]="context"></ng-container>
  }
</ng-template>
