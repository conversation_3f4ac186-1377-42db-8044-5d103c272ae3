import { DestroyRef, Directive, ElementRef, afterNextRender, inject, input, output } from '@angular/core'
import { outputFromObservable } from '@angular/core/rxjs-interop'
import { Gesture, GestureController } from '@ionic/angular'
import { debounceTime, fromEvent, throttleTime } from 'rxjs'

@Directive({
  selector: '[memoClick]',
  standalone: false,
})
export class MemoClickDirective {
  private lastOnStart = 0
  private DOUBLE_CLICK_THRESHOLD = 300
  private gesture?: Gesture

  debounceTime = input(0)
  throttleTime = input(300)

  protected _elementRef = inject<ElementRef<HTMLElement>>(ElementRef)
  protected gestureCtrl = inject(GestureController)
  protected destroyRef = inject(DestroyRef)

  memoClick = outputFromObservable(fromEvent<PointerEvent>(
    this._elementRef.nativeElement,
    'click',
  )
    .pipe(
      throttleTime(this.throttleTime()),
      debounceTime(this.debounceTime()),
    ))

  memoDbclick = output<HTMLElement>()

  constructor() {
    afterNextRender({
      write: () => {
        const gesture = this.gestureCtrl.create({
          gestureName: 'dbclick',
          el: this._elementRef.nativeElement,
          threshold: 0,
          onStart: () => {
            this.onStartDoubleClick()
          },
        })
        gesture.enable()
        this.gesture = gesture
      },
    })

    this.destroyRef.onDestroy(() => {
      this.gesture?.destroy()
    })
  }

  private onStartDoubleClick() {
    const now = Date.now()
    if (Math.abs(now - this.lastOnStart) <= this.DOUBLE_CLICK_THRESHOLD) {
      this.memoDbclick.emit(this._elementRef.nativeElement)
      this.lastOnStart = now
    } else {
      this.lastOnStart = now
    }
  }
}
