import { AnimationBuilder, animate, style } from '@angular/animations'
import { Injectable } from '@angular/core'

@Injectable({
  providedIn: 'root',
})
export class MemoTickleService {

  private tickleBuilder = this.builder.build([
    style({ transform: 'rotate(0) scale(1)', zIndex: 999 }),
    animate('70ms', style({ transform: 'rotate(5deg) scale(1.2)' })),
    animate('70ms', style({ transform: 'rotate(-5deg) scale(1.2)' })),
    animate('70ms', style({ transform: 'rotate(5deg)  scale(1.2)' })),
    animate('70ms', style({ transform: 'rotate(0) scale(1)' })),
  ],
  )

  constructor(
    protected builder: AnimationBuilder,
  ) { }

  tickle(element: HTMLElement, ...args: any) {
    this.tickleBuilder.create(element).play()
  }
}
