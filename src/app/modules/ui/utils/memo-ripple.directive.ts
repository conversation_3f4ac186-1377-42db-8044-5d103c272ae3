import { AnimationBuilder, AnimationPlayer, animate, style } from '@angular/animations'
import { Directive, ElementRef, inject, input, output, afterNextRender, DestroyRef } from '@angular/core'
import { Subject, filter, fromEvent, switchMap } from 'rxjs'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'

@Directive({
  selector: '[memoRipple]',
  standalone: false,
})
export class MemoRippleDirective {
  _elementRef = inject<ElementRef<HTMLElement>>(ElementRef)
  private animationBuilder = inject(AnimationBuilder)
  private destroyRef = inject(DestroyRef)

  private animationFactory = this.animationBuilder.build([
    style({ background: 'rgba(54, 181, 157, 0.2)' }),
    animate('1000ms', style({ background: 'transparent' })),
  ])

  private animationPlayer?: AnimationPlayer
  private triggerRipple$ = new Subject<void>()

  rippleEnabled = input(true)

  triggerElement = input<HTMLElement>()

  rippleStart = output()

  constructor() {
    this.triggerRipple$.pipe(
      takeUntilDestroyed(),
    )
      .subscribe(() => this.startAnimation())

    afterNextRender({
      write: () => {
        this.animationPlayer = this.animationFactory.create(this._elementRef.nativeElement)

        fromEvent(this._elementRef.nativeElement, 'click')
          .pipe(
            takeUntilDestroyed(this.destroyRef),
          )
          .subscribe(() => this.triggerRipple$.next())
      },
    })

    toObservable(this.triggerElement)
      .pipe(
        filter(Boolean),
        switchMap(el => fromEvent(el, 'click')),
        takeUntilDestroyed(),
      )
      .subscribe(() => {
        this.triggerRipple$.next()
      })
  }

  startAnimation(silent = false) {
    if (!this.rippleEnabled() || !this.animationPlayer) {
      return
    }
    this.animationPlayer.hasStarted() && this.animationPlayer.finish()
    this.animationPlayer.play()
    if (!silent) {
      this.rippleStart.emit()
    }
  }
}
