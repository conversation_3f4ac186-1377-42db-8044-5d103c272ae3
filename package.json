{"name": "team-events-frontend", "version": "1.0.0", "repository": {"type": "git", "url": "https://git.maimemo.com/maimemo/memo/activity-center-frontend.git"}, "engineStrict": true, "engines": {"node": ">= 18"}, "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4242 --proxy-config proxy.conf.prod.json", "lint": "echo \"Ignore <PERSON>\"", "build": "ng build --configuration production $EXTRA_BUILD_OPTS", "watch": "ng build --watch --configuration development", "test": "ng test", "e2e": "ng e2e", "build:stats": "ng build --stats-json", "prepare": "npx simple-git-hooks && npm run svg", "svg": "svg-generator"}, "private": true, "dependencies": {"@angular/animations": "^19.2.14", "@angular/cdk": "^19.2.18", "@angular/common": "^19.2.14", "@angular/compiler": "^19.2.14", "@angular/core": "^19.2.14", "@angular/forms": "^19.2.14", "@angular/platform-browser": "^19.2.14", "@angular/platform-browser-dynamic": "^19.2.14", "@angular/router": "^19.2.14", "@ionic/angular": "^6.7.5", "@juggle/resize-observer": "^3.4.0", "@maimemo/client-frontend-bridge": "^2.15.0", "@maimemo/link-preview": "^1.6.0", "@ngneat/svg-icon": "^8.0.0", "@primeuix/themes": "^1.2.1", "@rx-angular/cdk": "^19.1.0", "@rx-angular/template": "^19.2.2", "@sentry/angular": "^8.55.0", "@tanstack/angular-virtual": "^3.13.12", "big.js": "^7.0.1", "canvas-confetti": "^1.9.3", "comlink": "^4.4.1", "core-js": "^3.42.0", "dayjs": "^1.11.13", "dequal": "^2.0.3", "embla-carousel-angular": "^19.0.0", "lodash-es": "^4.17.21", "lottie-colorify": "^0.8.0", "lottie-web": "^5.12.2", "primeng": "^19.1.3", "rxjs": "~7.8.1", "swiper": "^8.4.7", "tslib": "^2.8.1", "web-animations-js": "^2.3.2", "zone.js": "~0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.14", "@angular/cli": "^19.2.14", "@angular/compiler-cli": "^19.2.14", "@maimemo/eslint-config": "3.0.0", "@ngneat/svg-generator": "^7.0.3", "@semantic-release/commit-analyzer": "^11.1.0", "@semantic-release/exec": "^6.0.3", "@semantic-release/gitlab": "^12.1.1", "@semantic-release/release-notes-generator": "^12.1.0", "@types/big.js": "^6.2.2", "@types/canvas-confetti": "^1.9.0", "@types/eslint": "^9.6.1", "@types/jasmine": "~3.10.0", "@types/lodash-es": "^4.17.12", "@types/node": "^20.17.19", "angular-eslint": "^19.7.1", "eslint": "^9.20.1", "jasmine-core": "~3.10.0", "lint-staged": "^15.4.3", "semantic-release": "^22.0.12", "simple-git-hooks": "^2.13.0", "svgo": "^3.3.2", "type-fest": "^4.41.0", "typescript": "~5.8.3"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"src/**/*.{html,ts,css,scss}": "eslint"}, "svgGenerator": {"outputPath": "./src/app/svg", "srcPath": "./src/assets/svg-icons", "rootBarrelFile": true, "svgoConfig": {"plugins": ["removeDimensions", "cleanupAttrs", {"name": "convertStyleToAttrs", "params": {"keepImportant": true}}, {"name": "convertColors", "params": {"currentColor": true}}]}}}