{"branches": ["master", "preview"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/gitlab", {"gitlabUrl": "https://git.maimemo.com", "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], ["@semantic-release/exec", {"analyzeCommitsCmd": "echo \"export const VERSION = '${lastRelease.version}'\" > src/version.ts && echo \"${lastRelease.version}\" > version", "verifyReleaseCmd": "echo \"export const VERSION = '${nextRelease.version}'\" > src/version.ts && echo \"${nextRelease.version}\" > version"}]]}