include:
  - project: maimemo/gitlab-ci
    file: frontend/template.yml

before_script:
  - |
    if [ $CI_COMMIT_BRANCH = "preview" ]; then
      export EXTRA_BUILD_OPTS=" --deploy-url https://static-cdn-dev.maimemo.com/pages/activity-center/";
      sed -i "s/deployUrl: ''/deployUrl: 'https:\/\/static-cdn-dev.maimemo.com\/pages\/activity-center\/'/g"  src/environments/environment.prod.ts || true;
    fi
  - |
    if [ $CI_COMMIT_BRANCH = "master" ]; then
      export EXTRA_BUILD_OPTS=" --deploy-url https://static-cdn.maimemo.com/pages/activity-center/";
      sed -i "s/deployUrl: ''/deployUrl: 'https:\/\/static-cdn.maimemo.com\/pages\/activity-center\/'/g"  src/environments/environment.prod.ts || true;
    fi

compile:
  image: registry-vpc.cn-hangzhou.aliyuncs.com/maimemo_library/node:$NODE_VERSION
  stage: compile
  tags:
    - serverless
  artifacts:
    when: on_success
    expire_in: 30 days
    paths:
      - dist
  cache:
    key: $CI_COMMIT_REF_NAME
    paths:
      - node_modules
  script:
    - npm config set @maimemo:registry https://git.maimemo.com/api/v4/packages/npm/
    - npm config set -- '//git.maimemo.com/api/v4/packages/npm/:_authToken' "${CI_JOB_TOKEN}"
    - npm config set registry $NPM_MIRROR
    - npm install --cache /opt/cache/npm --prefer-offline
    - npm run lint
    - if [[ $CI_COMMIT_BRANCH == "master" && $SENTRY_PROJECT ]] ; then
        npx semantic-release;
      fi
    - npm run build
    - if [[ $CI_COMMIT_BRANCH == "master" && $SENTRY_PROJECT ]] ; then
        mv version dist/;
      fi


variables:
  IMAGE_NAME: activity_center_frontend
  DEPLOY_NS: edge
  DEPLOY_NAME_DEV: activity-center-frontend-dev
  DEPLOY_NAME_PROD: activity-center-frontend-prod
  DEPLOY_NAME_PREVIEW: activity-center-frontend-dev
  SENTRY_PROJECT: activity-center-frontend
  SENTRY_SOURCE_MAP_PREFIX: "~/pages/activity-center"
  NODE_VERSION: 20
