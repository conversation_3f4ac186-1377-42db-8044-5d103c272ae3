{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}, "version": 1, "newProjectRoot": "projects", "projects": {"team-events-frontend": {"projectType": "application", "schematics": {"@schematics/angular:application": {"strict": true}, "@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/browser", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["core-js/modules/web.queue-microtask", "core-js/modules/es.array.flat", "core-js/modules/es.global-this", "core-js/modules/es.object.from-entries", "core-js/modules/es.string.replace-all", "lit/polyfill-support.js", "zone.js", "web-animations-js"], "tsConfig": "tsconfig.app.json", "sourceMap": {"styles": false}, "optimization": {"styles": {"inlineCritical": false}}, "buildOptimizer": true, "baseHref": "/pages/activity-center/", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/style/global.css", "src/style/index.scss", "./node_modules/swiper/swiper.min.css", "./node_modules/swiper/modules/grid/grid.min.css", "./node_modules/swiper/modules/virtual/virtual.min.css", "./node_modules/@ionic/angular/css/core.css", "./node_modules/@ionic/angular/css/flex-utils.css", "src/assets/css/flip.min.css"], "scripts": ["src/assets/js/flip.min.js"], "allowedCommonJsDependencies": ["@maimemo/client-frontend-bridge", "lottie-colorify", "lottie-web", "dayjs", "localforage"], "webWorkerTsConfig": "tsconfig.worker.json"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"options": {"headers": {"Cache-Control": "no-cache"}}, "builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "team-events-frontend:build:production"}, "development": {"buildTarget": "team-events-frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "team-events-frontend:build"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}}