server {
    listen       80;
    server_name  _;
    absolute_redirect off; 


    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /pages/activity-center/index.html;
        if ($request_filename ~ .*\.(htm|html)$)
           {
               add_header Cache-Control no-cache;
           }
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}

