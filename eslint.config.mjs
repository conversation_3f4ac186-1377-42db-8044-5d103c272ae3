import maimemo from '@maimemo/eslint-config'

/**
 * @type {import('eslint').Linter.Config[]}
 */
export default [
  ...await maimemo({
    angular: true,
    formatters: {
      scss: true,
    },
  }),
  {
    files: ['**/*.ts'],
    rules: {
      '@angular-eslint/directive-selector': [
        'error',
        {
          type: 'attribute',
          prefix: ['app', 'memo'],
          style: 'camelCase',
        },
      ],
      '@angular-eslint/component-selector': [
        'error',
        {
          type: 'element',
          prefix: ['app', 'memo'],
          style: 'kebab-case',
        },
      ],
      '@typescript-eslint/ban-ts-comment': 'off',
      '@typescript-eslint/no-extraneous-class': ['error', {
        allowEmpty: true,
      }],
    },
  },
  {
    files: ['**/*.html'],
    rules: {
      '@angular-eslint/template/no-distracting-elements': 'error',
      '@angular-eslint/template/no-duplicate-attributes': 'error',
      '@angular-eslint/template/conditional-complexity': [
        'warn',
        {
          maxComplexity: 3,
        },
      ],
    },
  },
]
